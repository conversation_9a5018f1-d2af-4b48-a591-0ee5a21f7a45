录音文件转写服务接口协议
2025-04-24

[toc]

功能介绍
录音文件转写提供将音频文件转写成文字的功能。

更多信息请参阅《语音识别服务使用手册》“基础知识”部分。

调用限制
录音文件时长大小在 10 小时以下，文件大小在 2 GB 以下。

交互流程
上传文件

请求文件转写接口：客户端向服务端发送带有待转写文件的 HTTP POST 方法的请求，服务端返回带有任务 ID 的HTTP 响应。
获取转写结果：提供轮询和回调方式获取转写结果

轮询：客户端向服务端发送带有请求文件转写接口返回的任务 ID 的 HTTP GET 方法的请求，服务端返回带有转写结果的 HTTP 响应。
回调：上传文件时传入回调地址，在转写任务完成时，系统自动将转写结果通过 HTTP POST 请求发送至指定的回调地址。
快速测试
Linux
请求文件转写接口

curl -X POST -F 'file=@test.mp3' -F 'lang_type=zh-cmn-Hans-CN' -F 'format=mp3' -F 'sample_rate=16000' 'http://localhost:7100/request'
获取转写结果接口

curl -X GET 'http://localhost:7100/getResult?task_id=******'
Windows
请求文件转写接口

curl.exe -X POST -F "file=@test.mp3" -F "lang_type=zh-cmn-Hans-CN" -F "format=mp3" -F "sample_rate=16000" "http://localhost:7100/request"
获取转写结果接口

curl.exe -X GET "http://localhost:7100/getResult?task_id=******"
请求地址
接口名	协议	URL	方法
请求文件转写	HTTP/1.1	http://{ip_address}:7100/request	POST
获取转写结果	HTTP/1.1	http://{ip_address}:7100/getResult	GET
查询任务	HTTP/1.1	http://{ip_address}:7100/getTasks	GET
删除任务	HTTP/1.1	http://{ip_address}:7100/deleteTask	POST
请求文件转写接口
请求行
POST /request
请求头
名称	类型	是否必需	说明
Content-type	String	是	必须为"multipart/form-data"
请求参数
客户端发送请求，其中在请求体（body）中需要进行参数设置，参数为 form-data 格式。各参数含义如下：

参数	类型	是否必需	说明	默认值
lang_type	String	是	待转写文件的语种，参见《语音识别服务使用手册》“语言支持”部分	必填
file	File	与 file_url 二选一	待转写的文件	无
file_url	String	与 file 二选一	待转写的文件URL，如果同时存在 file 参数则以 file 为准	无
format	String	是	待转写文件的格式，参见《语音识别服务使用手册》“音频编码”部分
注意： 用 16kHz 模型转写 8kHz 的 PCM 格式音频文件时，请传参数值 pcm_8000	必填
sample_rate	Integer	否	模型采样率（而不是音频采样率）
注意：16000 Hz 的模型也能转写 8000 Hz的音频	16000
output	String	否	text 为适用于文稿的格式，subtitle 为适用于字幕的格式，参见《语音识别服务使用手册》“实用功能”部分	text
channels	Integer	否	对音频进行转写的声道（通道）数，对音频文件的前 channels 个声道的音频分别进行转写，参数取值 ≥ 1，当channels ≥ 2 时，转写结果中用 cluster_id 做通道区分
当 channels = 1 时，将音频文件所有声道缩混为单声道，再进行转写；详见表格下方说明	1
enable_modal_particle_filter	Boolean	否	是否开启语气词过滤，参见《语音识别服务使用手册》“实用功能”部分	2.6.1及以上版本：true
2.6.0及以下版本：false
enable_punctuation_prediction	Boolean	否	是否添加标点	true（output=text）
false（output=subtitle）
enable_words	Boolean	否	是否开启返回词信息，参见《语音识别服务使用手册》“基本术语”部分	false
words_type	Integer	否	返回词信息的最小单位，仅对汉语有效
0表示以词为单位，1表示以字为单位	0
enable_inverse_text_normalization	Boolean	否	是否在后处理中执行ITN，参见《使用手册》“基本术语”部分	2.6.1及以上版本：true
2.6.0及以下版本：false
hotwords_id	String	否	热词ID，参见《语音识别服务使用手册》“实用功能”部分以及《语音识别服务热词接口协议》	无
hotwords_list	String	否	热词列表，仅在本次连接中生效，与hotwords_id参数同时存在时，以hotwords_list为准（2.5.12及以上版本支持）
多个热词之间用竖线 | 分隔	无
hotwords_weight	Float	否	热词权重，取值范围[0.1, 1.0]	0.4
correction_words_id	String	否	强制替换词库ID，参见《语音识别服务使用手册》“实用功能”部分以及《语音识别服务强制替换接口协议》
支持使用多个强制替换词库ID，用竖线 | 分隔每个ID；all 表示使用全部强制替换词库ID	无
forbidden_words_id	String	否	敏感词ID，参见《语音识别服务使用手册》“实用功能”部分以及《语音识别服务敏感词接口协议》
支持使用多个敏感词ID，用竖线 | 分隔每个敏感词ID；all 表示使用全部敏感词ID	无
keywords_quantity	Int	否	自动提取关键词的数量，取值范围[0, 100]	0
gain	Int	否	表示振幅增益系数，范围[1, 20]，参见《语音识别服务使用手册》“基础知识”及“实用功能”部分
1表示不放大，2表示为原振幅2倍（放大1倍），以此类推	1
callback_url	String	否	回调地址，如 http://10.0.0.2:8080	无
clusters	Int	否	说话人归档分类数量，范围[0~10]，表示根据声纹分为几类（几个说话人）
特殊地，1表示不做说话人归档处理；0表示由系统自动确定归档分类数量
当与 channels 参数同时存在且 channels > 1 时，将优先按照多通道音频转写的逻辑进行处理，不再进行说话人归档相关处理
此功能依赖声纹组件（module_vpr），与 vpr-file_url 参数不同时使用	1
vpr-file_url	String	否	传入说话人归档服务的轮询接口地址（含task_id），如 http://localhost:7800/getResult?task_id=******
说话人归档任务状态需为“已处理完成”（ status=00000 或 progress=100 ），否则将报错
当与 channels 参数同时存在且 channels > 1 时，将优先按照多通道音频转写的逻辑进行处理，不再进行说话人归档相关处理
此功能依赖说话人归档服务（vpr-file-integrated），与 clusters 参数不同时使用	无
max_sentence_silence	Integer	否	语音断句检测阈值，静音时长超过该阈值会被认为断句
取值范围[200, 5000]，单位：毫秒	450
enable_lang_label	Boolean	否	切换语种时在识别结果中返回语种代码，仅对英语混说（如中英混说、日英混说）语种生效（2.5.11及以上版本支持）	false
paragraph_condition	Integer	否	控制分段字符数，同一个 cluster_id 内达到设定字数时在下一句中返回新的段落编号，范围[100, 2000]，范围外的值表示不开启此功能（2.5.11及以上版本支持）	0
decode_silence_segment	Boolean	否	控制是否将VAD判定的静音段也做语音识别处理，适合远场录音环境（2.5.11及以上版本支持）	false
备注：

关于 channels 参数

当 channels = 1 时，将音频转为单通道音频再做转写

当 channels = 2 时，处理所有通道中的前两个通道，分别进行转写。开始处理时，剩余可用并发>=2时则占用两路并发，剩余可用并发=1时占一路并发。转写结果中用 cluster_id 做通道区分（取值为1和2）

当 channels = 3 时，处理所有通道中的前三个通道，分别进行转写……以此类推

当 channels > 音频实际的通道数时，channels 实际生效值为音频实际通道数。即，channels实际生效值 = min(channels参数值,音频实际通道数)

响应结果
所有接口响应的 HTTP Headers的 Content-Type 字段内容均为 application/json ，响应结果在 HTTP 的 Body 中。

响应结果字段如下：

名称	类型	描述
status	String	状态码
message	String	状态码描述
data	Dict	转写信息
├─ duration	Integer	音频时长（秒）
├─ task_id	String	此次转写的任务 ID，请记录该值，用于请求接口
成功响应
body中 status 字段为 00000 。

成功响应示例：

{
    "status": "00000",
    "message": "",
    "data": {
        "duration": 600,
        "task_id": "96e25f64-1727-4dbe-889c-4a9052c2eb28"
    }
}
失败响应
body中 status 字段不为 00000 ，均为失败响应，可将该字段作为响应是否成功的标志。

失败响应示例：

{
    "status": "20322",
    "message": "lang_type invalid",
    "data": null
}
获取转写结果接口（轮询）
请求行
GET /getResult
请求参数
客户端发送请求，query 参数含义如下：

参数	类型	是否必需	说明
task_id	String	是	任务ID，由请求文件转写接口返回
响应结果
所有接口响应的 HTTP Headers 的Content-Type 字段内容均为 application/json ，响应结果在 HTTP 的 Body 中。

响应结果字段如下：

名称	类型	描述
status	String	状态码
message	String	状态码描述
data	Dict	转写信息
statistics	Dict	统计信息
成功响应
当转写未完成时

body中 status 字段为 20302 。

body中 data 字段为此次转写任务的描述。

data 字段定义如下：

名称	类型	描述
desc	String	转写状态码描述
file_name	String	此次转写的文件名
insert_time	String	此次请求文件转写的时间
process_time	String	完成排队、实际开始转写的时间
progress	Integer	转写进度，负数 -n 表示排队中，前面还有n个转写任务；正数 n 表示正在转写，处理进度为百分之n；0 表示转写失败。
成功响应示例：

{
    "status": "20302",
    "message": "",
    "data": {
        "desc": "音频转写中",
        "file_name": "test.mp3",
        "insert_time": "2022-11-11 12:42:04",
        "progress": 92
    }
}
当转写完成时

body中 status 字段为 00000 。

body中 data[] 字段为此次转写任务的转写结果。

转写结果字段定义如下：

名称	类型	描述
lang_type	String	切换语种时在识别结果中返回语种代码，仅对英语混说（如中英混说、日英混说）语种生效（2.5.11及以上版本支持）
paragraph	Integer	段落编号，从1开始递增（2.5.11及以上版本支持）
begin	String	每片开始时间（相对于源音频文件）
end	String	每片结束时间（相对于源音频文件）
seg_num	Integer	片编号（从1开始递增）
transcript	String	每片转写结果
confidence	Float	当前结果的置信度，范围 [0, 1]
words	Dict[]	词信息（当设置词信息参数 enable_words = True 时返回）
cluster_id	Integer	归档（分类）编号，从1开始递增；cluster_id 相同的，表示是同一说话人
（当设置了有效的 clusters 或 vpr-file_url 参数时，或者设置 channels >=2时返回）
特殊地，0表示未知（无法分类的）说话人
volume	Integer	当前的音量，范围 [0, 100]
其中，词信息对象结构为：

参数	类型	说明
text	String	文本
start_time	Integer	词开始时间，单位为毫秒
end_time	Integer	词结束时间，单位为毫秒
type	String	类型
normal 表示普通文本，forbidden 表示敏感词，modal 表示语气词（仅当请求参数 words_type = 0 时会返回该类型），punc 表示标点符号
keywords_index	Integer	对应关键词序号（0表示非关键词），仅当请求参数 words_type = 0 时返回该字段
body中 statistics 字段为此次转写任务的统计信息：

参数	类型	说明
keywords	Dict[]	关键词对象（当设置 keywords_quantity 参数时返回）
speed	Integer	平均语速（汉语/日语/韩语：字/分钟；其他语种：词/分钟）
word_count	Integer	字数统计（汉语/日语/韩语：字数；其他语种：单词数）
insert_time	String	此次请求文件转写的时间
process_time	String	完成排队、实际开始转写的时间
finish_time	String	完成转写任务的时间
其中，关键词对象结构为：

参数	类型	说明
index	Integer	关键词序号（从1开始递增）
word	String	关键词
frequency	Integer	关键词出现频次
time	Dict[]	关键词出现时间段
├─ begin	String	关键词开始时间
├─ end	String	关键词结束时间
注意：

关键词 keywords 列表中的元素按频次和重要性综合打分排序返回（TF-IDF算法）。

如需仅按关键词出现频次排序，可使用 frequency 字段进行排序处理。

成功响应示例：

{
    "status": "00000",
    "message": "",
    "data": [
        {
            "begin": "00:00:01,170",
            "end": "00:00:04,610",
            "lang_type": "zh-cmn-Hans-CN",
            "paragraph": 1,
            "seg_num": 1,
            "transcript": "你好。请问你叫什么名字？",
            "confidence": 0.9,
            "volume": 76,
            "words": [
                {
                    "word": "你好",
                    "start_time": 80,
                    "end_time": 400,
                    "is_punc": false,
                    "keywords_index": 2,
                    "type": "normal"
                },
                {
                    "word": "。",
                    "start_time": 80,
                    "end_time": 400,
                    "is_punc": true,
                    "keywords_index": 0,
                    "type": "punc"
                },
                {
                    "word": "请问",
                    "start_time": 400,
                    "end_time": 860,
                    "is_punc": false,
                    "keywords_index": 0,
                    "type": "normal"
                },
                ……下略……
            ]    
        },
        {
            "begin": "00:00:04,630",
            "end": "00:00:07,010",
            "lang_type": "zh-cmn-Hans-CN",
            "paragraph": 1,
            "seg_num": 2,
            "transcript": "你好。我叫小明，你叫什么名字？",
            "confidence": 0.95,
            "volume": 82,
            "words": [
                ……下略……
            ]    
        }
    ],
    "statistics": {
        "finish_time": "2022-11-11 12:47:12",
        "insert_time": "2022-11-11 12:42:04",
        "process_time": "2022-11-11 12:46:28",
        "keywords": [
            {
                "index": 1,
                "words": "小明",
                "frequency": 2,
                "time": [
                    {
                        "begin": "00:00:05,120",
                        "end": "00:00:05,590"  
                    },
                    {
                        "begin": "00:00:13,420",
                        "end": "00:00:13,980"  
                    }
                ]
            },
            {
                "index": 2,
                "words": "你好",
                "frequency": 5,
                "time": [
                    ……下略……
                ]
            },
        ],
        "speed": 210,
        "word_count": 86
    }
}
失败响应
body中 status 字段不为 20302 或 00000 的，均为失败响应，可将该字段作为响应是否成功的标志。

失败响应示例：

{
    "status": "20326",
    "message": "task_id invalid",
    "data": null
}
回调识别结果
如果在调用请求文件转写接口时指定了 callback_url 参数，则系统将在转写完成（或转写失败）时以 HTTP POST 请求的形式自动将识别结果发送至指定地址。

响应结果字段如下：

名称	类型	描述
status	String	状态码
message	String	状态码描述
data	Dict	转写信息
转写成功
转写结果 data 字段定义如下：

名称	类型	描述
insert_time	String	创建转写任务的时间（该参数已在 V2.5.6 版本弃用，请改用 statistics 中的 insert_time 字段）
result	Dict[]	转写结果，与轮询接口 data 字段格式相同
statistics	Dict	统计信息，与轮询接口 statistics 字段格式相同
result_time	String	返回转写结果的时间（该参数已在 V2.5.6 版本弃用，请改用 statistics 中的 finish_time 字段）
task_id	String	此次转写的任务 ID
{
    "status":"00000",
    "message":"success",
    "data":{
        "result":[
            {
                "begin": "00:00:01,170",
                "end": "00:00:04,610",
                "lang_type": "zh-cmn-Hans-CN",
                "paragraph": 1,
                "seg_num": 1,
                "transcript": "你好。请问你叫什么名字？",
                "confidence": 0.9,
                "volume": 82
            },
            {
                "begin": "00:00:04,630",
                "end": "00:00:7,010",
                "lang_type": "zh-cmn-Hans-CN",
                "paragraph": 1,
                "seg_num": 2,
                "transcript": "你好。我叫小明，你叫什么名字？",
                "confidence": 0.95,
                "volume": 76
        	}
        ],
        "statistics": {
            "keywords":[],
            "speed": 210,
            "word_count": 86,
            "finish_time": "2022-11-11 12:47:12",
            "insert_time": "2022-11-11 12:42:04",
            "process_time": "2022-11-11 12:46:28",
        },
        "task_id":"27dc2487-bb02-4585-bd10-b75b445441bc"
    }
}
转写失败
字段格式同上，而 result 字段的值为空。任务失败的具体信息请参考 message 字段。

查询任务接口
根据条件筛选任务。按配置文件过期策略清除的任务将不会返回。

请求行
GET /getTasks
请求参数
客户端发送请求，query 参数含义如下：

参数	说明	默认值
time	按上传时间筛选条件，n小时内上传的音频	不限时间
type	按类型筛选，0：失败，1：完成，2：处理中，3：排队中	不限类型
count	按数量筛选，返回的任务数量上限	不限数量
以上筛选条件组合使用时为“与（AND）”的关系。

响应参数
所有接口响应的 HTTP Headers 的Content-Type 字段内容均为 application/json ，响应结果在 HTTP 的 Body 中。

响应结果字段如下：

参数	类型	说明
status	String	状态码
message	String	状态码描述
data	Dict[]	任务信息
├─ task_id	String	任务ID
├─ desc	String	转写状态描述
├─ file_name	String	转写的文件名
├─ insert_time	String	请求文件转写的时间
├─ process_time	String	完成排队、实际开始转写的时间
├─ finish_time	String	完成转写任务的时间
├─ progress	Integer	转写进度。负数 -n 表示排队中，前面还有n个转写任务；
正数 n 表示正在转写，处理进度为百分之n；0 表示转写失败；100表示转写完成。
statistics	Dict	统计信息
├─ count	Integer	查询结果任务总数
├─ types	Dict[]	各类数量汇总
├─├─ type	Integer	0：失败，1：完成，2：处理中，3：排队中
├─├─ desc	String	转写状态描述
├─├─ count	Integer	任务数量
删除任务接口
删除队列中的任务或转写完成的任务。对于转写处理中的任务，将在预处理阶段完成后删除。

请求行
POST /deleteTask
请求参数
客户端发送请求，query 参数含义如下：

参数	类型	是否必需	说明
task_id	String	是	任务ID，由请求文件转写接口返回
响应结果
所有接口响应的 HTTP Headers 的 Content-Type 字段内容均为 application/json ，响应结果在 HTTP 的 Body 中。

响应结果字段如下：

名称	类型	描述
status	String	状态码
message	String	状态码描述
data	Dict	null
服务状态码
注意：

从 V2.6.0 版本开始，状态码分类进一步细化，具体变化对照请参考 V2.6.X 版本状态码更新对照表 。

服务的每一次响应都会返回 status 、 message 字段，表示此次返回的状态码、状态码描述，服务状态码如下：

状态码	原因
20302	getResult接口成功返回转写进度
10001	参数解析错误（2.5.12 及以下版本）
20001	参数解析错误（2.6.0 及以上版本）
20002、20003、20006	文件处理失败
20111	WebSocket 升级失败
20114	请求体为空或获取音频文件失败
20115	语音长度或大小超限
20116	不支持的音频采样率
20190	参数缺失
20191	参数无效
20192	处理失败（解码器层）
20193	处理失败（服务层）
20194	连接超时（长时间未接收到客户端数据）
20195	其他错误
20330	说话人归档（角色区分）相关错误
更新时间：2025-05-07