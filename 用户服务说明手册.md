# ASR流式语音识别服务用户手册

## 服务简介

ASR流式语音识别服务是一个基于WebSocket的实时语音识别系统，支持多语种音频流的实时转写。服务采用先进的深度学习模型，提供高精度、低延迟的语音识别能力。

### 主要特性
- ✅ **实时识别**: 支持音频流实时转写，延迟低于500ms
- ✅ **多语种支持**: 支持中文、英文、俄语、哈萨克语、柯尔克孜语、维吾尔语
- ✅ **自动语种检测**: 智能识别音频语种，无需手动指定
- ✅ **高并发**: 支持多客户端同时连接
- ✅ **断句功能**: 智能断句，提供完整句子结果

## 快速开始

### 1. 服务地址
```
WebSocket地址: ws://服务器IP:端口/ws/{客户端ID}
默认端口: 10080
```

### 2. 音频要求
| 参数 | 要求 | 说明 |
|------|------|------|
| 采样率 | 16000Hz (推荐) | 支持8000Hz、44100Hz |
| 位深度 | 16bit | 固定要求 |
| 声道数 | 单声道 | 固定要求 |
| 格式 | PCM | Base64编码传输 |
| 包大小 | 12800字节 | 约400ms音频 |

### 3. 基本使用流程
1. 建立WebSocket连接
2. 发送音频数据包
3. 接收识别结果
4. 关闭连接

## 接口协议

### WebSocket连接

#### 连接地址
```
ws://服务器IP:端口/ws/{client_id}
```

**参数说明:**
- `client_id`: 客户端唯一标识符，建议使用UUID或时间戳

#### 连接示例
```javascript
const websocket = new WebSocket('ws://localhost:10080/ws/client_001');
```

### 消息格式

#### 请求消息格式
客户端发送的音频数据包格式：

```json
{
    "index": 0,
    "audio_data": "base64编码的音频数据",
    "sample_rate": 16000,
    "is_final": false
}
```

**字段说明:**
| 字段 | 类型 | 必填 | 说明 |
|------|------|------|------|
| index | int | ✅ | 数据包序号，从0开始递增 |
| audio_data | string | ✅ | Base64编码的PCM音频数据 |
| sample_rate | int | ✅ | 音频采样率 |
| is_final | boolean | ✅ | 是否为最后一个数据包 |

#### 响应消息格式
服务端返回的识别结果格式：

```json
{
    "code": 0,
    "state": "success",
    "index": 0,
    "result": "识别的文本内容",
    "voice_id": "client_001",
    "message_id": "client_001_uuid",
    "timestamp": 1703123456789,
    "final": 0
}
```

**字段说明:**
| 字段 | 类型 | 说明 |
|------|------|------|
| code | int | 状态码，0表示成功 |
| state | string | 状态描述 |
| index | int | 对应的请求包序号 |
| result | string | 识别结果文本 |
| voice_id | string | 客户端ID |
| message_id | string | 消息唯一标识 |
| timestamp | long | 时间戳(毫秒) |
| final | int | 是否最终结果(0:中间结果, 1:最终结果) |

## 使用示例

### Python客户端示例

```python
import asyncio
import websockets
import json
import base64
import soundfile as sf

async def send_audio():
    uri = "ws://localhost:10080/ws/test_client"
    
    async with websockets.connect(uri) as websocket:
        # 读取音频文件
        audio_data, sample_rate = sf.read("test.wav")
        
        # 转换为PCM格式
        pcm_data = (audio_data * 32767).astype('int16').tobytes()
        
        # 分包发送
        packet_size = 12800  # 400ms音频包
        packets = [pcm_data[i:i+packet_size] 
                  for i in range(0, len(pcm_data), packet_size)]
        
        # 启动接收任务
        receive_task = asyncio.create_task(receive_results(websocket))
        
        # 发送音频包
        for i, packet in enumerate(packets):
            message = {
                "index": i,
                "audio_data": base64.b64encode(packet).decode('utf-8'),
                "sample_rate": sample_rate,
                "is_final": i == len(packets) - 1
            }
            await websocket.send(json.dumps(message))
            await asyncio.sleep(0.4)  # 模拟实时发送
        
        # 等待接收完成
        await receive_task

async def receive_results(websocket):
    try:
        while True:
            response = await websocket.recv()
            result = json.loads(response)
            
            if result['code'] == 0:
                print(f"识别结果: {result['result']}")
                if result['final'] == 1:
                    print("识别完成")
                    break
            else:
                print(f"错误: {result['result']}")
                break
    except websockets.exceptions.ConnectionClosed:
        print("连接已关闭")

# 运行示例
asyncio.run(send_audio())
```

### JavaScript客户端示例

```javascript
class ASRClient {
    constructor(serverUrl, clientId) {
        this.serverUrl = serverUrl;
        this.clientId = clientId;
        this.websocket = null;
        this.packetIndex = 0;
    }
    
    connect() {
        return new Promise((resolve, reject) => {
            const url = `${this.serverUrl}/ws/${this.clientId}`;
            this.websocket = new WebSocket(url);
            
            this.websocket.onopen = () => {
                console.log('连接已建立');
                resolve();
            };
            
            this.websocket.onmessage = (event) => {
                const result = JSON.parse(event.data);
                this.handleResult(result);
            };
            
            this.websocket.onerror = (error) => {
                console.error('WebSocket错误:', error);
                reject(error);
            };
            
            this.websocket.onclose = () => {
                console.log('连接已关闭');
            };
        });
    }
    
    sendAudio(audioData, sampleRate, isFinal = false) {
        const message = {
            index: this.packetIndex++,
            audio_data: audioData,
            sample_rate: sampleRate,
            is_final: isFinal
        };
        
        this.websocket.send(JSON.stringify(message));
    }
    
    handleResult(result) {
        if (result.code === 0) {
            console.log(`识别结果: ${result.result}`);
            if (result.final === 1) {
                console.log('识别完成');
            }
        } else {
            console.error(`错误: ${result.result}`);
        }
    }
    
    close() {
        if (this.websocket) {
            this.websocket.close();
        }
    }
}

// 使用示例
const client = new ASRClient('ws://localhost:10080', 'web_client_001');
client.connect().then(() => {
    // 发送音频数据
    // client.sendAudio(base64AudioData, 16000, false);
});
```

## 错误处理

### 常见错误码

| 错误码 | 说明 | 解决方案 |
|--------|------|----------|
| 4002 | 音频格式错误 | 检查音频格式是否为PCM |
| 4003 | 采样率不支持 | 使用支持的采样率(8000/16000/44100Hz) |
| 4004 | 数据包大小错误 | 检查音频包大小是否正确 |
| 4005 | 缺少必需字段 | 检查请求消息是否包含所有必需字段 |
| 4007 | 数据包超时 | 检查网络连接，确保及时发送数据包 |
| 4010 | 心跳超时 | 检查连接状态，重新建立连接 |
| 5001 | 解码错误 | 检查音频数据是否正确 |
| 5002 | 模型加载错误 | 联系技术支持 |

### 错误处理示例

```python
async def handle_response(websocket):
    try:
        while True:
            response = await websocket.recv()
            result = json.loads(response)
            
            if result['code'] != 0:
                # 处理错误
                error_code = result['code']
                error_message = result['result']
                
                if error_code == 4003:
                    print("采样率不支持，请使用16000Hz")
                elif error_code == 4007:
                    print("数据包超时，请检查网络连接")
                else:
                    print(f"未知错误: {error_code} - {error_message}")
                
                break
            else:
                # 处理正常结果
                print(f"识别结果: {result['result']}")
                
    except websockets.exceptions.ConnectionClosed:
        print("连接意外关闭")
    except json.JSONDecodeError:
        print("响应格式错误")
```

## 最佳实践

### 1. 连接管理
- 使用唯一的客户端ID避免冲突
- 实现连接重试机制
- 正确处理连接关闭事件

### 2. 音频处理
- 确保音频格式符合要求
- 控制发送速率模拟实时流
- 正确设置最后一包标志

### 3. 结果处理
- 区分中间结果和最终结果
- 实现错误重试机制
- 记录识别日志便于调试

### 4. 性能优化
- 复用WebSocket连接
- 合理设置音频包大小
- 避免频繁建立连接

## 技术支持

### 获取帮助
- 查看服务状态: `GET http://服务器IP:端口/api/transmission-rules`
- 健康检查: `GET http://服务器IP:8081/health`
- 技术文档: 参考部署说明手册

### 常见问题
1. **Q: 为什么识别结果不准确？**
   A: 检查音频质量、采样率设置，确保音频清晰无噪音。

2. **Q: 连接经常断开怎么办？**
   A: 检查网络稳定性，实现心跳保活机制。

3. **Q: 支持哪些音频格式？**
   A: 目前支持PCM格式，采样率16000Hz，16bit，单声道。

4. **Q: 如何提高识别速度？**
   A: 使用推荐的音频参数，避免过大的音频包。

## 高级功能

### 多语种识别
服务支持自动语种检测，无需手动指定语种：

```json
{
    "index": 0,
    "audio_data": "base64编码的音频数据",
    "sample_rate": 16000,
    "is_final": false,
    "lang_code": "auto"
}
```

**支持的语种:**
- `zh`: 中文（普通话）
- `en`: 英文
- `ru`: 俄语
- `kk`: 哈萨克语
- `kkin`: 柯尔克孜语
- `ug`: 维吾尔语

### 自定义分隔符
可以自定义识别结果的分隔符：

```json
{
    "index": 0,
    "audio_data": "base64编码的音频数据",
    "sample_rate": 16000,
    "is_final": false,
    "custom_separator": "。"
}
```

### 实时率控制
建议按照400ms间隔发送音频包，模拟真实录音：

```python
import time

for i, packet in enumerate(packets):
    # 发送数据包
    await websocket.send(json.dumps(message))

    # 等待400ms模拟实时录音
    if i < len(packets) - 1:  # 最后一包不需要等待
        time.sleep(0.4)
```

## 服务监控

### 传输规则查询
获取服务器音频传输要求：

```bash
curl -X GET "http://localhost:10080/api/transmission-rules"
```

**响应示例:**
```json
{
    "audio_format": {
        "sample_rate": 16000,
        "sample_width": 2,
        "channels": 1,
        "encoding": "PCM",
        "data_encoding": "base64"
    },
    "packet_format": {
        "max_packet_size": 12800,
        "packet_interval_ms": 400,
        "timeout_seconds": 6
    },
    "protocol": {
        "websocket_endpoint": "/ws/{client_id}",
        "heartbeat_interval": 30,
        "supported_sample_rates": [44100, 16000, 8000]
    }
}
```

### 健康检查
检查服务运行状态：

```bash
curl -X GET "http://localhost:8081/health"
```

**响应示例:**
```json
{
    "status": "healthy",
    "timestamp": "2024-01-01T12:00:00Z",
    "memory_usage": 45.2,
    "cpu_usage": 12.8,
    "active_connections": 3,
    "uptime_seconds": 86400
}
```

## SDK和工具

### 音频处理工具
推荐使用以下工具处理音频：

**Python:**
```python
import soundfile as sf
import numpy as np

# 读取音频文件
audio, sr = sf.read('input.wav')

# 转换采样率到16kHz
if sr != 16000:
    import librosa
    audio = librosa.resample(audio, orig_sr=sr, target_sr=16000)

# 转换为PCM格式
pcm_data = (audio * 32767).astype(np.int16).tobytes()
```

**JavaScript (Web):**
```javascript
// 获取麦克风音频流
navigator.mediaDevices.getUserMedia({ audio: true })
    .then(stream => {
        const audioContext = new AudioContext({ sampleRate: 16000 });
        const source = audioContext.createMediaStreamSource(stream);

        // 处理音频数据...
    });
```

### 测试工具
项目提供了测试客户端 `client.py`，可用于测试服务：

```bash
python client.py --server localhost:10080 --audio test.wav
```

## 部署建议

### 客户端部署
1. **网络要求**: 确保与服务器网络连通
2. **音频设备**: 使用高质量麦克风设备
3. **采样设置**: 推荐使用16kHz采样率录音
4. **缓冲管理**: 实现音频缓冲避免丢包

### 集成建议
1. **错误重试**: 实现指数退避重试机制
2. **连接池**: 对于高频使用场景，考虑连接复用
3. **日志记录**: 记录请求和响应便于问题排查
4. **监控告警**: 监控识别准确率和响应时间

## 许可证和限制

### 使用限制
- 单个连接最大音频时长: 37小时
- 并发连接数: 根据服务器配置
- 音频包大小: 固定12800字节
- 心跳超时: 30秒

### 服务等级
- **可用性**: 99.9%
- **响应时间**: < 500ms
- **并发支持**: 10+ 连接
- **语种支持**: 6种语言

---

**版本信息**: v1.0
**更新时间**: 2024-01-01
**技术支持**: 请联系系统管理员
