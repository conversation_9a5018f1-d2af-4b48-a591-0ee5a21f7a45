2025-07-11 10:00:55.780 | INFO  | modules.config:init_logger :593 - 日志系统初始化成功, 配置: {'level': 'DEBUG', 'rotation': '1 day', 'retention': '7 days', 'compression': 'zip', 'max_file_size': '100 MB'}
2025-07-11 10:00:55.794 | INFO  | modules.monitoring:_start_health_server:308 - 健康检查服务器已启动, 端口: 8081
2025-07-11 10:00:55.795 | INFO  | modules.monitoring:__init__    :176 - 系统监控已启用, 内存检查间隔: 60秒
2025-07-11 10:00:55.795 | DEBUG | modules.config:init_monitoring:624 - 监控系统初始化成功
2025-07-11 10:00:55.795 | DEBUG | modules.config:init_session_pool:649 - 会话池初始化成功
2025-07-11 10:00:55.796 | DEBUG | modules.config:init_all_modules:667 - 所有模块初始化完成
2025-07-11 10:00:55.796 | INFO  | modules.asr_manager:__init__    :39 - 初始化统一ASR管理器, 支持语种: ['zh', 'en', 'ru', 'kk', 'kkin', 'ug']
2025-07-11 10:00:55.797 | INFO  | modules.asr_manager:load_models :59 - 单语种模式：加载语种 zh
2025-07-11 10:00:55.798 | DEBUG | modules.decoder:load_onnx   :62 - 加载模型: /ws/MODELS/online_onnx_zh, fp16: False, quant: False, device: cpu
2025-07-11 10:00:55.798 | DEBUG | modules.decoder:get_ep_list :58 - ['CPUExecutionProvider']
2025-07-11 10:00:56.288 | DEBUG | modules.decoder:load_onnx   :103 - ONNX Session配置: inter_op_threads=0, intra_op_threads=0, execution_mode=PARALLEL
2025-07-11 10:00:57.265 | DEBUG | modules.onnx_session_pool:register_model:153 - 模型 zh_encoder 注册成功，预创建会话: bb192bff
2025-07-11 10:00:57.265 | DEBUG | modules.decoder:load_onnx   :62 - 加载模型: /ws/MODELS/online_onnx_zh, fp16: False, quant: False, device: cpu
2025-07-11 10:00:57.265 | DEBUG | modules.decoder:get_ep_list :58 - ['CPUExecutionProvider']
2025-07-11 10:00:57.271 | DEBUG | modules.decoder:load_onnx   :103 - ONNX Session配置: inter_op_threads=0, intra_op_threads=0, execution_mode=PARALLEL
2025-07-11 10:00:57.288 | DEBUG | modules.onnx_session_pool:register_model:153 - 模型 zh_ctc 注册成功，预创建会话: 5ef3fa15
2025-07-11 10:00:57.289 | DEBUG | modules.asr_manager:_load_single_language:104 - 语种 zh 模型已注册到会话池
2025-07-11 10:00:57.297 | DEBUG | modules.config:build_model_config:205 - 构建的模型配置: {'feat_configs': {'feat_type': 'fbank', 'num_mel_bins': 80, 'frame_length': 25, 'frame_shift': 10, 'dither': 1.0, 'n_fft': 400, 'hop_length': 160}, 'batch': 1, 'chunk_size': 16, 'left_chunks': 16, 'reverse_weight': 0.3, 'decoding_window': 67, 'subsampling_rate': 4, 'right_context': 6, 'context_list_path': '/ws/MODELS/online_onnx_zh/hotwords.txt', 'context_graph_score': 40, 'output_size': 512, 'num_blocks': 12, 'cnn_module_kernel': 15, 'head': 8, 'feature_size': 80, 'vocab_size': 5538}
2025-07-11 10:00:57.297 | DEBUG | modules.symbol_table:load_dict   :30 - 加载词表: /ws/MODELS/online_onnx_zh/units.txt
2025-07-11 10:00:57.304 | DEBUG | modules.symbol_table:__init__    :24 - 启用后处理: 转全小写字母
2025-07-11 10:00:57.305 | INFO  | modules.asr_manager:load_models :72 - 模型加载完成：成功 1/1 个语种
2025-07-11 10:00:57.305 | INFO  | server :lifespan    :109 - 单语种模式 LID功能不可用
2025-07-11 10:00:57.305 | INFO  | modules.connect:__init__    :87 - 启用会话池动态扩容, 最大会话数: 4
2025-07-11 10:00:57.306 | INFO  | server :lifespan    :114 - Server start, init manager, LID_MANAGER, ASR_MANAGER


2025-07-11 10:01:02.033 | DEBUG | server :websocket_endpoint:231 - client_id:000 - 开始初始化连接
2025-07-11 10:01:02.034 | INFO  | server :websocket_endpoint:237 - client_id:000 - >>> [请求] 新客户连接，当前活跃连接数: 1
2025-07-11 10:01:02.036 | DEBUG | server :websocket_endpoint:231 - client_id:111 - 开始初始化连接
2025-07-11 10:01:02.036 | INFO  | server :websocket_endpoint:237 - client_id:111 - >>> [请求] 新客户连接，当前活跃连接数: 2
2025-07-11 10:01:02.446 | INFO  | modules.connect:on_check    :468 - client_id:000 - 设置自定义分隔符: "，"
2025-07-11 10:01:02.447 | INFO  | modules.connect:_init_decoder:141 - client_id:000 - 初始化解码器, 使用默认语种: zh
2025-07-11 10:01:02.480 | DEBUG | modules.decoder:__init__    :427 - 自定义分隔符: ，
2025-07-11 10:01:02.481 | INFO  | modules.connect:_init_decoder:148 - client_id:000 - 解码器初始化完成, 使用分隔符: "，"
2025-07-11 10:01:02.499 | DEBUG | modules.connect:on_check    :626 - client_id:000 - >>> [解析] 第0个数据包, 累计帧数: 38
2025-07-11 10:01:02.499 | DEBUG | modules.connect:on_decode   :992 - client_id:000 - 所需帧数: 67, 目前帧数: 38, 继续等待数据包
2025-07-11 10:01:02.500 | INFO  | modules.connect:on_check    :468 - client_id:111 - 设置自定义分隔符: "，"
2025-07-11 10:01:02.500 | INFO  | modules.connect:_init_decoder:141 - client_id:111 - 初始化解码器, 使用默认语种: zh
2025-07-11 10:01:02.502 | DEBUG | modules.decoder:__init__    :427 - 自定义分隔符: ，
2025-07-11 10:01:02.502 | INFO  | modules.connect:_init_decoder:148 - client_id:111 - 解码器初始化完成, 使用分隔符: "，"
2025-07-11 10:01:02.506 | DEBUG | modules.connect:on_check    :626 - client_id:111 - >>> [解析] 第0个数据包, 累计帧数: 38
2025-07-11 10:01:02.507 | DEBUG | modules.connect:on_decode   :992 - client_id:111 - 所需帧数: 67, 目前帧数: 38, 继续等待数据包
2025-07-11 10:01:02.861 | DEBUG | modules.connect:on_check    :626 - client_id:000 - >>> [解析] 第1个数据包, 累计帧数: 76
2025-07-11 10:01:02.862 | DEBUG | modules.connect:on_decode   :959 - client_id:000 - 所需帧数: 67, 目前帧数: 76, 开始解码
2025-07-11 10:01:02.865 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 000 获取现有会话: zh_encoder - bb192bff, 池大小: 1
2025-07-11 10:01:02.894 | DEBUG | modules.connect:on_check    :626 - client_id:111 - >>> [解析] 第1个数据包, 累计帧数: 76
2025-07-11 10:01:02.894 | DEBUG | modules.connect:on_decode   :959 - client_id:111 - 所需帧数: 67, 目前帧数: 76, 开始解码
2025-07-11 10:01:02.899 | DEBUG | modules.onnx_session_pool:get_session :195 - 客户 111 模型 zh_encoder 无可用会话，立即创建新会话
2025-07-11 10:01:02.899 | DEBUG | modules.decoder:load_onnx   :62 - 加载模型: /ws/MODELS/online_onnx_zh, fp16: False, quant: False, device: cpu
2025-07-11 10:01:02.899 | DEBUG | modules.decoder:get_ep_list :58 - ['CPUExecutionProvider']
2025-07-11 10:01:03.425 | DEBUG | modules.decoder:load_onnx   :103 - ONNX Session配置: inter_op_threads=0, intra_op_threads=0, execution_mode=PARALLEL
2025-07-11 10:01:04.388 | DEBUG | modules.onnx_session_pool:get_session :209 - 客户 111 为模型 zh_encoder 立即创建新会话 b4b3bb92, 池大小: 2
2025-07-11 10:01:04.389 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_encoder - bb192bff, 当前可用: 1/2
2025-07-11 10:01:04.391 | DEBUG | modules.decoder:decode_chunk:483 - client_id:000 - 当前推理累计时点: 16
2025-07-11 10:01:04.393 | DEBUG | modules.decoder:decode_chunk:484 - client_id:000 - ctc 搜索起始时点: 0 搜索片段长度: tensor([16])
2025-07-11 10:01:04.394 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 000 获取现有会话: zh_ctc - 5ef3fa15, 池大小: 1
2025-07-11 10:01:04.411 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_ctc - 5ef3fa15, 当前可用: 1/1
2025-07-11 10:01:04.443 | DEBUG | modules.decoder:search      :371 - ctc token&time: [(1522, 12)]
2025-07-11 10:01:04.444 | DEBUG | modules.decoder:decode_chunk:495 - ctc result: "很"
2025-07-11 10:01:04.444 | DEBUG | modules.connect:on_result   :428 - client_id:000 - <<< [发送] 第0个数据包, 更新识别结果: "很"
2025-07-11 10:01:04.452 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_encoder - b4b3bb92, 当前可用: 2/2
2025-07-11 10:01:04.452 | DEBUG | modules.decoder:decode_chunk:483 - client_id:111 - 当前推理累计时点: 16
2025-07-11 10:01:04.453 | DEBUG | modules.decoder:decode_chunk:484 - client_id:111 - ctc 搜索起始时点: 0 搜索片段长度: tensor([16])
2025-07-11 10:01:04.453 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 111 获取现有会话: zh_ctc - 5ef3fa15, 池大小: 1
2025-07-11 10:01:04.454 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_ctc - 5ef3fa15, 当前可用: 1/1
2025-07-11 10:01:04.463 | DEBUG | modules.connect:on_check    :626 - client_id:000 - >>> [解析] 第2个数据包, 累计帧数: 114
2025-07-11 10:01:04.464 | DEBUG | modules.connect:on_decode   :959 - client_id:000 - 所需帧数: 67, 目前帧数: 114, 开始解码
2025-07-11 10:01:04.466 | DEBUG | modules.connect:on_decode   :988 - client_id:000 - chunk 完成解码, 识别结果不变
2025-07-11 10:01:04.477 | DEBUG | modules.decoder:search      :371 - ctc token&time: [(1720, 7), (1664, 11)]
2025-07-11 10:01:04.477 | DEBUG | modules.decoder:decode_chunk:495 - ctc result: "我想"
2025-07-11 10:01:04.478 | DEBUG | modules.connect:on_check    :626 - client_id:000 - >>> [解析] 第3个数据包, 累计帧数: 152
2025-07-11 10:01:04.478 | DEBUG | modules.connect:on_decode   :959 - client_id:000 - 所需帧数: 67, 目前帧数: 152, 开始解码
2025-07-11 10:01:04.479 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 000 获取现有会话: zh_encoder - bb192bff, 池大小: 2
2025-07-11 10:01:04.479 | DEBUG | modules.connect:on_result   :428 - client_id:111 - <<< [发送] 第0个数据包, 更新识别结果: "我想"
2025-07-11 10:01:04.515 | DEBUG | modules.connect:on_check    :626 - client_id:111 - >>> [解析] 第2个数据包, 累计帧数: 114
2025-07-11 10:01:04.515 | DEBUG | modules.connect:on_decode   :959 - client_id:111 - 所需帧数: 67, 目前帧数: 114, 开始解码
2025-07-11 10:01:04.516 | DEBUG | modules.connect:on_decode   :988 - client_id:111 - chunk 完成解码, 识别结果不变
2025-07-11 10:01:04.519 | DEBUG | modules.connect:on_check    :626 - client_id:111 - >>> [解析] 第3个数据包, 累计帧数: 152
2025-07-11 10:01:04.519 | DEBUG | modules.connect:on_decode   :959 - client_id:111 - 所需帧数: 67, 目前帧数: 152, 开始解码
2025-07-11 10:01:04.519 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 111 获取现有会话: zh_encoder - b4b3bb92, 池大小: 2
2025-07-11 10:01:04.553 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_encoder - bb192bff, 当前可用: 1/2
2025-07-11 10:01:04.554 | DEBUG | modules.decoder:decode_chunk:483 - client_id:000 - 当前推理累计时点: 32
2025-07-11 10:01:04.554 | DEBUG | modules.decoder:decode_chunk:484 - client_id:000 - ctc 搜索起始时点: 16 搜索片段长度: tensor([16])
2025-07-11 10:01:04.554 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 000 获取现有会话: zh_ctc - 5ef3fa15, 池大小: 1
2025-07-11 10:01:04.555 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_ctc - 5ef3fa15, 当前可用: 1/1
2025-07-11 10:01:04.574 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_encoder - b4b3bb92, 当前可用: 2/2
2025-07-11 10:01:04.575 | DEBUG | modules.decoder:decode_chunk:483 - client_id:111 - 当前推理累计时点: 32
2025-07-11 10:01:04.575 | DEBUG | modules.decoder:decode_chunk:484 - client_id:111 - ctc 搜索起始时点: 16 搜索片段长度: tensor([16])
2025-07-11 10:01:04.575 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 111 获取现有会话: zh_ctc - 5ef3fa15, 池大小: 1
2025-07-11 10:01:04.576 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_ctc - 5ef3fa15, 当前可用: 1/1
2025-07-11 10:01:04.769 | DEBUG | modules.decoder:search      :371 - ctc token&time: [(1720, 7), (1664, 11), (928, 17), (3010, 22), (1586, 27)]
2025-07-11 10:01:04.772 | DEBUG | modules.decoder:search      :371 - ctc token&time: [(1522, 12), (5380, 17), (364, 22), (2181, 26)]
2025-07-11 10:01:04.773 | DEBUG | modules.decoder:decode_chunk:495 - ctc result: "我想在理性"
2025-07-11 10:01:04.773 | DEBUG | modules.decoder:decode_chunk:495 - ctc result: "很高兴来"
2025-07-11 10:01:04.775 | DEBUG | modules.connect:on_result   :428 - client_id:111 - <<< [发送] 第1个数据包, 更新识别结果: "我想在理性"
2025-07-11 10:01:04.776 | DEBUG | modules.connect:on_result   :428 - client_id:000 - <<< [发送] 第1个数据包, 更新识别结果: "很高兴来"
2025-07-11 10:01:04.783 | DEBUG | modules.connect:on_check    :626 - client_id:111 - >>> [解析] 第4个数据包, 累计帧数: 190
2025-07-11 10:01:04.783 | DEBUG | modules.connect:on_decode   :959 - client_id:111 - 所需帧数: 67, 目前帧数: 190, 开始解码
2025-07-11 10:01:04.789 | DEBUG | modules.connect:on_check    :626 - client_id:000 - >>> [解析] 第4个数据包, 累计帧数: 190
2025-07-11 10:01:04.789 | DEBUG | modules.connect:on_decode   :959 - client_id:000 - 所需帧数: 67, 目前帧数: 190, 开始解码
2025-07-11 10:01:04.790 | DEBUG | modules.connect:on_decode   :988 - client_id:111 - chunk 完成解码, 识别结果不变
2025-07-11 10:01:04.791 | DEBUG | modules.connect:on_decode   :988 - client_id:000 - chunk 完成解码, 识别结果不变
2025-07-11 10:01:04.796 | DEBUG | modules.connect:on_check    :626 - client_id:111 - >>> [解析] 第5个数据包, 累计帧数: 228
2025-07-11 10:01:04.796 | DEBUG | modules.connect:on_decode   :959 - client_id:111 - 所需帧数: 67, 目前帧数: 228, 开始解码
2025-07-11 10:01:04.798 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 111 获取现有会话: zh_encoder - bb192bff, 池大小: 2
2025-07-11 10:01:04.803 | DEBUG | modules.connect:on_check    :626 - client_id:000 - >>> [解析] 第5个数据包, 累计帧数: 228
2025-07-11 10:01:04.803 | DEBUG | modules.connect:on_decode   :959 - client_id:000 - 所需帧数: 67, 目前帧数: 228, 开始解码
2025-07-11 10:01:04.804 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 000 获取现有会话: zh_encoder - b4b3bb92, 池大小: 2
2025-07-11 10:01:04.858 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_encoder - bb192bff, 当前可用: 1/2
2025-07-11 10:01:04.859 | DEBUG | modules.decoder:decode_chunk:483 - client_id:111 - 当前推理累计时点: 48
2025-07-11 10:01:04.859 | DEBUG | modules.decoder:decode_chunk:484 - client_id:111 - ctc 搜索起始时点: 32 搜索片段长度: tensor([16])
2025-07-11 10:01:04.859 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 111 获取现有会话: zh_ctc - 5ef3fa15, 池大小: 1
2025-07-11 10:01:04.860 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_ctc - 5ef3fa15, 当前可用: 1/1
2025-07-11 10:01:04.894 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_encoder - b4b3bb92, 当前可用: 2/2
2025-07-11 10:01:04.897 | DEBUG | modules.decoder:decode_chunk:483 - client_id:000 - 当前推理累计时点: 48
2025-07-11 10:01:04.899 | DEBUG | modules.decoder:decode_chunk:484 - client_id:000 - ctc 搜索起始时点: 32 搜索片段长度: tensor([16])
2025-07-11 10:01:04.899 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 000 获取现有会话: zh_ctc - 5ef3fa15, 池大小: 1
2025-07-11 10:01:04.900 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_ctc - 5ef3fa15, 当前可用: 1/1
2025-07-11 10:01:04.958 | DEBUG | modules.decoder:search      :371 - ctc token&time: [(1720, 7), (1664, 11), (928, 17), (3010, 22), (1586, 27), (753, 43)]
2025-07-11 10:01:04.959 | DEBUG | modules.decoder:decode_chunk:495 - ctc result: "我想在理性，哲"
2025-07-11 10:01:04.962 | DEBUG | modules.connect:on_result   :428 - client_id:111 - <<< [发送] 第2个数据包, 更新识别结果: "我想在理性，哲"
2025-07-11 10:01:05.000 | DEBUG | modules.decoder:search      :371 - ctc token&time: [(1522, 12), (5380, 17), (364, 22), (2181, 26), (448, 32), (4765, 35), (4935, 40)]
2025-07-11 10:01:05.001 | DEBUG | modules.decoder:decode_chunk:495 - ctc result: "很高兴来到这里"
2025-07-11 10:01:05.004 | DEBUG | modules.connect:on_check    :626 - client_id:111 - >>> [解析] 第6个数据包, 累计帧数: 266
2025-07-11 10:01:05.004 | DEBUG | modules.connect:on_decode   :959 - client_id:111 - 所需帧数: 67, 目前帧数: 266, 开始解码
2025-07-11 10:01:05.006 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 111 获取现有会话: zh_encoder - bb192bff, 池大小: 2
2025-07-11 10:01:05.006 | DEBUG | modules.connect:on_result   :428 - client_id:000 - <<< [发送] 第2个数据包, 更新识别结果: "很高兴来到这里"
2025-07-11 10:01:05.014 | DEBUG | modules.connect:on_check    :626 - client_id:000 - >>> [解析] 第6个数据包, 累计帧数: 266
2025-07-11 10:01:05.015 | DEBUG | modules.connect:on_decode   :959 - client_id:000 - 所需帧数: 67, 目前帧数: 266, 开始解码
2025-07-11 10:01:05.016 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 000 获取现有会话: zh_encoder - b4b3bb92, 池大小: 2
2025-07-11 10:01:05.075 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_encoder - bb192bff, 当前可用: 1/2
2025-07-11 10:01:05.075 | DEBUG | modules.decoder:decode_chunk:483 - client_id:111 - 当前推理累计时点: 64
2025-07-11 10:01:05.076 | DEBUG | modules.decoder:decode_chunk:484 - client_id:111 - ctc 搜索起始时点: 48 搜索片段长度: tensor([16])
2025-07-11 10:01:05.076 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 111 获取现有会话: zh_ctc - 5ef3fa15, 池大小: 1
2025-07-11 10:01:05.077 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_ctc - 5ef3fa15, 当前可用: 1/1
2025-07-11 10:01:05.083 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_encoder - b4b3bb92, 当前可用: 2/2
2025-07-11 10:01:05.084 | DEBUG | modules.decoder:decode_chunk:483 - client_id:000 - 当前推理累计时点: 64
2025-07-11 10:01:05.086 | DEBUG | modules.decoder:decode_chunk:484 - client_id:000 - ctc 搜索起始时点: 48 搜索片段长度: tensor([16])
2025-07-11 10:01:05.087 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 000 获取现有会话: zh_ctc - 5ef3fa15, 池大小: 1
2025-07-11 10:01:05.089 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_ctc - 5ef3fa15, 当前可用: 1/1
2025-07-11 10:01:05.205 | DEBUG | modules.decoder:search      :371 - ctc token&time: [(1720, 7), (1664, 11), (928, 17), (3010, 22), (1586, 27), (753, 43), (1199, 49)]
2025-07-11 10:01:05.209 | DEBUG | modules.decoder:decode_chunk:495 - ctc result: "我想在理性，哲学"
2025-07-11 10:01:05.214 | DEBUG | modules.connect:on_result   :428 - client_id:111 - <<< [发送] 第3个数据包, 更新识别结果: "我想在理性，哲学"
2025-07-11 10:01:05.248 | DEBUG | modules.decoder:search      :371 - ctc token&time: [(1522, 12), (5380, 17), (364, 22), (2181, 26), (448, 32), (4765, 35), (4935, 40), (39, 57), (1041, 62)]
2025-07-11 10:01:05.248 | DEBUG | modules.decoder:decode_chunk:495 - ctc result: "很高兴来到这里，与大"
2025-07-11 10:01:05.249 | DEBUG | modules.connect:on_result   :428 - client_id:000 - <<< [发送] 第3个数据包, 更新识别结果: "很高兴来到这里，与大"
2025-07-11 10:01:05.298 | DEBUG | modules.connect:on_check    :626 - client_id:000 - >>> [解析] 第7个数据包, 累计帧数: 304
2025-07-11 10:01:05.298 | DEBUG | modules.connect:on_decode   :959 - client_id:000 - 所需帧数: 67, 目前帧数: 304, 开始解码
2025-07-11 10:01:05.300 | DEBUG | modules.connect:on_decode   :988 - client_id:000 - chunk 完成解码, 识别结果不变
2025-07-11 10:01:05.306 | DEBUG | modules.connect:on_check    :626 - client_id:111 - >>> [解析] 第7个数据包, 累计帧数: 304
2025-07-11 10:01:05.306 | DEBUG | modules.connect:on_decode   :959 - client_id:111 - 所需帧数: 67, 目前帧数: 304, 开始解码
2025-07-11 10:01:05.307 | DEBUG | modules.connect:on_decode   :988 - client_id:111 - chunk 完成解码, 识别结果不变
2025-07-11 10:01:05.705 | DEBUG | modules.connect:on_check    :626 - client_id:000 - >>> [解析] 第8个数据包, 累计帧数: 342
2025-07-11 10:01:05.706 | DEBUG | modules.connect:on_decode   :959 - client_id:000 - 所需帧数: 67, 目前帧数: 342, 开始解码
2025-07-11 10:01:05.707 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 000 获取现有会话: zh_encoder - bb192bff, 池大小: 2
2025-07-11 10:01:05.715 | DEBUG | modules.connect:on_check    :626 - client_id:111 - >>> [解析] 第8个数据包, 累计帧数: 342
2025-07-11 10:01:05.715 | DEBUG | modules.connect:on_decode   :959 - client_id:111 - 所需帧数: 67, 目前帧数: 342, 开始解码
2025-07-11 10:01:05.716 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 111 获取现有会话: zh_encoder - b4b3bb92, 池大小: 2
2025-07-11 10:01:05.777 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_encoder - bb192bff, 当前可用: 1/2
2025-07-11 10:01:05.777 | DEBUG | modules.decoder:decode_chunk:483 - client_id:000 - 当前推理累计时点: 80
2025-07-11 10:01:05.778 | DEBUG | modules.decoder:decode_chunk:484 - client_id:000 - ctc 搜索起始时点: 64 搜索片段长度: tensor([16])
2025-07-11 10:01:05.778 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 000 获取现有会话: zh_ctc - 5ef3fa15, 池大小: 1
2025-07-11 10:01:05.779 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_ctc - 5ef3fa15, 当前可用: 1/1
2025-07-11 10:01:05.794 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_encoder - b4b3bb92, 当前可用: 2/2
2025-07-11 10:01:05.795 | DEBUG | modules.decoder:decode_chunk:483 - client_id:111 - 当前推理累计时点: 80
2025-07-11 10:01:05.796 | DEBUG | modules.decoder:decode_chunk:484 - client_id:111 - ctc 搜索起始时点: 64 搜索片段长度: tensor([16])
2025-07-11 10:01:05.797 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 111 获取现有会话: zh_ctc - 5ef3fa15, 池大小: 1
2025-07-11 10:01:05.798 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_ctc - 5ef3fa15, 当前可用: 1/1
2025-07-11 10:01:05.839 | DEBUG | modules.decoder:search      :371 - ctc token&time: [(1522, 12), (5380, 17), (364, 22), (2181, 26), (448, 32), (4765, 35), (4935, 40), (39, 57), (1041, 62), (1241, 66), (1899, 71), (4416, 76)]
2025-07-11 10:01:05.841 | DEBUG | modules.decoder:decode_chunk:495 - ctc result: "很高兴来到这里，与大家探讨"
2025-07-11 10:01:05.842 | DEBUG | modules.connect:on_result   :428 - client_id:000 - <<< [发送] 第4个数据包, 更新识别结果: "很高兴来到这里，与大家探讨"
2025-07-11 10:01:05.870 | DEBUG | modules.decoder:search      :371 - ctc token&time: [(1720, 7), (1664, 11), (928, 17), (3010, 22), (1586, 27), (753, 43), (1199, 49), (157, 64), (589, 68), (1542, 73), (3010, 77)]
2025-07-11 10:01:05.870 | DEBUG | modules.decoder:decode_chunk:495 - ctc result: "我想在理性，哲学，以及心理"
2025-07-11 10:01:05.870 | DEBUG | modules.connect:on_result   :428 - client_id:111 - <<< [发送] 第4个数据包, 更新识别结果: "我想在理性，哲学，以及心理"
2025-07-11 10:01:06.113 | DEBUG | modules.connect:on_check    :626 - client_id:000 - >>> [解析] 第9个数据包, 累计帧数: 380
2025-07-11 10:01:06.114 | DEBUG | modules.connect:on_decode   :959 - client_id:000 - 所需帧数: 67, 目前帧数: 380, 开始解码
2025-07-11 10:01:06.116 | DEBUG | modules.connect:on_decode   :988 - client_id:000 - chunk 完成解码, 识别结果不变
2025-07-11 10:01:06.121 | DEBUG | modules.connect:on_check    :626 - client_id:111 - >>> [解析] 第9个数据包, 累计帧数: 380
2025-07-11 10:01:06.122 | DEBUG | modules.connect:on_decode   :959 - client_id:111 - 所需帧数: 67, 目前帧数: 380, 开始解码
2025-07-11 10:01:06.122 | DEBUG | modules.connect:on_decode   :988 - client_id:111 - chunk 完成解码, 识别结果不变
2025-07-11 10:01:06.520 | DEBUG | modules.connect:on_check    :626 - client_id:000 - >>> [解析] 第10个数据包, 累计帧数: 418
2025-07-11 10:01:06.521 | DEBUG | modules.connect:on_decode   :959 - client_id:000 - 所需帧数: 67, 目前帧数: 418, 开始解码
2025-07-11 10:01:06.522 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 000 获取现有会话: zh_encoder - bb192bff, 池大小: 2
2025-07-11 10:01:06.530 | DEBUG | modules.connect:on_check    :626 - client_id:111 - >>> [解析] 第10个数据包, 累计帧数: 418
2025-07-11 10:01:06.531 | DEBUG | modules.connect:on_decode   :959 - client_id:111 - 所需帧数: 67, 目前帧数: 418, 开始解码
2025-07-11 10:01:06.532 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 111 获取现有会话: zh_encoder - b4b3bb92, 池大小: 2
2025-07-11 10:01:06.582 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_encoder - bb192bff, 当前可用: 1/2
2025-07-11 10:01:06.583 | DEBUG | modules.decoder:decode_chunk:483 - client_id:000 - 当前推理累计时点: 96
2025-07-11 10:01:06.584 | DEBUG | modules.decoder:decode_chunk:484 - client_id:000 - ctc 搜索起始时点: 80 搜索片段长度: tensor([16])
2025-07-11 10:01:06.584 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 000 获取现有会话: zh_ctc - 5ef3fa15, 池大小: 1
2025-07-11 10:01:06.584 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_ctc - 5ef3fa15, 当前可用: 1/1
2025-07-11 10:01:06.608 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_encoder - b4b3bb92, 当前可用: 2/2
2025-07-11 10:01:06.610 | DEBUG | modules.decoder:decode_chunk:483 - client_id:111 - 当前推理累计时点: 96
2025-07-11 10:01:06.612 | DEBUG | modules.decoder:decode_chunk:484 - client_id:111 - ctc 搜索起始时点: 80 搜索片段长度: tensor([16])
2025-07-11 10:01:06.612 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 111 获取现有会话: zh_ctc - 5ef3fa15, 池大小: 1
2025-07-11 10:01:06.614 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_ctc - 5ef3fa15, 当前可用: 1/1
2025-07-11 10:01:06.680 | DEBUG | modules.decoder:search      :371 - ctc token&time: [(1522, 12), (5380, 17), (364, 22), (2181, 26), (448, 32), (4765, 35), (4935, 40), (39, 57), (1041, 62), (1241, 66), (1899, 71), (4416, 76), (1720, 81), (4765, 85), (67, 90)]
2025-07-11 10:01:06.682 | DEBUG | modules.decoder:decode_chunk:495 - ctc result: "很高兴来到这里，与大家探讨我这为"
2025-07-11 10:01:06.684 | DEBUG | modules.connect:on_result   :428 - client_id:000 - <<< [发送] 第5个数据包, 更新识别结果: "很高兴来到这里，与大家探讨我这为"
2025-07-11 10:01:06.707 | DEBUG | modules.decoder:search      :371 - ctc token&time: [(1720, 7), (1664, 11), (928, 17), (3010, 22), (1586, 27), (753, 43), (1199, 49), (157, 64), (589, 68), (1542, 73), (3010, 77), (1199, 81), (3185, 85), (1299, 89), (5214, 94)]
2025-07-11 10:01:06.707 | DEBUG | modules.decoder:decode_chunk:495 - ctc result: "我想在理性，哲学，以及心理学的层面"
2025-07-11 10:01:06.707 | DEBUG | modules.connect:on_result   :428 - client_id:111 - <<< [发送] 第5个数据包, 更新识别结果: "我想在理性，哲学，以及心理学的层面"
2025-07-11 10:01:06.929 | DEBUG | modules.connect:on_check    :626 - client_id:000 - >>> [解析] 第11个数据包, 累计帧数: 456
2025-07-11 10:01:06.930 | DEBUG | modules.connect:on_decode   :959 - client_id:000 - 所需帧数: 67, 目前帧数: 456, 开始解码
2025-07-11 10:01:06.931 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 000 获取现有会话: zh_encoder - bb192bff, 池大小: 2
2025-07-11 10:01:06.938 | DEBUG | modules.connect:on_check    :626 - client_id:111 - >>> [解析] 第11个数据包, 累计帧数: 456
2025-07-11 10:01:06.939 | DEBUG | modules.connect:on_decode   :959 - client_id:111 - 所需帧数: 67, 目前帧数: 456, 开始解码
2025-07-11 10:01:06.940 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 111 获取现有会话: zh_encoder - b4b3bb92, 池大小: 2
2025-07-11 10:01:06.992 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_encoder - bb192bff, 当前可用: 1/2
2025-07-11 10:01:06.993 | DEBUG | modules.decoder:decode_chunk:483 - client_id:000 - 当前推理累计时点: 112
2025-07-11 10:01:06.994 | DEBUG | modules.decoder:decode_chunk:484 - client_id:000 - ctc 搜索起始时点: 96 搜索片段长度: tensor([16])
2025-07-11 10:01:06.994 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 000 获取现有会话: zh_ctc - 5ef3fa15, 池大小: 1
2025-07-11 10:01:06.995 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_ctc - 5ef3fa15, 当前可用: 1/1
2025-07-11 10:01:07.017 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_encoder - b4b3bb92, 当前可用: 2/2
2025-07-11 10:01:07.020 | DEBUG | modules.decoder:decode_chunk:483 - client_id:111 - 当前推理累计时点: 112
2025-07-11 10:01:07.023 | DEBUG | modules.decoder:decode_chunk:484 - client_id:111 - ctc 搜索起始时点: 96 搜索片段长度: tensor([16])
2025-07-11 10:01:07.023 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 111 获取现有会话: zh_ctc - 5ef3fa15, 池大小: 1
2025-07-11 10:01:07.026 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_ctc - 5ef3fa15, 当前可用: 1/1
2025-07-11 10:01:07.066 | DEBUG | modules.decoder:search      :371 - ctc token&time: [(1522, 12), (5380, 17), (364, 22), (2181, 26), (448, 32), (4765, 35), (4935, 40), (39, 57), (1041, 62), (1241, 66), (1899, 71), (4416, 76), (1720, 81), (4765, 85), (67, 90), (363, 96), (1542, 99), (3185, 104), (30, 108)]
2025-07-11 10:01:07.069 | DEBUG | modules.decoder:decode_chunk:495 - ctc result: "很高兴来到这里，与大家探讨我这为关心的一"
2025-07-11 10:01:07.074 | DEBUG | modules.connect:on_result   :428 - client_id:000 - <<< [发送] 第6个数据包, 更新识别结果: "很高兴来到这里，与大家探讨我这为关心的一"
2025-07-11 10:01:07.104 | DEBUG | modules.decoder:search      :371 - ctc token&time: [(1720, 7), (1664, 11), (928, 17), (3010, 22), (1586, 27), (753, 43), (1199, 49), (157, 64), (589, 68), (1542, 73), (3010, 77), (1199, 81), (3185, 85), (1299, 89), (5214, 94), (36, 99), (3010, 104), (4399, 108)]
2025-07-11 10:01:07.104 | DEBUG | modules.decoder:decode_chunk:495 - ctc result: "我想在理性，哲学，以及心理学的层面上理解"
2025-07-11 10:01:07.105 | DEBUG | modules.connect:on_result   :428 - client_id:111 - <<< [发送] 第6个数据包, 更新识别结果: "我想在理性，哲学，以及心理学的层面上理解"
2025-07-11 10:01:07.339 | DEBUG | modules.connect:on_check    :626 - client_id:000 - >>> [解析] 第12个数据包, 累计帧数: 494
2025-07-11 10:01:07.340 | DEBUG | modules.connect:on_decode   :959 - client_id:000 - 所需帧数: 67, 目前帧数: 494, 开始解码
2025-07-11 10:01:07.342 | DEBUG | modules.connect:on_decode   :988 - client_id:000 - chunk 完成解码, 识别结果不变
2025-07-11 10:01:07.347 | DEBUG | modules.connect:on_check    :626 - client_id:111 - >>> [解析] 第12个数据包, 累计帧数: 494
2025-07-11 10:01:07.348 | DEBUG | modules.connect:on_decode   :959 - client_id:111 - 所需帧数: 67, 目前帧数: 494, 开始解码
2025-07-11 10:01:07.349 | DEBUG | modules.connect:on_decode   :988 - client_id:111 - chunk 完成解码, 识别结果不变
2025-07-11 10:01:07.745 | DEBUG | modules.connect:on_check    :626 - client_id:000 - >>> [解析] 第13个数据包, 累计帧数: 532
2025-07-11 10:01:07.746 | DEBUG | modules.connect:on_decode   :959 - client_id:000 - 所需帧数: 67, 目前帧数: 532, 开始解码
2025-07-11 10:01:07.748 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 000 获取现有会话: zh_encoder - bb192bff, 池大小: 2
2025-07-11 10:01:07.755 | DEBUG | modules.connect:on_check    :626 - client_id:111 - >>> [解析] 第13个数据包, 累计帧数: 532
2025-07-11 10:01:07.755 | DEBUG | modules.connect:on_decode   :959 - client_id:111 - 所需帧数: 67, 目前帧数: 532, 开始解码
2025-07-11 10:01:07.757 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 111 获取现有会话: zh_encoder - b4b3bb92, 池大小: 2
2025-07-11 10:01:07.831 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_encoder - bb192bff, 当前可用: 1/2
2025-07-11 10:01:07.832 | DEBUG | modules.decoder:decode_chunk:483 - client_id:000 - 当前推理累计时点: 128
2025-07-11 10:01:07.833 | DEBUG | modules.decoder:decode_chunk:484 - client_id:000 - ctc 搜索起始时点: 112 搜索片段长度: tensor([16])
2025-07-11 10:01:07.833 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 000 获取现有会话: zh_ctc - 5ef3fa15, 池大小: 1
2025-07-11 10:01:07.841 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_encoder - b4b3bb92, 当前可用: 2/2
2025-07-11 10:01:07.841 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_ctc - 5ef3fa15, 当前可用: 1/1
2025-07-11 10:01:07.842 | DEBUG | modules.decoder:decode_chunk:483 - client_id:111 - 当前推理累计时点: 128
2025-07-11 10:01:07.842 | DEBUG | modules.decoder:decode_chunk:484 - client_id:111 - ctc 搜索起始时点: 112 搜索片段长度: tensor([16])
2025-07-11 10:01:07.842 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 111 获取现有会话: zh_ctc - 5ef3fa15, 池大小: 1
2025-07-11 10:01:07.844 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_ctc - 5ef3fa15, 当前可用: 1/1
2025-07-11 10:01:07.921 | DEBUG | modules.decoder:search      :371 - ctc token&time: [(1522, 12), (5380, 17), (364, 22), (2181, 26), (448, 32), (4765, 35), (4935, 40), (39, 57), (1041, 62), (1241, 66), (1899, 71), (4416, 76), (1720, 81), (4765, 85), (67, 90), (363, 96), (1542, 99), (3185, 104), (30, 108), (58, 112), (68, 116), (5268, 121)]
2025-07-11 10:01:07.924 | DEBUG | modules.decoder:decode_chunk:495 - ctc result: "很高兴来到这里，与大家探讨我这为关心的一个主题"
2025-07-11 10:01:07.926 | DEBUG | modules.connect:on_result   :428 - client_id:000 - <<< [发送] 第7个数据包, 更新识别结果: "很高兴来到这里，与大家探讨我这为关心的一个主题"
2025-07-11 10:01:07.954 | DEBUG | modules.decoder:search      :371 - ctc token&time: [(1720, 7), (1664, 11), (928, 17), (3010, 22), (1586, 27), (753, 43), (1199, 49), (157, 64), (589, 68), (1542, 73), (3010, 77), (1199, 81), (3185, 85), (1299, 89), (5214, 94), (36, 99), (3010, 104), (4399, 108)]
2025-07-11 10:01:07.954 | DEBUG | modules.decoder:decode_chunk:495 - ctc result: "我想在理性，哲学，以及心理学的层面上理解"
2025-07-11 10:01:07.954 | DEBUG | modules.connect:on_decode   :988 - client_id:111 - chunk 完成解码, 识别结果不变
2025-07-11 10:01:08.153 | DEBUG | modules.connect:on_check    :626 - client_id:000 - >>> [解析] 第14个数据包, 累计帧数: 570
2025-07-11 10:01:08.154 | DEBUG | modules.connect:on_decode   :959 - client_id:000 - 所需帧数: 67, 目前帧数: 570, 开始解码
2025-07-11 10:01:08.156 | DEBUG | modules.connect:on_decode   :988 - client_id:000 - chunk 完成解码, 识别结果不变
2025-07-11 10:01:08.161 | DEBUG | modules.connect:on_check    :626 - client_id:111 - >>> [解析] 第14个数据包, 累计帧数: 570
2025-07-11 10:01:08.162 | DEBUG | modules.connect:on_decode   :959 - client_id:111 - 所需帧数: 67, 目前帧数: 570, 开始解码
2025-07-11 10:01:08.163 | DEBUG | modules.connect:on_decode   :988 - client_id:111 - chunk 完成解码, 识别结果不变
2025-07-11 10:01:08.562 | DEBUG | modules.connect:on_check    :626 - client_id:000 - >>> [解析] 第15个数据包, 累计帧数: 608
2025-07-11 10:01:08.563 | DEBUG | modules.connect:on_decode   :959 - client_id:000 - 所需帧数: 67, 目前帧数: 608, 开始解码
2025-07-11 10:01:08.564 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 000 获取现有会话: zh_encoder - bb192bff, 池大小: 2
2025-07-11 10:01:08.571 | DEBUG | modules.connect:on_check    :626 - client_id:111 - >>> [解析] 第15个数据包, 累计帧数: 608
2025-07-11 10:01:08.572 | DEBUG | modules.connect:on_decode   :959 - client_id:111 - 所需帧数: 67, 目前帧数: 608, 开始解码
2025-07-11 10:01:08.573 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 111 获取现有会话: zh_encoder - b4b3bb92, 池大小: 2
2025-07-11 10:01:08.632 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_encoder - bb192bff, 当前可用: 1/2
2025-07-11 10:01:08.633 | DEBUG | modules.decoder:decode_chunk:483 - client_id:000 - 当前推理累计时点: 144
2025-07-11 10:01:08.633 | DEBUG | modules.decoder:decode_chunk:484 - client_id:000 - ctc 搜索起始时点: 128 搜索片段长度: tensor([16])
2025-07-11 10:01:08.633 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 000 获取现有会话: zh_ctc - 5ef3fa15, 池大小: 1
2025-07-11 10:01:08.634 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_ctc - 5ef3fa15, 当前可用: 1/1
2025-07-11 10:01:08.644 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_encoder - b4b3bb92, 当前可用: 2/2
2025-07-11 10:01:08.645 | DEBUG | modules.decoder:decode_chunk:483 - client_id:111 - 当前推理累计时点: 144
2025-07-11 10:01:08.647 | DEBUG | modules.decoder:decode_chunk:484 - client_id:111 - ctc 搜索起始时点: 128 搜索片段长度: tensor([16])
2025-07-11 10:01:08.648 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 111 获取现有会话: zh_ctc - 5ef3fa15, 池大小: 1
2025-07-11 10:01:08.648 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_ctc - 5ef3fa15, 当前可用: 1/1
2025-07-11 10:01:08.740 | DEBUG | modules.decoder:search      :371 - ctc token&time: [(1522, 12), (5380, 17), (364, 22), (2181, 26), (448, 32), (4765, 35), (4935, 40), (39, 57), (1041, 62), (1241, 66), (1899, 71), (4416, 76), (1720, 81), (4765, 85), (67, 90), (363, 96), (1542, 99), (3185, 104), (30, 108), (58, 112), (68, 116), (5268, 121), (4851, 139)]
2025-07-11 10:01:08.743 | DEBUG | modules.decoder:decode_chunk:495 - ctc result: "很高兴来到这里，与大家探讨我这为关心的一个主题，那"
2025-07-11 10:01:08.746 | DEBUG | modules.connect:on_result   :428 - client_id:000 - <<< [发送] 第8个数据包, 更新识别结果: "很高兴来到这里，与大家探讨我这为关心的一个主题，那"
2025-07-11 10:01:08.763 | DEBUG | modules.decoder:search      :371 - ctc token&time: [(1720, 7), (1664, 11), (928, 17), (3010, 22), (1586, 27), (753, 43), (1199, 49), (157, 64), (589, 68), (1542, 73), (3010, 77), (1199, 81), (3185, 85), (1299, 89), (5214, 94), (36, 99), (3010, 104), (4399, 108), (3730, 136), (1530, 141)]
2025-07-11 10:01:08.763 | DEBUG | modules.decoder:decode_chunk:495 - ctc result: "我想在理性，哲学，以及心理学的层面上理解，美得"
2025-07-11 10:01:08.764 | DEBUG | modules.connect:on_result   :428 - client_id:111 - <<< [发送] 第7个数据包, 更新识别结果: "我想在理性，哲学，以及心理学的层面上理解，美得"
2025-07-11 10:01:08.970 | DEBUG | modules.connect:on_check    :626 - client_id:000 - >>> [解析] 第16个数据包, 累计帧数: 646
2025-07-11 10:01:08.970 | DEBUG | modules.connect:on_decode   :959 - client_id:000 - 所需帧数: 67, 目前帧数: 646, 开始解码
2025-07-11 10:01:08.972 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 000 获取现有会话: zh_encoder - bb192bff, 池大小: 2
2025-07-11 10:01:08.978 | DEBUG | modules.connect:on_check    :626 - client_id:111 - >>> [解析] 第16个数据包, 累计帧数: 646
2025-07-11 10:01:08.978 | DEBUG | modules.connect:on_decode   :959 - client_id:111 - 所需帧数: 67, 目前帧数: 646, 开始解码
2025-07-11 10:01:08.980 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 111 获取现有会话: zh_encoder - b4b3bb92, 池大小: 2
2025-07-11 10:01:09.041 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_encoder - bb192bff, 当前可用: 1/2
2025-07-11 10:01:09.041 | DEBUG | modules.decoder:decode_chunk:483 - client_id:000 - 当前推理累计时点: 160
2025-07-11 10:01:09.042 | DEBUG | modules.decoder:decode_chunk:484 - client_id:000 - ctc 搜索起始时点: 144 搜索片段长度: tensor([16])
2025-07-11 10:01:09.042 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 000 获取现有会话: zh_ctc - 5ef3fa15, 池大小: 1
2025-07-11 10:01:09.043 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_ctc - 5ef3fa15, 当前可用: 1/1
2025-07-11 10:01:09.047 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_encoder - b4b3bb92, 当前可用: 2/2
2025-07-11 10:01:09.048 | DEBUG | modules.decoder:decode_chunk:483 - client_id:111 - 当前推理累计时点: 160
2025-07-11 10:01:09.050 | DEBUG | modules.decoder:decode_chunk:484 - client_id:111 - ctc 搜索起始时点: 144 搜索片段长度: tensor([16])
2025-07-11 10:01:09.050 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 111 获取现有会话: zh_ctc - 5ef3fa15, 池大小: 1
2025-07-11 10:01:09.051 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_ctc - 5ef3fa15, 当前可用: 1/1
2025-07-11 10:01:09.157 | DEBUG | modules.decoder:search      :371 - ctc token&time: [(1522, 12), (5380, 17), (364, 22), (2181, 26), (448, 32), (4765, 35), (4935, 40), (39, 57), (1041, 62), (1241, 66), (1899, 71), (4416, 76), (1720, 81), (4765, 85), (67, 90), (363, 96), (1542, 99), (3185, 104), (30, 108), (58, 112), (68, 116), (5268, 121), (4851, 139), (1287, 144), (2085, 148), (3730, 154)]
2025-07-11 10:01:09.158 | DEBUG | modules.decoder:decode_chunk:495 - ctc result: "很高兴来到这里，与大家探讨我这为关心的一个主题，那就是美"
2025-07-11 10:01:09.162 | DEBUG | modules.connect:on_result   :428 - client_id:000 - <<< [发送] 第9个数据包, 更新识别结果: "很高兴来到这里，与大家探讨我这为关心的一个主题，那就是美"
2025-07-11 10:01:09.178 | DEBUG | modules.decoder:search      :371 - ctc token&time: [(1720, 7), (1664, 11), (928, 17), (3010, 22), (1586, 27), (753, 43), (1199, 49), (157, 64), (589, 68), (1542, 73), (3010, 77), (1199, 81), (3185, 85), (1299, 89), (5214, 94), (36, 99), (3010, 104), (4399, 108), (3730, 136), (1530, 141), (206, 145), (5354, 150)]
2025-07-11 10:01:09.178 | DEBUG | modules.decoder:decode_chunk:495 - ctc result: "我想在理性，哲学，以及心理学的层面上理解，美得体验"
2025-07-11 10:01:09.179 | DEBUG | modules.connect:on_result   :428 - client_id:111 - <<< [发送] 第8个数据包, 更新识别结果: "我想在理性，哲学，以及心理学的层面上理解，美得体验"
2025-07-11 10:01:09.379 | DEBUG | modules.connect:on_check    :626 - client_id:000 - >>> [解析] 第17个数据包, 累计帧数: 684
2025-07-11 10:01:09.379 | DEBUG | modules.connect:on_decode   :959 - client_id:000 - 所需帧数: 67, 目前帧数: 684, 开始解码
2025-07-11 10:01:09.381 | DEBUG | modules.connect:on_decode   :988 - client_id:000 - chunk 完成解码, 识别结果不变
2025-07-11 10:01:09.388 | DEBUG | modules.connect:on_check    :626 - client_id:111 - >>> [解析] 第17个数据包, 累计帧数: 684
2025-07-11 10:01:09.388 | DEBUG | modules.connect:on_decode   :959 - client_id:111 - 所需帧数: 67, 目前帧数: 684, 开始解码
2025-07-11 10:01:09.389 | DEBUG | modules.connect:on_decode   :988 - client_id:111 - chunk 完成解码, 识别结果不变
2025-07-11 10:01:09.785 | DEBUG | modules.connect:on_check    :626 - client_id:000 - >>> [解析] 第18个数据包, 累计帧数: 722
2025-07-11 10:01:09.785 | DEBUG | modules.connect:on_decode   :959 - client_id:000 - 所需帧数: 67, 目前帧数: 722, 开始解码
2025-07-11 10:01:09.787 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 000 获取现有会话: zh_encoder - bb192bff, 池大小: 2
2025-07-11 10:01:09.794 | DEBUG | modules.connect:on_check    :626 - client_id:111 - >>> [解析] 第18个数据包, 累计帧数: 722
2025-07-11 10:01:09.794 | DEBUG | modules.connect:on_decode   :959 - client_id:111 - 所需帧数: 67, 目前帧数: 722, 开始解码
2025-07-11 10:01:09.795 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 111 获取现有会话: zh_encoder - b4b3bb92, 池大小: 2
2025-07-11 10:01:09.862 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_encoder - bb192bff, 当前可用: 1/2
2025-07-11 10:01:09.862 | DEBUG | modules.decoder:decode_chunk:483 - client_id:000 - 当前推理累计时点: 176
2025-07-11 10:01:09.863 | DEBUG | modules.decoder:decode_chunk:484 - client_id:000 - ctc 搜索起始时点: 160 搜索片段长度: tensor([16])
2025-07-11 10:01:09.863 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 000 获取现有会话: zh_ctc - 5ef3fa15, 池大小: 1
2025-07-11 10:01:09.864 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_ctc - 5ef3fa15, 当前可用: 1/1
2025-07-11 10:01:09.868 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_encoder - b4b3bb92, 当前可用: 2/2
2025-07-11 10:01:09.869 | DEBUG | modules.decoder:decode_chunk:483 - client_id:111 - 当前推理累计时点: 176
2025-07-11 10:01:09.870 | DEBUG | modules.decoder:decode_chunk:484 - client_id:111 - ctc 搜索起始时点: 160 搜索片段长度: tensor([16])
2025-07-11 10:01:09.871 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 111 获取现有会话: zh_ctc - 5ef3fa15, 池大小: 1
2025-07-11 10:01:09.873 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_ctc - 5ef3fa15, 当前可用: 1/1
2025-07-11 10:01:09.983 | DEBUG | modules.decoder:search      :371 - ctc token&time: [(1522, 12), (5380, 17), (364, 22), (2181, 26), (448, 32), (4765, 35), (4935, 40), (39, 57), (1041, 62), (1241, 66), (1899, 71), (4416, 76), (1720, 81), (4765, 85), (67, 90), (363, 96), (1542, 99), (3185, 104), (30, 108), (58, 112), (68, 116), (5268, 121), (4851, 139), (1287, 144), (2085, 148), (3730, 154)]
2025-07-11 10:01:09.985 | DEBUG | modules.decoder:decode_chunk:495 - ctc result: "很高兴来到这里，与大家探讨我这为关心的一个主题，那就是美"
2025-07-11 10:01:09.989 | DEBUG | modules.connect:on_decode   :988 - client_id:000 - chunk 完成解码, 识别结果不变
2025-07-11 10:01:10.004 | DEBUG | modules.decoder:search      :371 - ctc token&time: [(1720, 7), (1664, 11), (928, 17), (3010, 22), (1586, 27), (753, 43), (1199, 49), (157, 64), (589, 68), (1542, 73), (3010, 77), (1199, 81), (3185, 85), (1299, 89), (5214, 94), (36, 99), (3010, 104), (4399, 108), (3730, 136), (1530, 141), (206, 145), (5354, 150), (3437, 161), (3467, 166), (2085, 171)]
2025-07-11 10:01:10.004 | DEBUG | modules.decoder:decode_chunk:495 - ctc result: "我想在理性，哲学，以及心理学的层面上理解，美得体验究竟是"
2025-07-11 10:01:10.005 | DEBUG | modules.connect:on_result   :428 - client_id:111 - <<< [发送] 第9个数据包, 更新识别结果: "我想在理性，哲学，以及心理学的层面上理解，美得体验究竟是"
2025-07-11 10:01:10.193 | DEBUG | modules.connect:on_check    :626 - client_id:000 - >>> [解析] 第19个数据包, 累计帧数: 760
2025-07-11 10:01:10.193 | DEBUG | modules.connect:on_decode   :959 - client_id:000 - 所需帧数: 67, 目前帧数: 760, 开始解码
2025-07-11 10:01:10.195 | DEBUG | modules.connect:on_decode   :988 - client_id:000 - chunk 完成解码, 识别结果不变
2025-07-11 10:01:10.202 | DEBUG | modules.connect:on_check    :626 - client_id:111 - >>> [解析] 第19个数据包, 累计帧数: 743
2025-07-11 10:01:10.203 | DEBUG | modules.connect:on_decode   :959 - client_id:111 - 所需帧数: 67, 目前帧数: 743, 开始解码
2025-07-11 10:01:10.204 | DEBUG | modules.decoder:decode      :526 - client_id:111 - 第11个chunk, 原始帧: 704~743,  torch.Size([1, 39, 80])
2025-07-11 10:01:10.205 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 111 获取现有会话: zh_encoder - bb192bff, 池大小: 2
2025-07-11 10:01:10.258 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_encoder - bb192bff, 当前可用: 2/2
2025-07-11 10:01:10.259 | DEBUG | modules.decoder:decode_chunk:483 - client_id:111 - 当前推理累计时点: 185
2025-07-11 10:01:10.259 | DEBUG | modules.decoder:decode_chunk:484 - client_id:111 - ctc 搜索起始时点: 176 搜索片段长度: tensor([9])
2025-07-11 10:01:10.259 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 111 获取现有会话: zh_ctc - 5ef3fa15, 池大小: 1
2025-07-11 10:01:10.260 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_ctc - 5ef3fa15, 当前可用: 1/1
2025-07-11 10:01:10.291 | DEBUG | modules.decoder:search      :371 - ctc token&time: [(1720, 7), (1664, 11), (928, 17), (3010, 22), (1586, 27), (753, 43), (1199, 49), (157, 64), (589, 68), (1542, 73), (3010, 77), (1199, 81), (3185, 85), (1299, 89), (5214, 94), (36, 99), (3010, 104), (4399, 108), (3730, 136), (1530, 141), (206, 145), (5354, 150), (3437, 161), (3467, 166), (2085, 171), (132, 176), (73, 179)]
2025-07-11 10:01:10.291 | DEBUG | modules.decoder:decode_chunk:495 - ctc result: "我想在理性，哲学，以及心理学的层面上理解，美得体验究竟是什么"
2025-07-11 10:01:10.291 | INFO  | modules.connect:on_decode   :980 - client_id:111 - *** 最后一个数据包完成解码 ***
2025-07-11 10:01:10.292 | DEBUG | modules.connect:on_result   :428 - client_id:111 - <<< [发送] 第10个数据包, 更新识别结果: "我想在理性，哲学，以及心理学的层面上理解，美得体验究竟是什么"
2025-07-11 10:01:10.292 | INFO  | modules.connect:on_result   :430 - client_id:111 - <<< [响应] 最终识别结果: 我想在理性，哲学，以及心理学的层面上理解，美得体验究竟是什么
2025-07-11 10:01:10.292 | DEBUG | server :receive     :322 - client_id:111 - 已发送最后一个识别结果, 主动关闭客户连接
2025-07-11 10:01:10.292 | DEBUG | modules.connect:disconnect  :290 - client_id:111 - 开始断开连接，当前活跃连接数: 2
2025-07-11 10:01:10.295 | DEBUG | modules.connect:disconnect  :307 - client_id:111 - 开始清理客户端状态和缓存
2025-07-11 10:01:10.296 | DEBUG | modules.decoder:__del__     :444 - ASRDecoder 显式释放资源
2025-07-11 10:01:10.296 | DEBUG | modules.connect:disconnect  :326 - client_id:111 - 已经断开连接，活跃连接数已更新: 1
2025-07-11 10:01:10.296 | INFO  | server :receive     :329 - client_id: 111 - 关闭客户连接，当前活跃连接数: 1
2025-07-11 10:01:10.600 | DEBUG | modules.connect:on_check    :626 - client_id:000 - >>> [解析] 第20个数据包, 累计帧数: 798
2025-07-11 10:01:10.601 | DEBUG | modules.connect:on_decode   :959 - client_id:000 - 所需帧数: 67, 目前帧数: 798, 开始解码
2025-07-11 10:01:10.602 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 000 获取现有会话: zh_encoder - bb192bff, 池大小: 2
2025-07-11 10:01:10.661 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_encoder - bb192bff, 当前可用: 2/2
2025-07-11 10:01:10.662 | DEBUG | modules.decoder:decode_chunk:483 - client_id:000 - 当前推理累计时点: 192
2025-07-11 10:01:10.662 | DEBUG | modules.decoder:decode_chunk:484 - client_id:000 - ctc 搜索起始时点: 176 搜索片段长度: tensor([16])
2025-07-11 10:01:10.662 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 000 获取现有会话: zh_ctc - 5ef3fa15, 池大小: 1
2025-07-11 10:01:10.663 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_ctc - 5ef3fa15, 当前可用: 1/1
2025-07-11 10:01:10.709 | DEBUG | modules.decoder:search      :371 - ctc token&time: [(1522, 12), (5380, 17), (364, 22), (2181, 26), (448, 32), (4765, 35), (4935, 40), (39, 57), (1041, 62), (1241, 66), (1899, 71), (4416, 76), (1720, 81), (4765, 85), (67, 90), (363, 96), (1542, 99), (3185, 104), (30, 108), (58, 112), (68, 116), (5268, 121), (4851, 139), (1287, 144), (2085, 148), (3730, 154), (1720, 178), (3300, 183), (3437, 188)]
2025-07-11 10:01:10.709 | DEBUG | modules.decoder:decode_chunk:495 - ctc result: "很高兴来到这里，与大家探讨我这为关心的一个主题，那就是美，我研究"
2025-07-11 10:01:10.709 | DEBUG | modules.connect:on_result   :428 - client_id:000 - <<< [发送] 第10个数据包, 更新识别结果: "很高兴来到这里，与大家探讨我这为关心的一个主题，那就是美，我研究"
2025-07-11 10:01:11.008 | DEBUG | modules.connect:on_check    :626 - client_id:000 - >>> [解析] 第21个数据包, 累计帧数: 836
2025-07-11 10:01:11.009 | DEBUG | modules.connect:on_decode   :959 - client_id:000 - 所需帧数: 67, 目前帧数: 836, 开始解码
2025-07-11 10:01:11.010 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 000 获取现有会话: zh_encoder - bb192bff, 池大小: 2
2025-07-11 10:01:11.070 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_encoder - bb192bff, 当前可用: 2/2
2025-07-11 10:01:11.070 | DEBUG | modules.decoder:decode_chunk:483 - client_id:000 - 当前推理累计时点: 208
2025-07-11 10:01:11.071 | DEBUG | modules.decoder:decode_chunk:484 - client_id:000 - ctc 搜索起始时点: 192 搜索片段长度: tensor([16])
2025-07-11 10:01:11.071 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 000 获取现有会话: zh_ctc - 5ef3fa15, 池大小: 1
2025-07-11 10:01:11.072 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_ctc - 5ef3fa15, 当前可用: 1/1
2025-07-11 10:01:11.111 | DEBUG | modules.decoder:search      :371 - ctc token&time: [(1522, 12), (5380, 17), (364, 22), (2181, 26), (448, 32), (4765, 35), (4935, 40), (39, 57), (1041, 62), (1241, 66), (1899, 71), (4416, 76), (1720, 81), (4765, 85), (67, 90), (363, 96), (1542, 99), (3185, 104), (30, 108), (58, 112), (68, 116), (5268, 121), (4851, 139), (1287, 144), (2085, 148), (3730, 154), (1720, 178), (3300, 183), (3437, 188), (3957, 193), (2158, 197), (39, 201)]
2025-07-11 10:01:11.111 | DEBUG | modules.decoder:decode_chunk:495 - ctc result: "很高兴来到这里，与大家探讨我这为关心的一个主题，那就是美，我研究艺术与"
2025-07-11 10:01:11.112 | DEBUG | modules.connect:on_result   :428 - client_id:000 - <<< [发送] 第11个数据包, 更新识别结果: "很高兴来到这里，与大家探讨我这为关心的一个主题，那就是美，我研究艺术与"
2025-07-11 10:01:11.416 | DEBUG | modules.connect:on_check    :626 - client_id:000 - >>> [解析] 第22个数据包, 累计帧数: 874
2025-07-11 10:01:11.417 | DEBUG | modules.connect:on_decode   :959 - client_id:000 - 所需帧数: 67, 目前帧数: 874, 开始解码
2025-07-11 10:01:11.418 | DEBUG | modules.connect:on_decode   :988 - client_id:000 - chunk 完成解码, 识别结果不变
2025-07-11 10:01:11.823 | DEBUG | modules.connect:on_check    :626 - client_id:000 - >>> [解析] 第23个数据包, 累计帧数: 912
2025-07-11 10:01:11.824 | DEBUG | modules.connect:on_decode   :959 - client_id:000 - 所需帧数: 67, 目前帧数: 912, 开始解码
2025-07-11 10:01:11.825 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 000 获取现有会话: zh_encoder - bb192bff, 池大小: 2
2025-07-11 10:01:11.888 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_encoder - bb192bff, 当前可用: 2/2
2025-07-11 10:01:11.888 | DEBUG | modules.decoder:decode_chunk:483 - client_id:000 - 当前推理累计时点: 224
2025-07-11 10:01:11.889 | DEBUG | modules.decoder:decode_chunk:484 - client_id:000 - ctc 搜索起始时点: 208 搜索片段长度: tensor([16])
2025-07-11 10:01:11.889 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 000 获取现有会话: zh_ctc - 5ef3fa15, 池大小: 1
2025-07-11 10:01:11.890 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_ctc - 5ef3fa15, 当前可用: 1/1
2025-07-11 10:01:11.929 | DEBUG | modules.decoder:search      :371 - ctc token&time: [(1522, 12), (5380, 17), (364, 22), (2181, 26), (448, 32), (4765, 35), (4935, 40), (39, 57), (1041, 62), (1241, 66), (1899, 71), (4416, 76), (1720, 81), (4765, 85), (67, 90), (363, 96), (1542, 99), (3185, 104), (30, 108), (58, 112), (68, 116), (5268, 121), (4851, 139), (1287, 144), (2085, 148), (3730, 154), (1720, 178), (3300, 183), (3437, 188), (3957, 193), (2158, 197), (39, 201), (1229, 208), (3730, 211), (753, 216), (1199, 221)]
2025-07-11 10:01:11.930 | DEBUG | modules.decoder:decode_chunk:495 - ctc result: "很高兴来到这里，与大家探讨我这为关心的一个主题，那就是美，我研究艺术与审美哲学"
2025-07-11 10:01:11.930 | DEBUG | modules.connect:on_result   :428 - client_id:000 - <<< [发送] 第12个数据包, 更新识别结果: "很高兴来到这里，与大家探讨我这为关心的一个主题，那就是美，我研究艺术与审美哲学"
2025-07-11 10:01:12.232 | DEBUG | modules.connect:on_check    :626 - client_id:000 - >>> [解析] 第24个数据包, 累计帧数: 950
2025-07-11 10:01:12.233 | DEBUG | modules.connect:on_decode   :959 - client_id:000 - 所需帧数: 67, 目前帧数: 950, 开始解码
2025-07-11 10:01:12.234 | DEBUG | modules.connect:on_decode   :988 - client_id:000 - chunk 完成解码, 识别结果不变
2025-07-11 10:01:12.639 | DEBUG | modules.connect:on_check    :626 - client_id:000 - >>> [解析] 第25个数据包, 累计帧数: 988
2025-07-11 10:01:12.640 | DEBUG | modules.connect:on_decode   :959 - client_id:000 - 所需帧数: 67, 目前帧数: 988, 开始解码
2025-07-11 10:01:12.641 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 000 获取现有会话: zh_encoder - bb192bff, 池大小: 2
2025-07-11 10:01:12.698 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_encoder - bb192bff, 当前可用: 2/2
2025-07-11 10:01:12.698 | DEBUG | modules.decoder:decode_chunk:483 - client_id:000 - 当前推理累计时点: 240
2025-07-11 10:01:12.699 | DEBUG | modules.decoder:decode_chunk:484 - client_id:000 - ctc 搜索起始时点: 224 搜索片段长度: tensor([16])
2025-07-11 10:01:12.699 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 000 获取现有会话: zh_ctc - 5ef3fa15, 池大小: 1
2025-07-11 10:01:12.700 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_ctc - 5ef3fa15, 当前可用: 1/1
2025-07-11 10:01:12.740 | DEBUG | modules.decoder:search      :371 - ctc token&time: [(1522, 12), (5380, 17), (364, 22), (2181, 26), (448, 32), (4765, 35), (4935, 40), (39, 57), (1041, 62), (1241, 66), (1899, 71), (4416, 76), (1720, 81), (4765, 85), (67, 90), (363, 96), (1542, 99), (3185, 104), (30, 108), (58, 112), (68, 116), (5268, 121), (4851, 139), (1287, 144), (2085, 148), (3730, 154), (1720, 178), (3300, 183), (3437, 188), (3957, 193), (2158, 197), (39, 201), (1229, 208), (3730, 211), (753, 216), (1199, 221)]
2025-07-11 10:01:12.740 | DEBUG | modules.decoder:decode_chunk:495 - ctc result: "很高兴来到这里，与大家探讨我这为关心的一个主题，那就是美，我研究艺术与审美哲学"
2025-07-11 10:01:12.740 | DEBUG | modules.connect:on_decode   :988 - client_id:000 - chunk 完成解码, 识别结果不变
2025-07-11 10:01:13.048 | DEBUG | modules.connect:on_check    :626 - client_id:000 - >>> [解析] 第26个数据包, 累计帧数: 1026
2025-07-11 10:01:13.048 | DEBUG | modules.connect:on_decode   :959 - client_id:000 - 所需帧数: 67, 目前帧数: 1026, 开始解码
2025-07-11 10:01:13.049 | DEBUG | modules.connect:on_decode   :988 - client_id:000 - chunk 完成解码, 识别结果不变
2025-07-11 10:01:13.454 | DEBUG | modules.connect:on_check    :626 - client_id:000 - >>> [解析] 第27个数据包, 累计帧数: 1064
2025-07-11 10:01:13.455 | DEBUG | modules.connect:on_decode   :959 - client_id:000 - 所需帧数: 67, 目前帧数: 1064, 开始解码
2025-07-11 10:01:13.456 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 000 获取现有会话: zh_encoder - bb192bff, 池大小: 2
2025-07-11 10:01:13.521 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_encoder - bb192bff, 当前可用: 2/2
2025-07-11 10:01:13.521 | DEBUG | modules.decoder:decode_chunk:483 - client_id:000 - 当前推理累计时点: 256
2025-07-11 10:01:13.521 | DEBUG | modules.decoder:decode_chunk:484 - client_id:000 - ctc 搜索起始时点: 240 搜索片段长度: tensor([16])
2025-07-11 10:01:13.522 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 000 获取现有会话: zh_ctc - 5ef3fa15, 池大小: 1
2025-07-11 10:01:13.523 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_ctc - 5ef3fa15, 当前可用: 1/1
2025-07-11 10:01:13.562 | DEBUG | modules.decoder:search      :371 - ctc token&time: [(1522, 12), (5380, 17), (364, 22), (2181, 26), (448, 32), (4765, 35), (4935, 40), (39, 57), (1041, 62), (1241, 66), (1899, 71), (4416, 76), (1720, 81), (4765, 85), (67, 90), (363, 96), (1542, 99), (3185, 104), (30, 108), (58, 112), (68, 116), (5268, 121), (4851, 139), (1287, 144), (2085, 148), (3730, 154), (1720, 178), (3300, 183), (3437, 188), (3957, 193), (2158, 197), (39, 201), (1229, 208), (3730, 211), (753, 216), (1199, 221), (102, 245), (1227, 249), (36, 254)]
2025-07-11 10:01:13.562 | DEBUG | modules.decoder:decode_chunk:495 - ctc result: "很高兴来到这里，与大家探讨我这为关心的一个主题，那就是美，我研究艺术与审美哲学，事实上"
2025-07-11 10:01:13.563 | DEBUG | modules.connect:on_result   :428 - client_id:000 - <<< [发送] 第13个数据包, 更新识别结果: "很高兴来到这里，与大家探讨我这为关心的一个主题，那就是美，我研究艺术与审美哲学，事实上"
2025-07-11 10:01:13.864 | DEBUG | modules.connect:on_check    :626 - client_id:000 - >>> [解析] 第28个数据包, 累计帧数: 1102
2025-07-11 10:01:13.865 | DEBUG | modules.connect:on_decode   :959 - client_id:000 - 所需帧数: 67, 目前帧数: 1102, 开始解码
2025-07-11 10:01:13.866 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 000 获取现有会话: zh_encoder - bb192bff, 池大小: 2
2025-07-11 10:01:13.930 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_encoder - bb192bff, 当前可用: 2/2
2025-07-11 10:01:13.931 | DEBUG | modules.decoder:decode_chunk:483 - client_id:000 - 当前推理累计时点: 272
2025-07-11 10:01:13.931 | DEBUG | modules.decoder:decode_chunk:484 - client_id:000 - ctc 搜索起始时点: 256 搜索片段长度: tensor([16])
2025-07-11 10:01:13.932 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 000 获取现有会话: zh_ctc - 5ef3fa15, 池大小: 1
2025-07-11 10:01:13.932 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_ctc - 5ef3fa15, 当前可用: 1/1
2025-07-11 10:01:13.979 | DEBUG | modules.decoder:search      :371 - ctc token&time: [(1522, 12), (5380, 17), (364, 22), (2181, 26), (448, 32), (4765, 35), (4935, 40), (39, 57), (1041, 62), (1241, 66), (1899, 71), (4416, 76), (1720, 81), (4765, 85), (67, 90), (363, 96), (1542, 99), (3185, 104), (30, 108), (58, 112), (68, 116), (5268, 121), (4851, 139), (1287, 144), (2085, 148), (3730, 154), (1720, 178), (3300, 183), (3437, 188), (3957, 193), (2158, 197), (39, 201), (1229, 208), (3730, 211), (753, 216), (1199, 221), (102, 245), (1227, 249), (36, 254), (4765, 271)]
2025-07-11 10:01:13.979 | DEBUG | modules.decoder:decode_chunk:495 - ctc result: "很高兴来到这里，与大家探讨我这为关心的一个主题，那就是美，我研究艺术与审美哲学，事实上，这"
2025-07-11 10:01:13.979 | DEBUG | modules.connect:on_result   :428 - client_id:000 - <<< [发送] 第14个数据包, 更新识别结果: "很高兴来到这里，与大家探讨我这为关心的一个主题，那就是美，我研究艺术与审美哲学，事实上，这"
2025-07-11 10:01:14.272 | DEBUG | modules.connect:on_check    :626 - client_id:000 - >>> [解析] 第29个数据包, 累计帧数: 1140
2025-07-11 10:01:14.272 | DEBUG | modules.connect:on_decode   :959 - client_id:000 - 所需帧数: 67, 目前帧数: 1140, 开始解码
2025-07-11 10:01:14.273 | DEBUG | modules.connect:on_decode   :988 - client_id:000 - chunk 完成解码, 识别结果不变
2025-07-11 10:01:14.679 | DEBUG | modules.connect:on_check    :626 - client_id:000 - >>> [解析] 第30个数据包, 累计帧数: 1178
2025-07-11 10:01:14.679 | DEBUG | modules.connect:on_decode   :959 - client_id:000 - 所需帧数: 67, 目前帧数: 1178, 开始解码
2025-07-11 10:01:14.681 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 000 获取现有会话: zh_encoder - bb192bff, 池大小: 2
2025-07-11 10:01:14.742 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_encoder - bb192bff, 当前可用: 2/2
2025-07-11 10:01:14.742 | DEBUG | modules.decoder:decode_chunk:483 - client_id:000 - 当前推理累计时点: 288
2025-07-11 10:01:14.742 | DEBUG | modules.decoder:decode_chunk:484 - client_id:000 - ctc 搜索起始时点: 272 搜索片段长度: tensor([16])
2025-07-11 10:01:14.743 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 000 获取现有会话: zh_ctc - 5ef3fa15, 池大小: 1
2025-07-11 10:01:14.743 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_ctc - 5ef3fa15, 当前可用: 1/1
2025-07-11 10:01:14.789 | DEBUG | modules.decoder:search      :371 - ctc token&time: [(1522, 12), (5380, 17), (364, 22), (2181, 26), (448, 32), (4765, 35), (4935, 40), (39, 57), (1041, 62), (1241, 66), (1899, 71), (4416, 76), (1720, 81), (4765, 85), (67, 90), (363, 96), (1542, 99), (3185, 104), (30, 108), (58, 112), (68, 116), (5268, 121), (4851, 139), (1287, 144), (2085, 148), (3730, 154), (1720, 178), (3300, 183), (3437, 188), (3957, 193), (2158, 197), (39, 201), (1229, 208), (3730, 211), (753, 216), (1199, 221), (102, 245), (1227, 249), (36, 254), (4765, 271), (2085, 275), (1720, 279), (3185, 283)]
2025-07-11 10:01:14.790 | DEBUG | modules.decoder:decode_chunk:495 - ctc result: "很高兴来到这里，与大家探讨我这为关心的一个主题，那就是美，我研究艺术与审美哲学，事实上，这是我的"
2025-07-11 10:01:14.790 | DEBUG | modules.connect:on_result   :428 - client_id:000 - <<< [发送] 第15个数据包, 更新识别结果: "很高兴来到这里，与大家探讨我这为关心的一个主题，那就是美，我研究艺术与审美哲学，事实上，这是我的"
2025-07-11 10:01:15.080 | DEBUG | modules.connect:on_check    :626 - client_id:000 - >>> [解析] 第31个数据包, 累计帧数: 1181
2025-07-11 10:01:15.081 | DEBUG | modules.connect:on_decode   :959 - client_id:000 - 所需帧数: 67, 目前帧数: 1181, 开始解码
2025-07-11 10:01:15.082 | DEBUG | modules.decoder:decode      :526 - client_id:000 - 第18个chunk, 原始帧: 1152~1181,  torch.Size([1, 29, 80])
2025-07-11 10:01:15.083 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 000 获取现有会话: zh_encoder - bb192bff, 池大小: 2
2025-07-11 10:01:15.136 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_encoder - bb192bff, 当前可用: 2/2
2025-07-11 10:01:15.137 | DEBUG | modules.decoder:decode_chunk:483 - client_id:000 - 当前推理累计时点: 294
2025-07-11 10:01:15.138 | DEBUG | modules.decoder:decode_chunk:484 - client_id:000 - ctc 搜索起始时点: 288 搜索片段长度: tensor([6])
2025-07-11 10:01:15.138 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 000 获取现有会话: zh_ctc - 5ef3fa15, 池大小: 1
2025-07-11 10:01:15.139 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_ctc - 5ef3fa15, 当前可用: 1/1
2025-07-11 10:01:15.190 | DEBUG | modules.decoder:search      :371 - ctc token&time: [(1522, 12), (5380, 17), (364, 22), (2181, 26), (448, 32), (4765, 35), (4935, 40), (39, 57), (1041, 62), (1241, 66), (1899, 71), (4416, 76), (1720, 81), (4765, 85), (67, 90), (363, 96), (1542, 99), (3185, 104), (30, 108), (58, 112), (68, 116), (5268, 121), (4851, 139), (1287, 144), (2085, 148), (3730, 154), (1720, 178), (3300, 183), (3437, 188), (3957, 193), (2158, 197), (39, 201), (1229, 208), (3730, 211), (753, 216), (1199, 221), (102, 245), (1227, 249), (36, 254), (4765, 271), (2085, 275), (1720, 279), (3185, 283), (1381, 288), (213, 293)]
2025-07-11 10:01:15.190 | DEBUG | modules.decoder:decode_chunk:495 - ctc result: "很高兴来到这里，与大家探讨我这为关心的一个主题，那就是美，我研究艺术与审美哲学，事实上，这是我的工作"
2025-07-11 10:01:15.190 | INFO  | modules.connect:on_decode   :980 - client_id:000 - *** 最后一个数据包完成解码 ***
2025-07-11 10:01:15.191 | DEBUG | modules.connect:on_result   :428 - client_id:000 - <<< [发送] 第16个数据包, 更新识别结果: "很高兴来到这里，与大家探讨我这为关心的一个主题，那就是美，我研究艺术与审美哲学，事实上，这是我的工作"
2025-07-11 10:01:15.191 | INFO  | modules.connect:on_result   :430 - client_id:000 - <<< [响应] 最终识别结果: 很高兴来到这里，与大家探讨我这为关心的一个主题，那就是美，我研究艺术与审美哲学，事实上，这是我的工作
2025-07-11 10:01:15.191 | DEBUG | server :receive     :322 - client_id:000 - 已发送最后一个识别结果, 主动关闭客户连接
2025-07-11 10:01:15.192 | DEBUG | modules.connect:disconnect  :290 - client_id:000 - 开始断开连接，当前活跃连接数: 1
2025-07-11 10:01:15.193 | DEBUG | modules.connect:disconnect  :307 - client_id:000 - 开始清理客户端状态和缓存
2025-07-11 10:01:15.193 | DEBUG | modules.decoder:__del__     :444 - ASRDecoder 显式释放资源
2025-07-11 10:01:15.194 | DEBUG | modules.connect:disconnect  :326 - client_id:000 - 已经断开连接，活跃连接数已更新: 0
2025-07-11 10:01:15.194 | INFO  | server :receive     :329 - client_id: 000 - 关闭客户连接，当前活跃连接数: 0
2025-07-11 10:04:28.133 | INFO  | server :lifespan    :120 - 正在关闭ASR服务...
2025-07-11 10:04:33.134 | INFO  | server :lifespan    :127 - 系统监控已关闭
2025-07-11 10:04:38.135 | DEBUG | modules.onnx_session_pool:shutdown    :371 - 关闭模型 zh_encoder 的会话 bb192bff, 使用次数: 20
2025-07-11 10:04:38.136 | DEBUG | modules.onnx_session_pool:shutdown    :371 - 关闭模型 zh_encoder 的会话 b4b3bb92, 使用次数: 11
2025-07-11 10:04:38.136 | DEBUG | modules.onnx_session_pool:shutdown    :371 - 关闭模型 zh_ctc 的会话 5ef3fa15, 使用次数: 31
2025-07-11 10:04:38.233 | INFO  | server :lifespan    :134 - ONNX会话池已关闭
2025-07-11 10:04:38.237 | INFO  | server :lifespan    :144 - 实时转写已关闭
2025-07-11 10:04:38.237 | INFO  | server :lifespan    :148 - Server shutdown, delete all global resources
