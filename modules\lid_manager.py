#! /user/bin/env python
#! -*- coding: utf-8 -*-
# File    :  lid_manager.py
# Time    :  2025/06/26 
# Author  :  AI Assistant
# Version :  1.0
# Description: LID (Language Identification) 管理器, 集成语种识别功能到ASR服务

import json
import math
import os
import sys
from typing import Any, Dict, List, Optional, Tuple

import numpy as np
import onnxruntime as ort
import torch
import torchaudio
import torchaudio.compliance.kaldi as kaldi
import yaml
import time

from modules.config import LIDConfig
from modules.decoder import get_ep_list
from modules.logger import logger
from utils.vad_utils import SpeechVadFrontend


class LIDManager:
    """
    语种识别管理器, 负责音频语种识别和VAD检测
    """
    
    def __init__(self, lid_config: LIDConfig, supported_languages):
        """
        初始化LID管理器
        Args:
            lid_config: LIDConfig对象, 包含模型路径等信息
            supported_languages: List 当前服务支持的语种列表
        """
        self.lid_config = lid_config
        self.session = None
        self.input_names = []
        self.output_names = []
        self.configs = {}
        self.spk2id_dict = {}
        self.do_cmvn = lid_config.do_cmvn
        
        # VAD配置
        self.vad_conf = {
            'vad_type': 'webrtcvad',
            'vad_level': 3,
            'frame_length': 20,
            'window_size': 10,
            'seg_thres': 0.9, 
            'max_speech_len': 10,
            'min_speech_len': 0.01,  # 10 ms
            'merge_sil_thres': 2
        }
        self.vad_frontend = SpeechVadFrontend(**self.vad_conf)
        
        # 初始化LID模型
        if lid_config.enabled:
            self._load_lid_model()
        
        # 其他重要参数
        self.max_attempts = self.lid_config.max_attempts
        self.detection_start = self.lid_config.detection_start
        self.detection_interval = self.lid_config.detection_interval
        self.detection_end = self.detection_start + (self.max_attempts - 1) * self.detection_interval
        self.confidence_threshold = self.lid_config.confidence_threshold
        self.silence_threshold = self.lid_config.silence_threshold

        # 限制语种范围
        self.supported_languages = supported_languages
    
    def _load_lid_model(self):
        """加载LID模型和配置"""
        try:
            # 加载配置文件
            config_path = self.lid_config.model_config
            if os.path.exists(config_path):
                with open(config_path, 'r') as fin:
                    self.configs = yaml.load(fin, Loader=yaml.FullLoader)
            else:
                logger.warning(f"LID配置文件不存在: {config_path}")
                return
            
            # 加载ONNX模型
            onnx_path = self.lid_config.model_path
            if os.path.exists(onnx_path):
                EP_list = get_ep_list(self.lid_config.device, self.lid_config.device_id)
                so = ort.SessionOptions()
                so.inter_op_num_threads = 0  # 使用所有可用CPU核心
                so.intra_op_num_threads = 0  # 使用所有可用CPU核心
                so.execution_mode = ort.ExecutionMode.ORT_PARALLEL  # 并行执行模式
                so.graph_optimization_level = ort.GraphOptimizationLevel.ORT_ENABLE_ALL
                self.session = ort.InferenceSession(onnx_path, sess_options=so, providers=EP_list)
                self.input_names = [input.name for input in self.session.get_inputs()]
                self.output_names = [output.name for output in self.session.get_outputs()]
                logger.debug(f"LID模型加载成功: {onnx_path}")
            else:
                logger.warning(f"LID模型文件不存在: {onnx_path}")
                return
            
            # 加载语种字典
            spk2id_path = self.lid_config.dict_path
            if os.path.exists(spk2id_path):
                with open(spk2id_path, 'r') as fspk2id:
                    self.spk2id_dict = json.loads(fspk2id.read())
                logger.debug(f"LID语种字典加载成功: {spk2id_path}")
            else:
                logger.warning(f"LID语种字典文件不存在: {spk2id_path}")
                return
            
            default_cmvn = os.path.abspath(os.path.dirname(onnx_path)) + '/global_cmvn'
            self.cmvn_file = self.configs['dataset_args'].get('cmvn_file', default_cmvn)          
            if os.path.exists(self.cmvn_file):
                self.do_cmvn = True
                logger.debug(f"应用全局CMVN: {self.cmvn_file}")

        except Exception as e:
            logger.error(f"加载LID模型失败: {e}")
            self.session = None
    
    def is_available(self) -> bool:
        """检查LID功能是否可用"""
        return self.session is not None and len(self.spk2id_dict) > 0
    
    def detect_speech(self, waveform: torch.Tensor, sample_rate: int = 16000) -> bool:
        """
        检测音频是否包含有效语音
        Args:
            waveform: 音频波形张量
            sample_rate: 采样率
        Returns:
            bool: True表示包含有效语音, False表示静音或噪音
        """
        try:
            start_time = time.time()
            if waveform.dim() == 2:
                waveform = waveform[0]
            
            # 使用VAD检测语音
            segments, segment_lens, segment_ranges = self.vad_frontend.get_all_speech_segments(waveform, sample_rate)
            
            logger.debug(f"VAD 耗时: {time.time() - start_time:.4f} s")

            # 如果有语音片段且总长度超过阈值, 认为是有效语音
            total_speech_duration = sum(segment_lens) / 1000.0  # 转换为秒
            return total_speech_duration > 0.1  # 至少100ms的语音           

        except Exception as e:
            logger.warning(f"VAD 检测失败: {e}")
            return True  # 检测失败时默认认为是有效语音
    
    def _compute_fbank(self, waveform: torch.Tensor, sample_rate: int) -> torch.Tensor:
        """计算fbank特征"""
        if not self.configs:
            raise ValueError("LID配置未加载")
            
        dataset_args = self.configs.get('dataset_args', {})
        fbank_args = dataset_args.get('fbank_args', {})
        
        waveform = waveform * (1 << 15)
        mat = kaldi.fbank(
            waveform,
            num_mel_bins=fbank_args.get('num_mel_bins', 80),
            frame_length=fbank_args.get('frame_length', 25),
            frame_shift=fbank_args.get('frame_shift', 10),
            dither=fbank_args.get('dither', 0.0),
            sample_frequency=sample_rate,
            window_type='hamming',
            use_energy=False
        )
        return mat
    
    def _load_cmvn(self, cmvn_file: str) -> np.ndarray:
        """加载CMVN统计信息"""
        cmvn_file_export = cmvn_file + '.export'
        if os.path.exists(cmvn_file_export):
            with open(cmvn_file_export) as f:
                cmvn_stats = json.load(f)
                cmvn_stats = np.array([cmvn_stats['mean_stat'], cmvn_stats['var_stat']])
        else:
            with open(cmvn_file) as f:
                cmvn_stats = json.load(f)
            means = cmvn_stats['mean_stat']
            variance = cmvn_stats['var_stat']
            count = cmvn_stats['frame_num']
            for i in range(len(means)):
                means[i] /= count
                variance[i] = variance[i] / count - means[i] * means[i]
                if variance[i] < 1.0e-20:
                    variance[i] = 1.0e-20
                variance[i] = 1.0 / math.sqrt(variance[i])
            cmvn_stats = np.array([means, variance])
        return cmvn_stats
    
    def _apply_global_cmvn(self, mat: torch.Tensor, cmvn_file: str) -> torch.Tensor:
        """应用全局CMVN"""
        mean, istd = self._load_cmvn(cmvn_file)
        mean, var = torch.from_numpy(mean).float(), torch.from_numpy(istd).float()
        mat = mat - mean
        mat = mat * var
        return mat
    
    def _apply_cvn(self, mat: torch.Tensor) -> torch.Tensor:
        """应用CVN（倒谱均值归一化）"""
        mat = mat - torch.mean(mat, dim=0)
        return mat
    
    def _audio_seg(self, waveform: torch.Tensor, sr: int = 16000) -> Tuple[torch.Tensor, float]:
        """音频分段, 提取最长的语音片段"""
        try:
            segments, segment_lens, segment_ranges = self.vad_frontend.get_all_speech_segments(waveform, sr)
            if segment_lens:
                index = segment_lens.index(max(segment_lens))
                return segments[index], segment_lens[index] / 1000.0
            else:
                return waveform[0] if waveform.dim() > 1 else waveform, waveform.shape[-1] / sr
        except:
            return waveform[0] if waveform.dim() > 1 else waveform, waveform.shape[-1] / sr
    
    def predict_language(self, waveform: torch.Tensor, sample_rate: int = 16000) -> Dict[str, Any]:
        """
        预测音频的语种
        Args:
            waveform: 音频波形张量
            sample_rate: 采样率
        Returns:
            Dict: 包含预测结果的字典
        """
        if not self.is_available():
            logger.warning("LID功能不可用")
            return {"predict": "unknown", "scores": {}, "duration": 0.0}
        
        try:
            # 音频分段
            duration = waveform.shape[-1] / sample_rate
            if duration > 5:
                waveform, duration = self._audio_seg(waveform, sr=sample_rate)
            waveform = waveform.unsqueeze(0)

            # 重采样到目标采样率
            resample_rate = self.configs['dataset_args']['resample_rate']
            if sample_rate != resample_rate:
                waveform = torchaudio.transforms.Resample(
                    orig_freq=sample_rate, new_freq=resample_rate)(waveform)
            duration = waveform.shape[-1] / resample_rate

            # 提取特征
            feats = self._compute_fbank(waveform, resample_rate)
            
            # 应用CMVN
            if self.do_cmvn:
                feats = self._apply_global_cmvn(feats, self.cmvn_file)
            else:
                feats = self._apply_cvn(feats)
            
            # 准备输入
            feats = feats.unsqueeze(0).numpy()  # 添加batch维度
            if len(self.input_names) == 1:
                ort_inputs = {self.input_names[0]: feats}
            elif len(self.input_names) == 2:
                fake_targets = torch.randint(12, (1,)).numpy()
                ort_inputs = {self.input_names[0]: feats, self.input_names[1]: fake_targets}
            else:
                raise ValueError(f"不支持的输入数量: {len(self.input_names)}")
            
            # 推理
            ort_outs = self.session.run(self.output_names, ort_inputs)
            output = ort_outs[0]
            
            # 输出分数对应的语种列表
            spks = list(self.spk2id_dict.keys())
 
            # 根据限制的语种列表,剔除不支持的语种
            limited_spks = []
            limited_scores = []
            for i, spk in enumerate(spks):
                if spk in self.supported_languages:
                    limited_spks.append(spk)
                    limited_scores.append(output[0][i].item())
            predict_id = np.argmax(limited_scores)
            predict_spk = limited_spks[predict_id]

            del spks

            # 计算分数
            second_score = 0.0
            score_dict = {}
            mm = max(limited_scores)
            mi = min(limited_scores)
            for i, k in enumerate(limited_spks):
                s = (limited_scores[i] - mi) / (mm - mi) if mm != mi else 0.5
                score_dict[k] = round(s, 2)
                if i != predict_id and s > second_score:
                    second_score = s
            confidence = round(score_dict[limited_spks[predict_id]] - second_score, 2)
            result = {
                "predict": predict_spk,
                "scores": score_dict,
                "confidence": confidence,
                "duration": duration
            }
            
            # debug
            # torchaudio.save(f"audio_predict_{duration}s_{spks[predict_id]}.wav", waveform, resample_rate)

            # logger.info(f"LID预测结果: {result['predict']}, 置信度: {score_dict[result['predict']]}, {score_dict}")
            return result
            
        except Exception as e:
            logger.error(f"LID预测失败: {e}")
            return {"predict": "unknown", "scores": {}, "duration": 0.0}
