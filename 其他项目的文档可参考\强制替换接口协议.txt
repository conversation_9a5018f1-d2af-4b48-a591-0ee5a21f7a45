强制替换接口协议
2022-06-09

[toc]

创建强制替换词库
基本信息
Path：/correction_words

**Method：**POST

接口描述： 上传一个强制替换列表文件，创建强制替换词库。

强制替换列表文件格式
UTF-8 编码的文本文件，每行一个强制替换词对，原词和目标词之间用tab分隔，每行不超过 100 字符，不超过 10000 行。总共可创建 200 个强制替换词库。

文件示例：

#原词和目标词之间用tab分隔
无忌	5G
#支持多个词替换成一个词，用竖线分隔
google search|Google search|谷哥搜索	谷歌搜索
#如果tab后没有字符，表示替换为空
强制删除	
请求参数
Body（form-data）

名称	类型	是否必需	说明	默认值
name	string	否	强制替换词库的名称	空
file	file	是	强制替换列表文件（UTF-8 编码的 TXT 格式）	必填
lang_type	string	是	语种	必填
返回参数
名称	类型	说明
status	string	状态码
message	string	状态码描述
data	object	数据
请求示例

curl -X POST '127.0.0.1:7100/correction_words' \
-F 'name=政治相关' \
-F 'file=@politics.txt' \
-F 'lang_type=zh-cmn-Hans-CN'
返回示例

{
    "status": "00000",
    "message": "success",
    "data": "CW43DA3B90" // data为创建的强制替换词库ID
}
强制替换词信息列表
基本信息
Path：/correction_words

**Method：**GET

接口描述： 获取所有强制替换词库的信息。

返回参数
名称	类型	说明
status	string	状态码
message	string	状态码描述
data	object[]	数据
├─ id	string	强制替换词库ID
├─ name	string	强制替换词库名称
├─ lang_type	string	语种
├─ amount	int	强制替换词对数量
├─ update_time	string	最近修改时间
请求示例

curl -X GET '127.0.0.1:7100/correction_words'
返回示例

{
    "status": "00000",
    "message": "success",
    "data": [
        {
            "id": "CW43DA3B90",
            "name": "政治相关",
            "lang_type": "zh-cmn-Hans-CN",
            "amount": 12,
            "update_time": "2022-01-19 13:14:15"
        }
    ]
}
删除强制替换词库
基本信息
Path：/correction_words/{correction_words_id}

**Method：**DELETE

接口描述： 删除指定ID的强制替换词库。

请求示例

curl -X DELETE '127.0.0.1:7100/correction_words/CW43DA3B90'
返回示例

{
    "status": "00000",
    "message": "success",
    "data": null
}
返回参数
名称	类型	说明
status	string	状态码
message	string	状态码描述
data	object	数据
更新强制替换词库
基本信息
Path：/correction_words/{correction_words_id}

**Method：**PUT

接口描述： 修改强制替换词库名称或强制替换词列表。

请求参数
Body（form-data）

名称	类型	是否必需	说明	默认值
name	string	否	强制替换词库的名称	无
file	file	否	强制替换词列表文件	无
返回参数
名称	类型	说明
status	string	状态码
message	string	状态码描述
data	object	强制替换词ID
请求示例

修改强制替换词库名称
curl -X PUT '127.0.0.1:7100/correction_words/CW43DA3B90' \
-F 'name=新名称'
修改强制替换列表文件
curl -X PUT '127.0.0.1:7100/correction_words/CW43DA3B90' \
-F 'file=@new.txt'
返回示例

{
    "status": "00000",
    "message": "success",
    "data": "CW43DA3B90"
}
获取强制替换词信息
基本信息
Path：/correction_words/{correction_words_id}

**Method：**GET

接口描述： 获取指定强制替换词库ID的信息。

返回参数
名称	类型	说明
status	string	状态码
message	string	状态码描述
data	object	数据
├─ id	string	强制替换词ID
├─ name	string	强制替换词名称
├─ lang_type	string	语种
├─ file	string	Base64编码的强制替换词文件（UTF-8 编码的 TXT 格式）
├─ amount	int	强制替换词数量
├─ update_time	string	最近修改时间
请求示例

curl -X GET '127.0.0.1:7100/correction_words/CW43DA3B90'
返回示例

{
    "status": "00000",
    "message": "success",
    "data": {
            "id": "CW43DA3B90",
            "name": "人名",
            "lang_type": "zh-cmn-Hans-CN",
            "file": "<Base64编码>",
            "amount": 12,
            "update_time": "2021-11-29 13:14:15"
        }
}
更新时间：2023-06-27