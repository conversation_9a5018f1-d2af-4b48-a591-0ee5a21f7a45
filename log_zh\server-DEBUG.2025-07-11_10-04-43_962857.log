2025-07-11 10:04:43.966 | INFO  | modules.config:init_logger :593 - 日志系统初始化成功, 配置: {'level': 'DEBUG', 'rotation': '1 day', 'retention': '7 days', 'compression': 'zip', 'max_file_size': '100 MB'}
2025-07-11 10:04:43.979 | INFO  | modules.monitoring:_start_health_server:308 - 健康检查服务器已启动, 端口: 8081
2025-07-11 10:04:43.981 | INFO  | modules.monitoring:__init__    :176 - 系统监控已启用, 内存检查间隔: 60秒
2025-07-11 10:04:43.981 | DEBUG | modules.config:init_monitoring:624 - 监控系统初始化成功
2025-07-11 10:04:43.982 | DEBUG | modules.config:init_session_pool:649 - 会话池初始化成功
2025-07-11 10:04:43.982 | DEBUG | modules.config:init_all_modules:667 - 所有模块初始化完成
2025-07-11 10:04:43.982 | INFO  | modules.asr_manager:__init__    :39 - 初始化统一ASR管理器, 支持语种: ['zh', 'en', 'ru', 'kk', 'kkin', 'ug']
2025-07-11 10:04:43.984 | INFO  | modules.asr_manager:load_models :59 - 单语种模式：加载语种 zh
2025-07-11 10:04:43.984 | DEBUG | modules.decoder:load_onnx   :62 - 加载模型: /ws/MODELS/online_onnx_zh, fp16: False, quant: False, device: cpu
2025-07-11 10:04:43.984 | DEBUG | modules.decoder:get_ep_list :58 - ['CPUExecutionProvider']
2025-07-11 10:04:44.473 | DEBUG | modules.decoder:load_onnx   :103 - ONNX Session配置: inter_op_threads=0, intra_op_threads=0, execution_mode=PARALLEL
2025-07-11 10:04:45.486 | DEBUG | modules.onnx_session_pool:register_model:153 - 模型 zh_encoder 注册成功，预创建会话: 41a84e4d
2025-07-11 10:04:45.487 | DEBUG | modules.decoder:load_onnx   :62 - 加载模型: /ws/MODELS/online_onnx_zh, fp16: False, quant: False, device: cpu
2025-07-11 10:04:45.487 | DEBUG | modules.decoder:get_ep_list :58 - ['CPUExecutionProvider']
2025-07-11 10:04:45.493 | DEBUG | modules.decoder:load_onnx   :103 - ONNX Session配置: inter_op_threads=0, intra_op_threads=0, execution_mode=PARALLEL
2025-07-11 10:04:45.513 | DEBUG | modules.onnx_session_pool:register_model:153 - 模型 zh_ctc 注册成功，预创建会话: 578779c7
2025-07-11 10:04:45.514 | DEBUG | modules.asr_manager:_load_single_language:104 - 语种 zh 模型已注册到会话池
2025-07-11 10:04:45.520 | DEBUG | modules.config:build_model_config:205 - 构建的模型配置: {'feat_configs': {'feat_type': 'fbank', 'num_mel_bins': 80, 'frame_length': 25, 'frame_shift': 10, 'dither': 1.0, 'n_fft': 400, 'hop_length': 160}, 'batch': 1, 'chunk_size': 16, 'left_chunks': 16, 'reverse_weight': 0.3, 'decoding_window': 67, 'subsampling_rate': 4, 'right_context': 6, 'context_list_path': '/ws/MODELS/online_onnx_zh/hotwords.txt', 'context_graph_score': 40, 'output_size': 512, 'num_blocks': 12, 'cnn_module_kernel': 15, 'head': 8, 'feature_size': 80, 'vocab_size': 5538}
2025-07-11 10:04:45.520 | DEBUG | modules.symbol_table:load_dict   :30 - 加载词表: /ws/MODELS/online_onnx_zh/units.txt
2025-07-11 10:04:45.527 | DEBUG | modules.symbol_table:__init__    :24 - 启用后处理: 转全小写字母
2025-07-11 10:04:45.528 | INFO  | modules.asr_manager:load_models :72 - 模型加载完成：成功 1/1 个语种
2025-07-11 10:04:45.528 | INFO  | server :lifespan    :109 - 单语种模式 LID功能不可用
2025-07-11 10:04:45.529 | INFO  | server :lifespan    :114 - Server start, init manager, LID_MANAGER, ASR_MANAGER


2025-07-11 10:04:50.561 | DEBUG | server :websocket_endpoint:231 - client_id:000 - 开始初始化连接
2025-07-11 10:04:50.562 | INFO  | server :websocket_endpoint:237 - client_id:000 - >>> [请求] 新客户连接，当前活跃连接数: 1
2025-07-11 10:04:50.564 | DEBUG | server :websocket_endpoint:231 - client_id:111 - 开始初始化连接
2025-07-11 10:04:50.564 | INFO  | server :websocket_endpoint:237 - client_id:111 - >>> [请求] 新客户连接，当前活跃连接数: 2
2025-07-11 10:04:50.974 | INFO  | modules.connect:on_check    :468 - client_id:000 - 设置自定义分隔符: "，"
2025-07-11 10:04:50.974 | INFO  | modules.connect:_init_decoder:141 - client_id:000 - 初始化解码器, 使用默认语种: zh
2025-07-11 10:04:51.015 | DEBUG | modules.decoder:__init__    :427 - 自定义分隔符: ，
2025-07-11 10:04:51.016 | INFO  | modules.connect:_init_decoder:148 - client_id:000 - 解码器初始化完成, 使用分隔符: "，"
2025-07-11 10:04:51.021 | DEBUG | modules.connect:on_check    :626 - client_id:000 - >>> [解析] 第0个数据包, 累计帧数: 38
2025-07-11 10:04:51.022 | DEBUG | modules.connect:on_decode   :992 - client_id:000 - 所需帧数: 67, 目前帧数: 38, 继续等待数据包
2025-07-11 10:04:51.023 | INFO  | modules.connect:on_check    :468 - client_id:111 - 设置自定义分隔符: "，"
2025-07-11 10:04:51.023 | INFO  | modules.connect:_init_decoder:141 - client_id:111 - 初始化解码器, 使用默认语种: zh
2025-07-11 10:04:51.024 | DEBUG | modules.decoder:__init__    :427 - 自定义分隔符: ，
2025-07-11 10:04:51.024 | INFO  | modules.connect:_init_decoder:148 - client_id:111 - 解码器初始化完成, 使用分隔符: "，"
2025-07-11 10:04:51.027 | DEBUG | modules.connect:on_check    :626 - client_id:111 - >>> [解析] 第0个数据包, 累计帧数: 38
2025-07-11 10:04:51.027 | DEBUG | modules.connect:on_decode   :992 - client_id:111 - 所需帧数: 67, 目前帧数: 38, 继续等待数据包
2025-07-11 10:04:51.386 | DEBUG | modules.connect:on_check    :626 - client_id:000 - >>> [解析] 第1个数据包, 累计帧数: 76
2025-07-11 10:04:51.387 | DEBUG | modules.connect:on_decode   :959 - client_id:000 - 所需帧数: 67, 目前帧数: 76, 开始解码
2025-07-11 10:04:51.388 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 000 获取现有会话: zh_encoder - 41a84e4d, 池大小: 1
2025-07-11 10:04:51.461 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_encoder - 41a84e4d, 当前可用: 1/1
2025-07-11 10:04:51.461 | DEBUG | modules.decoder:decode_chunk:483 - client_id:000 - 当前推理累计时点: 16
2025-07-11 10:04:51.462 | DEBUG | modules.decoder:decode_chunk:484 - client_id:000 - ctc 搜索起始时点: 0 搜索片段长度: tensor([16])
2025-07-11 10:04:51.462 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 000 获取现有会话: zh_ctc - 578779c7, 池大小: 1
2025-07-11 10:04:51.463 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_ctc - 578779c7, 当前可用: 1/1
2025-07-11 10:04:51.483 | DEBUG | modules.decoder:search      :371 - ctc token&time: [(1522, 12)]
2025-07-11 10:04:51.483 | DEBUG | modules.decoder:decode_chunk:495 - ctc result: "很"
2025-07-11 10:04:51.483 | DEBUG | modules.connect:on_result   :428 - client_id:000 - <<< [发送] 第0个数据包, 更新识别结果: "很"
2025-07-11 10:04:51.517 | DEBUG | modules.connect:on_check    :626 - client_id:111 - >>> [解析] 第1个数据包, 累计帧数: 76
2025-07-11 10:04:51.518 | DEBUG | modules.connect:on_decode   :959 - client_id:111 - 所需帧数: 67, 目前帧数: 76, 开始解码
2025-07-11 10:04:51.518 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 111 获取现有会话: zh_encoder - 41a84e4d, 池大小: 1
2025-07-11 10:04:51.573 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_encoder - 41a84e4d, 当前可用: 1/1
2025-07-11 10:04:51.573 | DEBUG | modules.decoder:decode_chunk:483 - client_id:111 - 当前推理累计时点: 16
2025-07-11 10:04:51.573 | DEBUG | modules.decoder:decode_chunk:484 - client_id:111 - ctc 搜索起始时点: 0 搜索片段长度: tensor([16])
2025-07-11 10:04:51.573 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 111 获取现有会话: zh_ctc - 578779c7, 池大小: 1
2025-07-11 10:04:51.575 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_ctc - 578779c7, 当前可用: 1/1
2025-07-11 10:04:51.593 | DEBUG | modules.decoder:search      :371 - ctc token&time: [(1720, 7), (1664, 11)]
2025-07-11 10:04:51.594 | DEBUG | modules.decoder:decode_chunk:495 - ctc result: "我想"
2025-07-11 10:04:51.594 | DEBUG | modules.connect:on_result   :428 - client_id:111 - <<< [发送] 第0个数据包, 更新识别结果: "我想"
2025-07-11 10:04:51.793 | DEBUG | modules.connect:on_check    :626 - client_id:000 - >>> [解析] 第2个数据包, 累计帧数: 114
2025-07-11 10:04:51.793 | DEBUG | modules.connect:on_decode   :959 - client_id:000 - 所需帧数: 67, 目前帧数: 114, 开始解码
2025-07-11 10:04:51.794 | DEBUG | modules.connect:on_decode   :988 - client_id:000 - chunk 完成解码, 识别结果不变
2025-07-11 10:04:51.800 | DEBUG | modules.connect:on_check    :626 - client_id:111 - >>> [解析] 第2个数据包, 累计帧数: 114
2025-07-11 10:04:51.800 | DEBUG | modules.connect:on_decode   :959 - client_id:111 - 所需帧数: 67, 目前帧数: 114, 开始解码
2025-07-11 10:04:51.801 | DEBUG | modules.connect:on_decode   :988 - client_id:111 - chunk 完成解码, 识别结果不变
2025-07-11 10:04:52.222 | DEBUG | modules.connect:on_check    :626 - client_id:000 - >>> [解析] 第3个数据包, 累计帧数: 152
2025-07-11 10:04:52.222 | DEBUG | modules.connect:on_decode   :959 - client_id:000 - 所需帧数: 67, 目前帧数: 152, 开始解码
2025-07-11 10:04:52.222 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 000 获取现有会话: zh_encoder - 41a84e4d, 池大小: 1
2025-07-11 10:04:52.280 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_encoder - 41a84e4d, 当前可用: 1/1
2025-07-11 10:04:52.281 | DEBUG | modules.decoder:decode_chunk:483 - client_id:000 - 当前推理累计时点: 32
2025-07-11 10:04:52.281 | DEBUG | modules.decoder:decode_chunk:484 - client_id:000 - ctc 搜索起始时点: 16 搜索片段长度: tensor([16])
2025-07-11 10:04:52.281 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 000 获取现有会话: zh_ctc - 578779c7, 池大小: 1
2025-07-11 10:04:52.282 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_ctc - 578779c7, 当前可用: 1/1
2025-07-11 10:04:52.345 | DEBUG | modules.decoder:search      :371 - ctc token&time: [(1522, 12), (5380, 17), (364, 22), (2181, 26)]
2025-07-11 10:04:52.345 | DEBUG | modules.decoder:decode_chunk:495 - ctc result: "很高兴来"
2025-07-11 10:04:52.346 | DEBUG | modules.connect:on_result   :428 - client_id:000 - <<< [发送] 第1个数据包, 更新识别结果: "很高兴来"
2025-07-11 10:04:52.349 | DEBUG | modules.connect:on_check    :626 - client_id:111 - >>> [解析] 第3个数据包, 累计帧数: 152
2025-07-11 10:04:52.349 | DEBUG | modules.connect:on_decode   :959 - client_id:111 - 所需帧数: 67, 目前帧数: 152, 开始解码
2025-07-11 10:04:52.349 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 111 获取现有会话: zh_encoder - 41a84e4d, 池大小: 1
2025-07-11 10:04:52.405 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_encoder - 41a84e4d, 当前可用: 1/1
2025-07-11 10:04:52.406 | DEBUG | modules.decoder:decode_chunk:483 - client_id:111 - 当前推理累计时点: 32
2025-07-11 10:04:52.406 | DEBUG | modules.decoder:decode_chunk:484 - client_id:111 - ctc 搜索起始时点: 16 搜索片段长度: tensor([16])
2025-07-11 10:04:52.406 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 111 获取现有会话: zh_ctc - 578779c7, 池大小: 1
2025-07-11 10:04:52.407 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_ctc - 578779c7, 当前可用: 1/1
2025-07-11 10:04:52.460 | DEBUG | modules.decoder:search      :371 - ctc token&time: [(1720, 7), (1664, 11), (928, 17), (3010, 22), (1586, 27)]
2025-07-11 10:04:52.461 | DEBUG | modules.decoder:decode_chunk:495 - ctc result: "我想在理性"
2025-07-11 10:04:52.461 | DEBUG | modules.connect:on_result   :428 - client_id:111 - <<< [发送] 第1个数据包, 更新识别结果: "我想在理性"
2025-07-11 10:04:52.608 | DEBUG | modules.connect:on_check    :626 - client_id:000 - >>> [解析] 第4个数据包, 累计帧数: 190
2025-07-11 10:04:52.608 | DEBUG | modules.connect:on_decode   :959 - client_id:000 - 所需帧数: 67, 目前帧数: 190, 开始解码
2025-07-11 10:04:52.609 | DEBUG | modules.connect:on_decode   :988 - client_id:000 - chunk 完成解码, 识别结果不变
2025-07-11 10:04:52.615 | DEBUG | modules.connect:on_check    :626 - client_id:111 - >>> [解析] 第4个数据包, 累计帧数: 190
2025-07-11 10:04:52.615 | DEBUG | modules.connect:on_decode   :959 - client_id:111 - 所需帧数: 67, 目前帧数: 190, 开始解码
2025-07-11 10:04:52.616 | DEBUG | modules.connect:on_decode   :988 - client_id:111 - chunk 完成解码, 识别结果不变
2025-07-11 10:04:53.015 | DEBUG | modules.connect:on_check    :626 - client_id:000 - >>> [解析] 第5个数据包, 累计帧数: 228
2025-07-11 10:04:53.015 | DEBUG | modules.connect:on_decode   :959 - client_id:000 - 所需帧数: 67, 目前帧数: 228, 开始解码
2025-07-11 10:04:53.016 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 000 获取现有会话: zh_encoder - 41a84e4d, 池大小: 1
2025-07-11 10:04:53.084 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_encoder - 41a84e4d, 当前可用: 1/1
2025-07-11 10:04:53.084 | DEBUG | modules.decoder:decode_chunk:483 - client_id:000 - 当前推理累计时点: 48
2025-07-11 10:04:53.084 | DEBUG | modules.decoder:decode_chunk:484 - client_id:000 - ctc 搜索起始时点: 32 搜索片段长度: tensor([16])
2025-07-11 10:04:53.085 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 000 获取现有会话: zh_ctc - 578779c7, 池大小: 1
2025-07-11 10:04:53.085 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_ctc - 578779c7, 当前可用: 1/1
2025-07-11 10:04:53.148 | DEBUG | modules.decoder:search      :371 - ctc token&time: [(1522, 12), (5380, 17), (364, 22), (2181, 26), (448, 32), (4765, 35), (4935, 40)]
2025-07-11 10:04:53.148 | DEBUG | modules.decoder:decode_chunk:495 - ctc result: "很高兴来到这里"
2025-07-11 10:04:53.148 | DEBUG | modules.connect:on_result   :428 - client_id:000 - <<< [发送] 第2个数据包, 更新识别结果: "很高兴来到这里"
2025-07-11 10:04:53.151 | DEBUG | modules.connect:on_check    :626 - client_id:111 - >>> [解析] 第5个数据包, 累计帧数: 228
2025-07-11 10:04:53.151 | DEBUG | modules.connect:on_decode   :959 - client_id:111 - 所需帧数: 67, 目前帧数: 228, 开始解码
2025-07-11 10:04:53.152 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 111 获取现有会话: zh_encoder - 41a84e4d, 池大小: 1
2025-07-11 10:04:53.209 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_encoder - 41a84e4d, 当前可用: 1/1
2025-07-11 10:04:53.209 | DEBUG | modules.decoder:decode_chunk:483 - client_id:111 - 当前推理累计时点: 48
2025-07-11 10:04:53.209 | DEBUG | modules.decoder:decode_chunk:484 - client_id:111 - ctc 搜索起始时点: 32 搜索片段长度: tensor([16])
2025-07-11 10:04:53.210 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 111 获取现有会话: zh_ctc - 578779c7, 池大小: 1
2025-07-11 10:04:53.210 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_ctc - 578779c7, 当前可用: 1/1
2025-07-11 10:04:53.287 | DEBUG | modules.decoder:search      :371 - ctc token&time: [(1720, 7), (1664, 11), (928, 17), (3010, 22), (1586, 27), (753, 43)]
2025-07-11 10:04:53.288 | DEBUG | modules.decoder:decode_chunk:495 - ctc result: "我想在理性，哲"
2025-07-11 10:04:53.289 | DEBUG | modules.connect:on_result   :428 - client_id:111 - <<< [发送] 第2个数据包, 更新识别结果: "我想在理性，哲"
2025-07-11 10:04:53.426 | DEBUG | modules.connect:on_check    :626 - client_id:000 - >>> [解析] 第6个数据包, 累计帧数: 266
2025-07-11 10:04:53.426 | DEBUG | modules.connect:on_decode   :959 - client_id:000 - 所需帧数: 67, 目前帧数: 266, 开始解码
2025-07-11 10:04:53.428 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 000 获取现有会话: zh_encoder - 41a84e4d, 池大小: 1
2025-07-11 10:04:53.488 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_encoder - 41a84e4d, 当前可用: 1/1
2025-07-11 10:04:53.489 | DEBUG | modules.decoder:decode_chunk:483 - client_id:000 - 当前推理累计时点: 64
2025-07-11 10:04:53.489 | DEBUG | modules.decoder:decode_chunk:484 - client_id:000 - ctc 搜索起始时点: 48 搜索片段长度: tensor([16])
2025-07-11 10:04:53.489 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 000 获取现有会话: zh_ctc - 578779c7, 池大小: 1
2025-07-11 10:04:53.490 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_ctc - 578779c7, 当前可用: 1/1
2025-07-11 10:04:53.545 | DEBUG | modules.decoder:search      :371 - ctc token&time: [(1522, 12), (5380, 17), (364, 22), (2181, 26), (448, 32), (4765, 35), (4935, 40), (39, 57), (1041, 62)]
2025-07-11 10:04:53.546 | DEBUG | modules.decoder:decode_chunk:495 - ctc result: "很高兴来到这里，与大"
2025-07-11 10:04:53.546 | DEBUG | modules.connect:on_result   :428 - client_id:000 - <<< [发送] 第3个数据包, 更新识别结果: "很高兴来到这里，与大"
2025-07-11 10:04:53.549 | DEBUG | modules.connect:on_check    :626 - client_id:111 - >>> [解析] 第6个数据包, 累计帧数: 266
2025-07-11 10:04:53.549 | DEBUG | modules.connect:on_decode   :959 - client_id:111 - 所需帧数: 67, 目前帧数: 266, 开始解码
2025-07-11 10:04:53.549 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 111 获取现有会话: zh_encoder - 41a84e4d, 池大小: 1
2025-07-11 10:04:53.605 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_encoder - 41a84e4d, 当前可用: 1/1
2025-07-11 10:04:53.605 | DEBUG | modules.decoder:decode_chunk:483 - client_id:111 - 当前推理累计时点: 64
2025-07-11 10:04:53.606 | DEBUG | modules.decoder:decode_chunk:484 - client_id:111 - ctc 搜索起始时点: 48 搜索片段长度: tensor([16])
2025-07-11 10:04:53.606 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 111 获取现有会话: zh_ctc - 578779c7, 池大小: 1
2025-07-11 10:04:53.606 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_ctc - 578779c7, 当前可用: 1/1
2025-07-11 10:04:53.661 | DEBUG | modules.decoder:search      :371 - ctc token&time: [(1720, 7), (1664, 11), (928, 17), (3010, 22), (1586, 27), (753, 43), (1199, 49)]
2025-07-11 10:04:53.661 | DEBUG | modules.decoder:decode_chunk:495 - ctc result: "我想在理性，哲学"
2025-07-11 10:04:53.661 | DEBUG | modules.connect:on_result   :428 - client_id:111 - <<< [发送] 第3个数据包, 更新识别结果: "我想在理性，哲学"
2025-07-11 10:04:53.831 | DEBUG | modules.connect:on_check    :626 - client_id:000 - >>> [解析] 第7个数据包, 累计帧数: 304
2025-07-11 10:04:53.831 | DEBUG | modules.connect:on_decode   :959 - client_id:000 - 所需帧数: 67, 目前帧数: 304, 开始解码
2025-07-11 10:04:53.832 | DEBUG | modules.connect:on_decode   :988 - client_id:000 - chunk 完成解码, 识别结果不变
2025-07-11 10:04:53.837 | DEBUG | modules.connect:on_check    :626 - client_id:111 - >>> [解析] 第7个数据包, 累计帧数: 304
2025-07-11 10:04:53.837 | DEBUG | modules.connect:on_decode   :959 - client_id:111 - 所需帧数: 67, 目前帧数: 304, 开始解码
2025-07-11 10:04:53.838 | DEBUG | modules.connect:on_decode   :988 - client_id:111 - chunk 完成解码, 识别结果不变
2025-07-11 10:04:54.238 | DEBUG | modules.connect:on_check    :626 - client_id:000 - >>> [解析] 第8个数据包, 累计帧数: 342
2025-07-11 10:04:54.238 | DEBUG | modules.connect:on_decode   :959 - client_id:000 - 所需帧数: 67, 目前帧数: 342, 开始解码
2025-07-11 10:04:54.239 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 000 获取现有会话: zh_encoder - 41a84e4d, 池大小: 1
2025-07-11 10:04:54.301 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_encoder - 41a84e4d, 当前可用: 1/1
2025-07-11 10:04:54.302 | DEBUG | modules.decoder:decode_chunk:483 - client_id:000 - 当前推理累计时点: 80
2025-07-11 10:04:54.302 | DEBUG | modules.decoder:decode_chunk:484 - client_id:000 - ctc 搜索起始时点: 64 搜索片段长度: tensor([16])
2025-07-11 10:04:54.302 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 000 获取现有会话: zh_ctc - 578779c7, 池大小: 1
2025-07-11 10:04:54.303 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_ctc - 578779c7, 当前可用: 1/1
2025-07-11 10:04:54.350 | DEBUG | modules.decoder:search      :371 - ctc token&time: [(1522, 12), (5380, 17), (364, 22), (2181, 26), (448, 32), (4765, 35), (4935, 40), (39, 57), (1041, 62), (1241, 66), (1899, 71), (4416, 76)]
2025-07-11 10:04:54.350 | DEBUG | modules.decoder:decode_chunk:495 - ctc result: "很高兴来到这里，与大家探讨"
2025-07-11 10:04:54.350 | DEBUG | modules.connect:on_result   :428 - client_id:000 - <<< [发送] 第4个数据包, 更新识别结果: "很高兴来到这里，与大家探讨"
2025-07-11 10:04:54.353 | DEBUG | modules.connect:on_check    :626 - client_id:111 - >>> [解析] 第8个数据包, 累计帧数: 342
2025-07-11 10:04:54.353 | DEBUG | modules.connect:on_decode   :959 - client_id:111 - 所需帧数: 67, 目前帧数: 342, 开始解码
2025-07-11 10:04:54.354 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 111 获取现有会话: zh_encoder - 41a84e4d, 池大小: 1
2025-07-11 10:04:54.406 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_encoder - 41a84e4d, 当前可用: 1/1
2025-07-11 10:04:54.407 | DEBUG | modules.decoder:decode_chunk:483 - client_id:111 - 当前推理累计时点: 80
2025-07-11 10:04:54.407 | DEBUG | modules.decoder:decode_chunk:484 - client_id:111 - ctc 搜索起始时点: 64 搜索片段长度: tensor([16])
2025-07-11 10:04:54.407 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 111 获取现有会话: zh_ctc - 578779c7, 池大小: 1
2025-07-11 10:04:54.408 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_ctc - 578779c7, 当前可用: 1/1
2025-07-11 10:04:54.451 | DEBUG | modules.decoder:search      :371 - ctc token&time: [(1720, 7), (1664, 11), (928, 17), (3010, 22), (1586, 27), (753, 43), (1199, 49), (157, 64), (589, 68), (1542, 73), (3010, 77)]
2025-07-11 10:04:54.451 | DEBUG | modules.decoder:decode_chunk:495 - ctc result: "我想在理性，哲学，以及心理"
2025-07-11 10:04:54.451 | DEBUG | modules.connect:on_result   :428 - client_id:111 - <<< [发送] 第4个数据包, 更新识别结果: "我想在理性，哲学，以及心理"
2025-07-11 10:04:54.646 | DEBUG | modules.connect:on_check    :626 - client_id:000 - >>> [解析] 第9个数据包, 累计帧数: 380
2025-07-11 10:04:54.646 | DEBUG | modules.connect:on_decode   :959 - client_id:000 - 所需帧数: 67, 目前帧数: 380, 开始解码
2025-07-11 10:04:54.647 | DEBUG | modules.connect:on_decode   :988 - client_id:000 - chunk 完成解码, 识别结果不变
2025-07-11 10:04:54.653 | DEBUG | modules.connect:on_check    :626 - client_id:111 - >>> [解析] 第9个数据包, 累计帧数: 380
2025-07-11 10:04:54.653 | DEBUG | modules.connect:on_decode   :959 - client_id:111 - 所需帧数: 67, 目前帧数: 380, 开始解码
2025-07-11 10:04:54.653 | DEBUG | modules.connect:on_decode   :988 - client_id:111 - chunk 完成解码, 识别结果不变
2025-07-11 10:04:55.053 | DEBUG | modules.connect:on_check    :626 - client_id:000 - >>> [解析] 第10个数据包, 累计帧数: 418
2025-07-11 10:04:55.054 | DEBUG | modules.connect:on_decode   :959 - client_id:000 - 所需帧数: 67, 目前帧数: 418, 开始解码
2025-07-11 10:04:55.055 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 000 获取现有会话: zh_encoder - 41a84e4d, 池大小: 1
2025-07-11 10:04:55.119 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_encoder - 41a84e4d, 当前可用: 1/1
2025-07-11 10:04:55.120 | DEBUG | modules.decoder:decode_chunk:483 - client_id:000 - 当前推理累计时点: 96
2025-07-11 10:04:55.120 | DEBUG | modules.decoder:decode_chunk:484 - client_id:000 - ctc 搜索起始时点: 80 搜索片段长度: tensor([16])
2025-07-11 10:04:55.120 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 000 获取现有会话: zh_ctc - 578779c7, 池大小: 1
2025-07-11 10:04:55.121 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_ctc - 578779c7, 当前可用: 1/1
2025-07-11 10:04:55.161 | DEBUG | modules.decoder:search      :371 - ctc token&time: [(1522, 12), (5380, 17), (364, 22), (2181, 26), (448, 32), (4765, 35), (4935, 40), (39, 57), (1041, 62), (1241, 66), (1899, 71), (4416, 76), (1720, 81), (4765, 85), (67, 90)]
2025-07-11 10:04:55.161 | DEBUG | modules.decoder:decode_chunk:495 - ctc result: "很高兴来到这里，与大家探讨我这为"
2025-07-11 10:04:55.161 | DEBUG | modules.connect:on_result   :428 - client_id:000 - <<< [发送] 第5个数据包, 更新识别结果: "很高兴来到这里，与大家探讨我这为"
2025-07-11 10:04:55.164 | DEBUG | modules.connect:on_check    :626 - client_id:111 - >>> [解析] 第10个数据包, 累计帧数: 418
2025-07-11 10:04:55.164 | DEBUG | modules.connect:on_decode   :959 - client_id:111 - 所需帧数: 67, 目前帧数: 418, 开始解码
2025-07-11 10:04:55.164 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 111 获取现有会话: zh_encoder - 41a84e4d, 池大小: 1
2025-07-11 10:04:55.221 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_encoder - 41a84e4d, 当前可用: 1/1
2025-07-11 10:04:55.221 | DEBUG | modules.decoder:decode_chunk:483 - client_id:111 - 当前推理累计时点: 96
2025-07-11 10:04:55.222 | DEBUG | modules.decoder:decode_chunk:484 - client_id:111 - ctc 搜索起始时点: 80 搜索片段长度: tensor([16])
2025-07-11 10:04:55.222 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 111 获取现有会话: zh_ctc - 578779c7, 池大小: 1
2025-07-11 10:04:55.223 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_ctc - 578779c7, 当前可用: 1/1
2025-07-11 10:04:55.268 | DEBUG | modules.decoder:search      :371 - ctc token&time: [(1720, 7), (1664, 11), (928, 17), (3010, 22), (1586, 27), (753, 43), (1199, 49), (157, 64), (589, 68), (1542, 73), (3010, 77), (1199, 81), (3185, 85), (1299, 89), (5214, 94)]
2025-07-11 10:04:55.268 | DEBUG | modules.decoder:decode_chunk:495 - ctc result: "我想在理性，哲学，以及心理学的层面"
2025-07-11 10:04:55.268 | DEBUG | modules.connect:on_result   :428 - client_id:111 - <<< [发送] 第5个数据包, 更新识别结果: "我想在理性，哲学，以及心理学的层面"
2025-07-11 10:04:55.462 | DEBUG | modules.connect:on_check    :626 - client_id:000 - >>> [解析] 第11个数据包, 累计帧数: 456
2025-07-11 10:04:55.462 | DEBUG | modules.connect:on_decode   :959 - client_id:000 - 所需帧数: 67, 目前帧数: 456, 开始解码
2025-07-11 10:04:55.463 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 000 获取现有会话: zh_encoder - 41a84e4d, 池大小: 1
2025-07-11 10:04:55.531 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_encoder - 41a84e4d, 当前可用: 1/1
2025-07-11 10:04:55.533 | DEBUG | modules.decoder:decode_chunk:483 - client_id:000 - 当前推理累计时点: 112
2025-07-11 10:04:55.533 | DEBUG | modules.decoder:decode_chunk:484 - client_id:000 - ctc 搜索起始时点: 96 搜索片段长度: tensor([16])
2025-07-11 10:04:55.533 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 000 获取现有会话: zh_ctc - 578779c7, 池大小: 1
2025-07-11 10:04:55.547 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_ctc - 578779c7, 当前可用: 1/1
2025-07-11 10:04:55.591 | DEBUG | modules.decoder:search      :371 - ctc token&time: [(1522, 12), (5380, 17), (364, 22), (2181, 26), (448, 32), (4765, 35), (4935, 40), (39, 57), (1041, 62), (1241, 66), (1899, 71), (4416, 76), (1720, 81), (4765, 85), (67, 90), (363, 96), (1542, 99), (3185, 104), (30, 108)]
2025-07-11 10:04:55.591 | DEBUG | modules.decoder:decode_chunk:495 - ctc result: "很高兴来到这里，与大家探讨我这为关心的一"
2025-07-11 10:04:55.592 | DEBUG | modules.connect:on_result   :428 - client_id:000 - <<< [发送] 第6个数据包, 更新识别结果: "很高兴来到这里，与大家探讨我这为关心的一"
2025-07-11 10:04:55.598 | DEBUG | modules.connect:on_check    :626 - client_id:111 - >>> [解析] 第11个数据包, 累计帧数: 456
2025-07-11 10:04:55.598 | DEBUG | modules.connect:on_decode   :959 - client_id:111 - 所需帧数: 67, 目前帧数: 456, 开始解码
2025-07-11 10:04:55.599 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 111 获取现有会话: zh_encoder - 41a84e4d, 池大小: 1
2025-07-11 10:04:55.696 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_encoder - 41a84e4d, 当前可用: 1/1
2025-07-11 10:04:55.697 | DEBUG | modules.decoder:decode_chunk:483 - client_id:111 - 当前推理累计时点: 112
2025-07-11 10:04:55.697 | DEBUG | modules.decoder:decode_chunk:484 - client_id:111 - ctc 搜索起始时点: 96 搜索片段长度: tensor([16])
2025-07-11 10:04:55.697 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 111 获取现有会话: zh_ctc - 578779c7, 池大小: 1
2025-07-11 10:04:55.699 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_ctc - 578779c7, 当前可用: 1/1
2025-07-11 10:04:55.743 | DEBUG | modules.decoder:search      :371 - ctc token&time: [(1720, 7), (1664, 11), (928, 17), (3010, 22), (1586, 27), (753, 43), (1199, 49), (157, 64), (589, 68), (1542, 73), (3010, 77), (1199, 81), (3185, 85), (1299, 89), (5214, 94), (36, 99), (3010, 104), (4399, 108)]
2025-07-11 10:04:55.744 | DEBUG | modules.decoder:decode_chunk:495 - ctc result: "我想在理性，哲学，以及心理学的层面上理解"
2025-07-11 10:04:55.744 | DEBUG | modules.connect:on_result   :428 - client_id:111 - <<< [发送] 第6个数据包, 更新识别结果: "我想在理性，哲学，以及心理学的层面上理解"
2025-07-11 10:04:55.872 | DEBUG | modules.connect:on_check    :626 - client_id:000 - >>> [解析] 第12个数据包, 累计帧数: 494
2025-07-11 10:04:55.872 | DEBUG | modules.connect:on_decode   :959 - client_id:000 - 所需帧数: 67, 目前帧数: 494, 开始解码
2025-07-11 10:04:55.874 | DEBUG | modules.connect:on_decode   :988 - client_id:000 - chunk 完成解码, 识别结果不变
2025-07-11 10:04:55.879 | DEBUG | modules.connect:on_check    :626 - client_id:111 - >>> [解析] 第12个数据包, 累计帧数: 494
2025-07-11 10:04:55.879 | DEBUG | modules.connect:on_decode   :959 - client_id:111 - 所需帧数: 67, 目前帧数: 494, 开始解码
2025-07-11 10:04:55.879 | DEBUG | modules.connect:on_decode   :988 - client_id:111 - chunk 完成解码, 识别结果不变
2025-07-11 10:04:56.292 | DEBUG | modules.connect:on_check    :626 - client_id:000 - >>> [解析] 第13个数据包, 累计帧数: 532
2025-07-11 10:04:56.293 | DEBUG | modules.connect:on_decode   :959 - client_id:000 - 所需帧数: 67, 目前帧数: 532, 开始解码
2025-07-11 10:04:56.293 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 000 获取现有会话: zh_encoder - 41a84e4d, 池大小: 1
2025-07-11 10:04:56.366 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_encoder - 41a84e4d, 当前可用: 1/1
2025-07-11 10:04:56.367 | DEBUG | modules.decoder:decode_chunk:483 - client_id:000 - 当前推理累计时点: 128
2025-07-11 10:04:56.367 | DEBUG | modules.decoder:decode_chunk:484 - client_id:000 - ctc 搜索起始时点: 112 搜索片段长度: tensor([16])
2025-07-11 10:04:56.367 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 000 获取现有会话: zh_ctc - 578779c7, 池大小: 1
2025-07-11 10:04:56.368 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_ctc - 578779c7, 当前可用: 1/1
2025-07-11 10:04:56.414 | DEBUG | modules.decoder:search      :371 - ctc token&time: [(1522, 12), (5380, 17), (364, 22), (2181, 26), (448, 32), (4765, 35), (4935, 40), (39, 57), (1041, 62), (1241, 66), (1899, 71), (4416, 76), (1720, 81), (4765, 85), (67, 90), (363, 96), (1542, 99), (3185, 104), (30, 108), (58, 112), (68, 116), (5268, 121)]
2025-07-11 10:04:56.414 | DEBUG | modules.decoder:decode_chunk:495 - ctc result: "很高兴来到这里，与大家探讨我这为关心的一个主题"
2025-07-11 10:04:56.414 | DEBUG | modules.connect:on_result   :428 - client_id:000 - <<< [发送] 第7个数据包, 更新识别结果: "很高兴来到这里，与大家探讨我这为关心的一个主题"
2025-07-11 10:04:56.417 | DEBUG | modules.connect:on_check    :626 - client_id:111 - >>> [解析] 第13个数据包, 累计帧数: 532
2025-07-11 10:04:56.417 | DEBUG | modules.connect:on_decode   :959 - client_id:111 - 所需帧数: 67, 目前帧数: 532, 开始解码
2025-07-11 10:04:56.418 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 111 获取现有会话: zh_encoder - 41a84e4d, 池大小: 1
2025-07-11 10:04:56.477 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_encoder - 41a84e4d, 当前可用: 1/1
2025-07-11 10:04:56.478 | DEBUG | modules.decoder:decode_chunk:483 - client_id:111 - 当前推理累计时点: 128
2025-07-11 10:04:56.478 | DEBUG | modules.decoder:decode_chunk:484 - client_id:111 - ctc 搜索起始时点: 112 搜索片段长度: tensor([16])
2025-07-11 10:04:56.479 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 111 获取现有会话: zh_ctc - 578779c7, 池大小: 1
2025-07-11 10:04:56.480 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_ctc - 578779c7, 当前可用: 1/1
2025-07-11 10:04:56.519 | DEBUG | modules.decoder:search      :371 - ctc token&time: [(1720, 7), (1664, 11), (928, 17), (3010, 22), (1586, 27), (753, 43), (1199, 49), (157, 64), (589, 68), (1542, 73), (3010, 77), (1199, 81), (3185, 85), (1299, 89), (5214, 94), (36, 99), (3010, 104), (4399, 108)]
2025-07-11 10:04:56.520 | DEBUG | modules.decoder:decode_chunk:495 - ctc result: "我想在理性，哲学，以及心理学的层面上理解"
2025-07-11 10:04:56.520 | DEBUG | modules.connect:on_decode   :988 - client_id:111 - chunk 完成解码, 识别结果不变
2025-07-11 10:04:56.687 | DEBUG | modules.connect:on_check    :626 - client_id:000 - >>> [解析] 第14个数据包, 累计帧数: 570
2025-07-11 10:04:56.687 | DEBUG | modules.connect:on_decode   :959 - client_id:000 - 所需帧数: 67, 目前帧数: 570, 开始解码
2025-07-11 10:04:56.688 | DEBUG | modules.connect:on_decode   :988 - client_id:000 - chunk 完成解码, 识别结果不变
2025-07-11 10:04:56.695 | DEBUG | modules.connect:on_check    :626 - client_id:111 - >>> [解析] 第14个数据包, 累计帧数: 570
2025-07-11 10:04:56.695 | DEBUG | modules.connect:on_decode   :959 - client_id:111 - 所需帧数: 67, 目前帧数: 570, 开始解码
2025-07-11 10:04:56.696 | DEBUG | modules.connect:on_decode   :988 - client_id:111 - chunk 完成解码, 识别结果不变
2025-07-11 10:04:57.093 | DEBUG | modules.connect:on_check    :626 - client_id:000 - >>> [解析] 第15个数据包, 累计帧数: 608
2025-07-11 10:04:57.094 | DEBUG | modules.connect:on_decode   :959 - client_id:000 - 所需帧数: 67, 目前帧数: 608, 开始解码
2025-07-11 10:04:57.094 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 000 获取现有会话: zh_encoder - 41a84e4d, 池大小: 1
2025-07-11 10:04:57.163 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_encoder - 41a84e4d, 当前可用: 1/1
2025-07-11 10:04:57.164 | DEBUG | modules.decoder:decode_chunk:483 - client_id:000 - 当前推理累计时点: 144
2025-07-11 10:04:57.164 | DEBUG | modules.decoder:decode_chunk:484 - client_id:000 - ctc 搜索起始时点: 128 搜索片段长度: tensor([16])
2025-07-11 10:04:57.164 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 000 获取现有会话: zh_ctc - 578779c7, 池大小: 1
2025-07-11 10:04:57.165 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_ctc - 578779c7, 当前可用: 1/1
2025-07-11 10:04:57.205 | DEBUG | modules.decoder:search      :371 - ctc token&time: [(1522, 12), (5380, 17), (364, 22), (2181, 26), (448, 32), (4765, 35), (4935, 40), (39, 57), (1041, 62), (1241, 66), (1899, 71), (4416, 76), (1720, 81), (4765, 85), (67, 90), (363, 96), (1542, 99), (3185, 104), (30, 108), (58, 112), (68, 116), (5268, 121), (4851, 139)]
2025-07-11 10:04:57.205 | DEBUG | modules.decoder:decode_chunk:495 - ctc result: "很高兴来到这里，与大家探讨我这为关心的一个主题，那"
2025-07-11 10:04:57.205 | DEBUG | modules.connect:on_result   :428 - client_id:000 - <<< [发送] 第8个数据包, 更新识别结果: "很高兴来到这里，与大家探讨我这为关心的一个主题，那"
2025-07-11 10:04:57.210 | DEBUG | modules.connect:on_check    :626 - client_id:111 - >>> [解析] 第15个数据包, 累计帧数: 608
2025-07-11 10:04:57.210 | DEBUG | modules.connect:on_decode   :959 - client_id:111 - 所需帧数: 67, 目前帧数: 608, 开始解码
2025-07-11 10:04:57.210 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 111 获取现有会话: zh_encoder - 41a84e4d, 池大小: 1
2025-07-11 10:04:57.265 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_encoder - 41a84e4d, 当前可用: 1/1
2025-07-11 10:04:57.266 | DEBUG | modules.decoder:decode_chunk:483 - client_id:111 - 当前推理累计时点: 144
2025-07-11 10:04:57.267 | DEBUG | modules.decoder:decode_chunk:484 - client_id:111 - ctc 搜索起始时点: 128 搜索片段长度: tensor([16])
2025-07-11 10:04:57.267 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 111 获取现有会话: zh_ctc - 578779c7, 池大小: 1
2025-07-11 10:04:57.268 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_ctc - 578779c7, 当前可用: 1/1
2025-07-11 10:04:57.307 | DEBUG | modules.decoder:search      :371 - ctc token&time: [(1720, 7), (1664, 11), (928, 17), (3010, 22), (1586, 27), (753, 43), (1199, 49), (157, 64), (589, 68), (1542, 73), (3010, 77), (1199, 81), (3185, 85), (1299, 89), (5214, 94), (36, 99), (3010, 104), (4399, 108), (3730, 136), (1530, 141)]
2025-07-11 10:04:57.307 | DEBUG | modules.decoder:decode_chunk:495 - ctc result: "我想在理性，哲学，以及心理学的层面上理解，美得"
2025-07-11 10:04:57.307 | DEBUG | modules.connect:on_result   :428 - client_id:111 - <<< [发送] 第7个数据包, 更新识别结果: "我想在理性，哲学，以及心理学的层面上理解，美得"
2025-07-11 10:04:57.502 | DEBUG | modules.connect:on_check    :626 - client_id:000 - >>> [解析] 第16个数据包, 累计帧数: 646
2025-07-11 10:04:57.503 | DEBUG | modules.connect:on_decode   :959 - client_id:000 - 所需帧数: 67, 目前帧数: 646, 开始解码
2025-07-11 10:04:57.504 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 000 获取现有会话: zh_encoder - 41a84e4d, 池大小: 1
2025-07-11 10:04:57.563 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_encoder - 41a84e4d, 当前可用: 1/1
2025-07-11 10:04:57.563 | DEBUG | modules.decoder:decode_chunk:483 - client_id:000 - 当前推理累计时点: 160
2025-07-11 10:04:57.564 | DEBUG | modules.decoder:decode_chunk:484 - client_id:000 - ctc 搜索起始时点: 144 搜索片段长度: tensor([16])
2025-07-11 10:04:57.564 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 000 获取现有会话: zh_ctc - 578779c7, 池大小: 1
2025-07-11 10:04:57.564 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_ctc - 578779c7, 当前可用: 1/1
2025-07-11 10:04:57.604 | DEBUG | modules.decoder:search      :371 - ctc token&time: [(1522, 12), (5380, 17), (364, 22), (2181, 26), (448, 32), (4765, 35), (4935, 40), (39, 57), (1041, 62), (1241, 66), (1899, 71), (4416, 76), (1720, 81), (4765, 85), (67, 90), (363, 96), (1542, 99), (3185, 104), (30, 108), (58, 112), (68, 116), (5268, 121), (4851, 139), (1287, 144), (2085, 148), (3730, 154)]
2025-07-11 10:04:57.604 | DEBUG | modules.decoder:decode_chunk:495 - ctc result: "很高兴来到这里，与大家探讨我这为关心的一个主题，那就是美"
2025-07-11 10:04:57.604 | DEBUG | modules.connect:on_result   :428 - client_id:000 - <<< [发送] 第9个数据包, 更新识别结果: "很高兴来到这里，与大家探讨我这为关心的一个主题，那就是美"
2025-07-11 10:04:57.607 | DEBUG | modules.connect:on_check    :626 - client_id:111 - >>> [解析] 第16个数据包, 累计帧数: 646
2025-07-11 10:04:57.607 | DEBUG | modules.connect:on_decode   :959 - client_id:111 - 所需帧数: 67, 目前帧数: 646, 开始解码
2025-07-11 10:04:57.607 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 111 获取现有会话: zh_encoder - 41a84e4d, 池大小: 1
2025-07-11 10:04:57.655 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_encoder - 41a84e4d, 当前可用: 1/1
2025-07-11 10:04:57.656 | DEBUG | modules.decoder:decode_chunk:483 - client_id:111 - 当前推理累计时点: 160
2025-07-11 10:04:57.656 | DEBUG | modules.decoder:decode_chunk:484 - client_id:111 - ctc 搜索起始时点: 144 搜索片段长度: tensor([16])
2025-07-11 10:04:57.656 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 111 获取现有会话: zh_ctc - 578779c7, 池大小: 1
2025-07-11 10:04:57.657 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_ctc - 578779c7, 当前可用: 1/1
2025-07-11 10:04:57.695 | DEBUG | modules.decoder:search      :371 - ctc token&time: [(1720, 7), (1664, 11), (928, 17), (3010, 22), (1586, 27), (753, 43), (1199, 49), (157, 64), (589, 68), (1542, 73), (3010, 77), (1199, 81), (3185, 85), (1299, 89), (5214, 94), (36, 99), (3010, 104), (4399, 108), (3730, 136), (1530, 141), (206, 145), (5354, 150)]
2025-07-11 10:04:57.695 | DEBUG | modules.decoder:decode_chunk:495 - ctc result: "我想在理性，哲学，以及心理学的层面上理解，美得体验"
2025-07-11 10:04:57.696 | DEBUG | modules.connect:on_result   :428 - client_id:111 - <<< [发送] 第8个数据包, 更新识别结果: "我想在理性，哲学，以及心理学的层面上理解，美得体验"
2025-07-11 10:04:57.910 | DEBUG | modules.connect:on_check    :626 - client_id:000 - >>> [解析] 第17个数据包, 累计帧数: 684
2025-07-11 10:04:57.911 | DEBUG | modules.connect:on_decode   :959 - client_id:000 - 所需帧数: 67, 目前帧数: 684, 开始解码
2025-07-11 10:04:57.911 | DEBUG | modules.connect:on_decode   :988 - client_id:000 - chunk 完成解码, 识别结果不变
2025-07-11 10:04:57.917 | DEBUG | modules.connect:on_check    :626 - client_id:111 - >>> [解析] 第17个数据包, 累计帧数: 684
2025-07-11 10:04:57.918 | DEBUG | modules.connect:on_decode   :959 - client_id:111 - 所需帧数: 67, 目前帧数: 684, 开始解码
2025-07-11 10:04:57.918 | DEBUG | modules.connect:on_decode   :988 - client_id:111 - chunk 完成解码, 识别结果不变
2025-07-11 10:04:58.316 | DEBUG | modules.connect:on_check    :626 - client_id:000 - >>> [解析] 第18个数据包, 累计帧数: 722
2025-07-11 10:04:58.317 | DEBUG | modules.connect:on_decode   :959 - client_id:000 - 所需帧数: 67, 目前帧数: 722, 开始解码
2025-07-11 10:04:58.318 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 000 获取现有会话: zh_encoder - 41a84e4d, 池大小: 1
2025-07-11 10:04:58.375 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_encoder - 41a84e4d, 当前可用: 1/1
2025-07-11 10:04:58.376 | DEBUG | modules.decoder:decode_chunk:483 - client_id:000 - 当前推理累计时点: 176
2025-07-11 10:04:58.376 | DEBUG | modules.decoder:decode_chunk:484 - client_id:000 - ctc 搜索起始时点: 160 搜索片段长度: tensor([16])
2025-07-11 10:04:58.377 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 000 获取现有会话: zh_ctc - 578779c7, 池大小: 1
2025-07-11 10:04:58.377 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_ctc - 578779c7, 当前可用: 1/1
2025-07-11 10:04:58.416 | DEBUG | modules.decoder:search      :371 - ctc token&time: [(1522, 12), (5380, 17), (364, 22), (2181, 26), (448, 32), (4765, 35), (4935, 40), (39, 57), (1041, 62), (1241, 66), (1899, 71), (4416, 76), (1720, 81), (4765, 85), (67, 90), (363, 96), (1542, 99), (3185, 104), (30, 108), (58, 112), (68, 116), (5268, 121), (4851, 139), (1287, 144), (2085, 148), (3730, 154)]
2025-07-11 10:04:58.416 | DEBUG | modules.decoder:decode_chunk:495 - ctc result: "很高兴来到这里，与大家探讨我这为关心的一个主题，那就是美"
2025-07-11 10:04:58.416 | DEBUG | modules.connect:on_decode   :988 - client_id:000 - chunk 完成解码, 识别结果不变
2025-07-11 10:04:58.419 | DEBUG | modules.connect:on_check    :626 - client_id:111 - >>> [解析] 第18个数据包, 累计帧数: 722
2025-07-11 10:04:58.419 | DEBUG | modules.connect:on_decode   :959 - client_id:111 - 所需帧数: 67, 目前帧数: 722, 开始解码
2025-07-11 10:04:58.420 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 111 获取现有会话: zh_encoder - 41a84e4d, 池大小: 1
2025-07-11 10:04:58.479 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_encoder - 41a84e4d, 当前可用: 1/1
2025-07-11 10:04:58.479 | DEBUG | modules.decoder:decode_chunk:483 - client_id:111 - 当前推理累计时点: 176
2025-07-11 10:04:58.480 | DEBUG | modules.decoder:decode_chunk:484 - client_id:111 - ctc 搜索起始时点: 160 搜索片段长度: tensor([16])
2025-07-11 10:04:58.480 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 111 获取现有会话: zh_ctc - 578779c7, 池大小: 1
2025-07-11 10:04:58.481 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_ctc - 578779c7, 当前可用: 1/1
2025-07-11 10:04:58.521 | DEBUG | modules.decoder:search      :371 - ctc token&time: [(1720, 7), (1664, 11), (928, 17), (3010, 22), (1586, 27), (753, 43), (1199, 49), (157, 64), (589, 68), (1542, 73), (3010, 77), (1199, 81), (3185, 85), (1299, 89), (5214, 94), (36, 99), (3010, 104), (4399, 108), (3730, 136), (1530, 141), (206, 145), (5354, 150), (3437, 161), (3467, 166), (2085, 171)]
2025-07-11 10:04:58.522 | DEBUG | modules.decoder:decode_chunk:495 - ctc result: "我想在理性，哲学，以及心理学的层面上理解，美得体验究竟是"
2025-07-11 10:04:58.522 | DEBUG | modules.connect:on_result   :428 - client_id:111 - <<< [发送] 第9个数据包, 更新识别结果: "我想在理性，哲学，以及心理学的层面上理解，美得体验究竟是"
2025-07-11 10:04:58.724 | DEBUG | modules.connect:on_check    :626 - client_id:000 - >>> [解析] 第19个数据包, 累计帧数: 760
2025-07-11 10:04:58.724 | DEBUG | modules.connect:on_decode   :959 - client_id:000 - 所需帧数: 67, 目前帧数: 760, 开始解码
2025-07-11 10:04:58.725 | DEBUG | modules.connect:on_decode   :988 - client_id:000 - chunk 完成解码, 识别结果不变
2025-07-11 10:04:58.731 | DEBUG | modules.connect:on_check    :626 - client_id:111 - >>> [解析] 第19个数据包, 累计帧数: 743
2025-07-11 10:04:58.732 | DEBUG | modules.connect:on_decode   :959 - client_id:111 - 所需帧数: 67, 目前帧数: 743, 开始解码
2025-07-11 10:04:58.732 | DEBUG | modules.decoder:decode      :526 - client_id:111 - 第11个chunk, 原始帧: 704~743,  torch.Size([1, 39, 80])
2025-07-11 10:04:58.733 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 111 获取现有会话: zh_encoder - 41a84e4d, 池大小: 1
2025-07-11 10:04:58.791 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_encoder - 41a84e4d, 当前可用: 1/1
2025-07-11 10:04:58.791 | DEBUG | modules.decoder:decode_chunk:483 - client_id:111 - 当前推理累计时点: 185
2025-07-11 10:04:58.792 | DEBUG | modules.decoder:decode_chunk:484 - client_id:111 - ctc 搜索起始时点: 176 搜索片段长度: tensor([9])
2025-07-11 10:04:58.792 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 111 获取现有会话: zh_ctc - 578779c7, 池大小: 1
2025-07-11 10:04:58.792 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_ctc - 578779c7, 当前可用: 1/1
2025-07-11 10:04:58.839 | DEBUG | modules.decoder:search      :371 - ctc token&time: [(1720, 7), (1664, 11), (928, 17), (3010, 22), (1586, 27), (753, 43), (1199, 49), (157, 64), (589, 68), (1542, 73), (3010, 77), (1199, 81), (3185, 85), (1299, 89), (5214, 94), (36, 99), (3010, 104), (4399, 108), (3730, 136), (1530, 141), (206, 145), (5354, 150), (3437, 161), (3467, 166), (2085, 171), (132, 176), (73, 179)]
2025-07-11 10:04:58.839 | DEBUG | modules.decoder:decode_chunk:495 - ctc result: "我想在理性，哲学，以及心理学的层面上理解，美得体验究竟是什么"
2025-07-11 10:04:58.839 | INFO  | modules.connect:on_decode   :980 - client_id:111 - *** 最后一个数据包完成解码 ***
2025-07-11 10:04:58.840 | DEBUG | modules.connect:on_result   :428 - client_id:111 - <<< [发送] 第10个数据包, 更新识别结果: "我想在理性，哲学，以及心理学的层面上理解，美得体验究竟是什么"
2025-07-11 10:04:58.840 | INFO  | modules.connect:on_result   :430 - client_id:111 - <<< [响应] 最终识别结果: 我想在理性，哲学，以及心理学的层面上理解，美得体验究竟是什么
2025-07-11 10:04:58.840 | DEBUG | server :receive     :322 - client_id:111 - 已发送最后一个识别结果, 主动关闭客户连接
2025-07-11 10:04:58.840 | DEBUG | modules.connect:disconnect  :290 - client_id:111 - 开始断开连接，当前活跃连接数: 2
2025-07-11 10:04:58.842 | DEBUG | modules.connect:disconnect  :307 - client_id:111 - 开始清理客户端状态和缓存
2025-07-11 10:04:58.843 | DEBUG | modules.decoder:__del__     :444 - ASRDecoder 显式释放资源
2025-07-11 10:04:58.843 | DEBUG | modules.connect:disconnect  :326 - client_id:111 - 已经断开连接，活跃连接数已更新: 1
2025-07-11 10:04:58.843 | INFO  | server :receive     :329 - client_id: 111 - 关闭客户连接，当前活跃连接数: 1
2025-07-11 10:04:59.131 | DEBUG | modules.connect:on_check    :626 - client_id:000 - >>> [解析] 第20个数据包, 累计帧数: 798
2025-07-11 10:04:59.132 | DEBUG | modules.connect:on_decode   :959 - client_id:000 - 所需帧数: 67, 目前帧数: 798, 开始解码
2025-07-11 10:04:59.133 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 000 获取现有会话: zh_encoder - 41a84e4d, 池大小: 1
2025-07-11 10:04:59.196 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_encoder - 41a84e4d, 当前可用: 1/1
2025-07-11 10:04:59.196 | DEBUG | modules.decoder:decode_chunk:483 - client_id:000 - 当前推理累计时点: 192
2025-07-11 10:04:59.197 | DEBUG | modules.decoder:decode_chunk:484 - client_id:000 - ctc 搜索起始时点: 176 搜索片段长度: tensor([16])
2025-07-11 10:04:59.197 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 000 获取现有会话: zh_ctc - 578779c7, 池大小: 1
2025-07-11 10:04:59.198 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_ctc - 578779c7, 当前可用: 1/1
2025-07-11 10:04:59.236 | DEBUG | modules.decoder:search      :371 - ctc token&time: [(1522, 12), (5380, 17), (364, 22), (2181, 26), (448, 32), (4765, 35), (4935, 40), (39, 57), (1041, 62), (1241, 66), (1899, 71), (4416, 76), (1720, 81), (4765, 85), (67, 90), (363, 96), (1542, 99), (3185, 104), (30, 108), (58, 112), (68, 116), (5268, 121), (4851, 139), (1287, 144), (2085, 148), (3730, 154), (1720, 178), (3300, 183), (3437, 188)]
2025-07-11 10:04:59.236 | DEBUG | modules.decoder:decode_chunk:495 - ctc result: "很高兴来到这里，与大家探讨我这为关心的一个主题，那就是美，我研究"
2025-07-11 10:04:59.237 | DEBUG | modules.connect:on_result   :428 - client_id:000 - <<< [发送] 第10个数据包, 更新识别结果: "很高兴来到这里，与大家探讨我这为关心的一个主题，那就是美，我研究"
2025-07-11 10:04:59.540 | DEBUG | modules.connect:on_check    :626 - client_id:000 - >>> [解析] 第21个数据包, 累计帧数: 836
2025-07-11 10:04:59.540 | DEBUG | modules.connect:on_decode   :959 - client_id:000 - 所需帧数: 67, 目前帧数: 836, 开始解码
2025-07-11 10:04:59.541 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 000 获取现有会话: zh_encoder - 41a84e4d, 池大小: 1
2025-07-11 10:04:59.601 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_encoder - 41a84e4d, 当前可用: 1/1
2025-07-11 10:04:59.602 | DEBUG | modules.decoder:decode_chunk:483 - client_id:000 - 当前推理累计时点: 208
2025-07-11 10:04:59.602 | DEBUG | modules.decoder:decode_chunk:484 - client_id:000 - ctc 搜索起始时点: 192 搜索片段长度: tensor([16])
2025-07-11 10:04:59.603 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 000 获取现有会话: zh_ctc - 578779c7, 池大小: 1
2025-07-11 10:04:59.604 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_ctc - 578779c7, 当前可用: 1/1
2025-07-11 10:04:59.642 | DEBUG | modules.decoder:search      :371 - ctc token&time: [(1522, 12), (5380, 17), (364, 22), (2181, 26), (448, 32), (4765, 35), (4935, 40), (39, 57), (1041, 62), (1241, 66), (1899, 71), (4416, 76), (1720, 81), (4765, 85), (67, 90), (363, 96), (1542, 99), (3185, 104), (30, 108), (58, 112), (68, 116), (5268, 121), (4851, 139), (1287, 144), (2085, 148), (3730, 154), (1720, 178), (3300, 183), (3437, 188), (3957, 193), (2158, 197), (39, 201)]
2025-07-11 10:04:59.643 | DEBUG | modules.decoder:decode_chunk:495 - ctc result: "很高兴来到这里，与大家探讨我这为关心的一个主题，那就是美，我研究艺术与"
2025-07-11 10:04:59.643 | DEBUG | modules.connect:on_result   :428 - client_id:000 - <<< [发送] 第11个数据包, 更新识别结果: "很高兴来到这里，与大家探讨我这为关心的一个主题，那就是美，我研究艺术与"
2025-07-11 10:04:59.948 | DEBUG | modules.connect:on_check    :626 - client_id:000 - >>> [解析] 第22个数据包, 累计帧数: 874
2025-07-11 10:04:59.948 | DEBUG | modules.connect:on_decode   :959 - client_id:000 - 所需帧数: 67, 目前帧数: 874, 开始解码
2025-07-11 10:04:59.949 | DEBUG | modules.connect:on_decode   :988 - client_id:000 - chunk 完成解码, 识别结果不变
2025-07-11 10:05:00.356 | DEBUG | modules.connect:on_check    :626 - client_id:000 - >>> [解析] 第23个数据包, 累计帧数: 912
2025-07-11 10:05:00.356 | DEBUG | modules.connect:on_decode   :959 - client_id:000 - 所需帧数: 67, 目前帧数: 912, 开始解码
2025-07-11 10:05:00.357 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 000 获取现有会话: zh_encoder - 41a84e4d, 池大小: 1
2025-07-11 10:05:00.418 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_encoder - 41a84e4d, 当前可用: 1/1
2025-07-11 10:05:00.418 | DEBUG | modules.decoder:decode_chunk:483 - client_id:000 - 当前推理累计时点: 224
2025-07-11 10:05:00.419 | DEBUG | modules.decoder:decode_chunk:484 - client_id:000 - ctc 搜索起始时点: 208 搜索片段长度: tensor([16])
2025-07-11 10:05:00.419 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 000 获取现有会话: zh_ctc - 578779c7, 池大小: 1
2025-07-11 10:05:00.420 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_ctc - 578779c7, 当前可用: 1/1
2025-07-11 10:05:00.464 | DEBUG | modules.decoder:search      :371 - ctc token&time: [(1522, 12), (5380, 17), (364, 22), (2181, 26), (448, 32), (4765, 35), (4935, 40), (39, 57), (1041, 62), (1241, 66), (1899, 71), (4416, 76), (1720, 81), (4765, 85), (67, 90), (363, 96), (1542, 99), (3185, 104), (30, 108), (58, 112), (68, 116), (5268, 121), (4851, 139), (1287, 144), (2085, 148), (3730, 154), (1720, 178), (3300, 183), (3437, 188), (3957, 193), (2158, 197), (39, 201), (1229, 208), (3730, 211), (753, 216), (1199, 221)]
2025-07-11 10:05:00.465 | DEBUG | modules.decoder:decode_chunk:495 - ctc result: "很高兴来到这里，与大家探讨我这为关心的一个主题，那就是美，我研究艺术与审美哲学"
2025-07-11 10:05:00.465 | DEBUG | modules.connect:on_result   :428 - client_id:000 - <<< [发送] 第12个数据包, 更新识别结果: "很高兴来到这里，与大家探讨我这为关心的一个主题，那就是美，我研究艺术与审美哲学"
2025-07-11 10:05:00.765 | DEBUG | modules.connect:on_check    :626 - client_id:000 - >>> [解析] 第24个数据包, 累计帧数: 950
2025-07-11 10:05:00.765 | DEBUG | modules.connect:on_decode   :959 - client_id:000 - 所需帧数: 67, 目前帧数: 950, 开始解码
2025-07-11 10:05:00.766 | DEBUG | modules.connect:on_decode   :988 - client_id:000 - chunk 完成解码, 识别结果不变
2025-07-11 10:05:01.172 | DEBUG | modules.connect:on_check    :626 - client_id:000 - >>> [解析] 第25个数据包, 累计帧数: 988
2025-07-11 10:05:01.172 | DEBUG | modules.connect:on_decode   :959 - client_id:000 - 所需帧数: 67, 目前帧数: 988, 开始解码
2025-07-11 10:05:01.173 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 000 获取现有会话: zh_encoder - 41a84e4d, 池大小: 1
2025-07-11 10:05:01.269 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_encoder - 41a84e4d, 当前可用: 1/1
2025-07-11 10:05:01.270 | DEBUG | modules.decoder:decode_chunk:483 - client_id:000 - 当前推理累计时点: 240
2025-07-11 10:05:01.271 | DEBUG | modules.decoder:decode_chunk:484 - client_id:000 - ctc 搜索起始时点: 224 搜索片段长度: tensor([16])
2025-07-11 10:05:01.271 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 000 获取现有会话: zh_ctc - 578779c7, 池大小: 1
2025-07-11 10:05:01.272 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_ctc - 578779c7, 当前可用: 1/1
2025-07-11 10:05:01.319 | DEBUG | modules.decoder:search      :371 - ctc token&time: [(1522, 12), (5380, 17), (364, 22), (2181, 26), (448, 32), (4765, 35), (4935, 40), (39, 57), (1041, 62), (1241, 66), (1899, 71), (4416, 76), (1720, 81), (4765, 85), (67, 90), (363, 96), (1542, 99), (3185, 104), (30, 108), (58, 112), (68, 116), (5268, 121), (4851, 139), (1287, 144), (2085, 148), (3730, 154), (1720, 178), (3300, 183), (3437, 188), (3957, 193), (2158, 197), (39, 201), (1229, 208), (3730, 211), (753, 216), (1199, 221)]
2025-07-11 10:05:01.319 | DEBUG | modules.decoder:decode_chunk:495 - ctc result: "很高兴来到这里，与大家探讨我这为关心的一个主题，那就是美，我研究艺术与审美哲学"
2025-07-11 10:05:01.319 | DEBUG | modules.connect:on_decode   :988 - client_id:000 - chunk 完成解码, 识别结果不变
2025-07-11 10:05:01.581 | DEBUG | modules.connect:on_check    :626 - client_id:000 - >>> [解析] 第26个数据包, 累计帧数: 1026
2025-07-11 10:05:01.582 | DEBUG | modules.connect:on_decode   :959 - client_id:000 - 所需帧数: 67, 目前帧数: 1026, 开始解码
2025-07-11 10:05:01.583 | DEBUG | modules.connect:on_decode   :988 - client_id:000 - chunk 完成解码, 识别结果不变
2025-07-11 10:05:01.988 | DEBUG | modules.connect:on_check    :626 - client_id:000 - >>> [解析] 第27个数据包, 累计帧数: 1064
2025-07-11 10:05:01.988 | DEBUG | modules.connect:on_decode   :959 - client_id:000 - 所需帧数: 67, 目前帧数: 1064, 开始解码
2025-07-11 10:05:01.989 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 000 获取现有会话: zh_encoder - 41a84e4d, 池大小: 1
2025-07-11 10:05:02.048 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_encoder - 41a84e4d, 当前可用: 1/1
2025-07-11 10:05:02.049 | DEBUG | modules.decoder:decode_chunk:483 - client_id:000 - 当前推理累计时点: 256
2025-07-11 10:05:02.049 | DEBUG | modules.decoder:decode_chunk:484 - client_id:000 - ctc 搜索起始时点: 240 搜索片段长度: tensor([16])
2025-07-11 10:05:02.050 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 000 获取现有会话: zh_ctc - 578779c7, 池大小: 1
2025-07-11 10:05:02.050 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_ctc - 578779c7, 当前可用: 1/1
2025-07-11 10:05:02.104 | DEBUG | modules.decoder:search      :371 - ctc token&time: [(1522, 12), (5380, 17), (364, 22), (2181, 26), (448, 32), (4765, 35), (4935, 40), (39, 57), (1041, 62), (1241, 66), (1899, 71), (4416, 76), (1720, 81), (4765, 85), (67, 90), (363, 96), (1542, 99), (3185, 104), (30, 108), (58, 112), (68, 116), (5268, 121), (4851, 139), (1287, 144), (2085, 148), (3730, 154), (1720, 178), (3300, 183), (3437, 188), (3957, 193), (2158, 197), (39, 201), (1229, 208), (3730, 211), (753, 216), (1199, 221), (102, 245), (1227, 249), (36, 254)]
2025-07-11 10:05:02.104 | DEBUG | modules.decoder:decode_chunk:495 - ctc result: "很高兴来到这里，与大家探讨我这为关心的一个主题，那就是美，我研究艺术与审美哲学，事实上"
2025-07-11 10:05:02.104 | DEBUG | modules.connect:on_result   :428 - client_id:000 - <<< [发送] 第13个数据包, 更新识别结果: "很高兴来到这里，与大家探讨我这为关心的一个主题，那就是美，我研究艺术与审美哲学，事实上"
2025-07-11 10:05:02.395 | DEBUG | modules.connect:on_check    :626 - client_id:000 - >>> [解析] 第28个数据包, 累计帧数: 1102
2025-07-11 10:05:02.396 | DEBUG | modules.connect:on_decode   :959 - client_id:000 - 所需帧数: 67, 目前帧数: 1102, 开始解码
2025-07-11 10:05:02.397 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 000 获取现有会话: zh_encoder - 41a84e4d, 池大小: 1
2025-07-11 10:05:02.463 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_encoder - 41a84e4d, 当前可用: 1/1
2025-07-11 10:05:02.463 | DEBUG | modules.decoder:decode_chunk:483 - client_id:000 - 当前推理累计时点: 272
2025-07-11 10:05:02.464 | DEBUG | modules.decoder:decode_chunk:484 - client_id:000 - ctc 搜索起始时点: 256 搜索片段长度: tensor([16])
2025-07-11 10:05:02.464 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 000 获取现有会话: zh_ctc - 578779c7, 池大小: 1
2025-07-11 10:05:02.465 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_ctc - 578779c7, 当前可用: 1/1
2025-07-11 10:05:02.522 | DEBUG | modules.decoder:search      :371 - ctc token&time: [(1522, 12), (5380, 17), (364, 22), (2181, 26), (448, 32), (4765, 35), (4935, 40), (39, 57), (1041, 62), (1241, 66), (1899, 71), (4416, 76), (1720, 81), (4765, 85), (67, 90), (363, 96), (1542, 99), (3185, 104), (30, 108), (58, 112), (68, 116), (5268, 121), (4851, 139), (1287, 144), (2085, 148), (3730, 154), (1720, 178), (3300, 183), (3437, 188), (3957, 193), (2158, 197), (39, 201), (1229, 208), (3730, 211), (753, 216), (1199, 221), (102, 245), (1227, 249), (36, 254), (4765, 271)]
2025-07-11 10:05:02.522 | DEBUG | modules.decoder:decode_chunk:495 - ctc result: "很高兴来到这里，与大家探讨我这为关心的一个主题，那就是美，我研究艺术与审美哲学，事实上，这"
2025-07-11 10:05:02.523 | DEBUG | modules.connect:on_result   :428 - client_id:000 - <<< [发送] 第14个数据包, 更新识别结果: "很高兴来到这里，与大家探讨我这为关心的一个主题，那就是美，我研究艺术与审美哲学，事实上，这"
2025-07-11 10:05:02.804 | DEBUG | modules.connect:on_check    :626 - client_id:000 - >>> [解析] 第29个数据包, 累计帧数: 1140
2025-07-11 10:05:02.805 | DEBUG | modules.connect:on_decode   :959 - client_id:000 - 所需帧数: 67, 目前帧数: 1140, 开始解码
2025-07-11 10:05:02.805 | DEBUG | modules.connect:on_decode   :988 - client_id:000 - chunk 完成解码, 识别结果不变
2025-07-11 10:05:03.212 | DEBUG | modules.connect:on_check    :626 - client_id:000 - >>> [解析] 第30个数据包, 累计帧数: 1178
2025-07-11 10:05:03.213 | DEBUG | modules.connect:on_decode   :959 - client_id:000 - 所需帧数: 67, 目前帧数: 1178, 开始解码
2025-07-11 10:05:03.214 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 000 获取现有会话: zh_encoder - 41a84e4d, 池大小: 1
2025-07-11 10:05:03.277 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_encoder - 41a84e4d, 当前可用: 1/1
2025-07-11 10:05:03.277 | DEBUG | modules.decoder:decode_chunk:483 - client_id:000 - 当前推理累计时点: 288
2025-07-11 10:05:03.278 | DEBUG | modules.decoder:decode_chunk:484 - client_id:000 - ctc 搜索起始时点: 272 搜索片段长度: tensor([16])
2025-07-11 10:05:03.278 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 000 获取现有会话: zh_ctc - 578779c7, 池大小: 1
2025-07-11 10:05:03.279 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_ctc - 578779c7, 当前可用: 1/1
2025-07-11 10:05:03.323 | DEBUG | modules.decoder:search      :371 - ctc token&time: [(1522, 12), (5380, 17), (364, 22), (2181, 26), (448, 32), (4765, 35), (4935, 40), (39, 57), (1041, 62), (1241, 66), (1899, 71), (4416, 76), (1720, 81), (4765, 85), (67, 90), (363, 96), (1542, 99), (3185, 104), (30, 108), (58, 112), (68, 116), (5268, 121), (4851, 139), (1287, 144), (2085, 148), (3730, 154), (1720, 178), (3300, 183), (3437, 188), (3957, 193), (2158, 197), (39, 201), (1229, 208), (3730, 211), (753, 216), (1199, 221), (102, 245), (1227, 249), (36, 254), (4765, 271), (2085, 275), (1720, 279), (3185, 283)]
2025-07-11 10:05:03.324 | DEBUG | modules.decoder:decode_chunk:495 - ctc result: "很高兴来到这里，与大家探讨我这为关心的一个主题，那就是美，我研究艺术与审美哲学，事实上，这是我的"
2025-07-11 10:05:03.324 | DEBUG | modules.connect:on_result   :428 - client_id:000 - <<< [发送] 第15个数据包, 更新识别结果: "很高兴来到这里，与大家探讨我这为关心的一个主题，那就是美，我研究艺术与审美哲学，事实上，这是我的"
2025-07-11 10:05:03.613 | DEBUG | modules.connect:on_check    :626 - client_id:000 - >>> [解析] 第31个数据包, 累计帧数: 1181
2025-07-11 10:05:03.613 | DEBUG | modules.connect:on_decode   :959 - client_id:000 - 所需帧数: 67, 目前帧数: 1181, 开始解码
2025-07-11 10:05:03.614 | DEBUG | modules.decoder:decode      :526 - client_id:000 - 第18个chunk, 原始帧: 1152~1181,  torch.Size([1, 29, 80])
2025-07-11 10:05:03.615 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 000 获取现有会话: zh_encoder - 41a84e4d, 池大小: 1
2025-07-11 10:05:03.664 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_encoder - 41a84e4d, 当前可用: 1/1
2025-07-11 10:05:03.665 | DEBUG | modules.decoder:decode_chunk:483 - client_id:000 - 当前推理累计时点: 294
2025-07-11 10:05:03.666 | DEBUG | modules.decoder:decode_chunk:484 - client_id:000 - ctc 搜索起始时点: 288 搜索片段长度: tensor([6])
2025-07-11 10:05:03.666 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 000 获取现有会话: zh_ctc - 578779c7, 池大小: 1
2025-07-11 10:05:03.667 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_ctc - 578779c7, 当前可用: 1/1
2025-07-11 10:05:03.706 | DEBUG | modules.decoder:search      :371 - ctc token&time: [(1522, 12), (5380, 17), (364, 22), (2181, 26), (448, 32), (4765, 35), (4935, 40), (39, 57), (1041, 62), (1241, 66), (1899, 71), (4416, 76), (1720, 81), (4765, 85), (67, 90), (363, 96), (1542, 99), (3185, 104), (30, 108), (58, 112), (68, 116), (5268, 121), (4851, 139), (1287, 144), (2085, 148), (3730, 154), (1720, 178), (3300, 183), (3437, 188), (3957, 193), (2158, 197), (39, 201), (1229, 208), (3730, 211), (753, 216), (1199, 221), (102, 245), (1227, 249), (36, 254), (4765, 271), (2085, 275), (1720, 279), (3185, 283), (1381, 288), (213, 293)]
2025-07-11 10:05:03.707 | DEBUG | modules.decoder:decode_chunk:495 - ctc result: "很高兴来到这里，与大家探讨我这为关心的一个主题，那就是美，我研究艺术与审美哲学，事实上，这是我的工作"
2025-07-11 10:05:03.707 | INFO  | modules.connect:on_decode   :980 - client_id:000 - *** 最后一个数据包完成解码 ***
2025-07-11 10:05:03.707 | DEBUG | modules.connect:on_result   :428 - client_id:000 - <<< [发送] 第16个数据包, 更新识别结果: "很高兴来到这里，与大家探讨我这为关心的一个主题，那就是美，我研究艺术与审美哲学，事实上，这是我的工作"
2025-07-11 10:05:03.707 | INFO  | modules.connect:on_result   :430 - client_id:000 - <<< [响应] 最终识别结果: 很高兴来到这里，与大家探讨我这为关心的一个主题，那就是美，我研究艺术与审美哲学，事实上，这是我的工作
2025-07-11 10:05:03.707 | DEBUG | server :receive     :322 - client_id:000 - 已发送最后一个识别结果, 主动关闭客户连接
2025-07-11 10:05:03.707 | DEBUG | modules.connect:disconnect  :290 - client_id:000 - 开始断开连接，当前活跃连接数: 1
2025-07-11 10:05:03.709 | DEBUG | modules.connect:disconnect  :307 - client_id:000 - 开始清理客户端状态和缓存
2025-07-11 10:05:03.709 | DEBUG | modules.decoder:__del__     :444 - ASRDecoder 显式释放资源
2025-07-11 10:05:03.709 | DEBUG | modules.connect:disconnect  :326 - client_id:000 - 已经断开连接，活跃连接数已更新: 0
2025-07-11 10:05:03.709 | INFO  | server :receive     :329 - client_id: 000 - 关闭客户连接，当前活跃连接数: 0
2025-07-11 10:09:01.197 | INFO  | server :lifespan    :120 - 正在关闭ASR服务...
2025-07-11 10:09:06.198 | INFO  | server :lifespan    :127 - 系统监控已关闭
2025-07-11 10:09:11.199 | DEBUG | modules.onnx_session_pool:shutdown    :371 - 关闭模型 zh_encoder 的会话 41a84e4d, 使用次数: 31
2025-07-11 10:09:11.200 | DEBUG | modules.onnx_session_pool:shutdown    :371 - 关闭模型 zh_ctc 的会话 578779c7, 使用次数: 31
2025-07-11 10:09:11.265 | INFO  | server :lifespan    :134 - ONNX会话池已关闭
2025-07-11 10:09:11.270 | INFO  | server :lifespan    :144 - 实时转写已关闭
2025-07-11 10:09:11.270 | INFO  | server :lifespan    :148 - Server shutdown, delete all global resources
