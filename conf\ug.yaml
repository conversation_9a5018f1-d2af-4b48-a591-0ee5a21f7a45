# 模型加载
onnx_dir: /ws/MODELS/online_onnx_ug   # 模型路径
onnx_config: /ws/MODELS/online_onnx_ug/train.yaml  # 模型配置文件路径
fp16: false    # 是否使用单精度模型（适合双精度无法满足实时性的情况，达到加速）
quant: true   # 是否使用量化模型（适合双精度无法满足实时性的情况，达到加速）
device: cpu    # 可选：cpu/gpu/npu
device_id: 0   # 当 device: gpu 或 device: npu 时有效

# 热词功能（待支持）

# 断句功能
separator_interval: 0.8  # 若说话间隙超过此值（秒），则添加分隔符
default_separator: " "   # 维语默认分隔符（空格） 若客户端请求消息中不含自定义分隔符，则启用此分隔符

# 其余后处理
lower: false
remove_spm: false
map_path: /ws/MODELS/online_onnx_ug/map_uyg2lat.txt