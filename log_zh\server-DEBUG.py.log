2025-07-18 08:41:46.811 | INFO  | modules.config:init_logger :601 - 日志系统初始化成功, 配置: {'level': 'DEBUG', 'rotation': '1 day', 'retention': '7 days', 'compression': 'zip', 'max_file_size': '100 MB'}
2025-07-18 08:41:46.815 | WARNING | modules.monitoring:<module>    :32 - psutil未安装, 将使用系统调用获取监控信息
2025-07-18 08:41:46.819 | INFO  | modules.monitoring:_start_health_server:308 - 健康检查服务器已启动, 端口: 11082
2025-07-18 08:41:46.819 | INFO  | modules.monitoring:__init__    :176 - 系统监控已启用, 内存检查间隔: 60秒
2025-07-18 08:41:46.819 | DEBUG | modules.config:init_monitoring:632 - 监控系统初始化成功
2025-07-18 08:41:46.820 | DEBUG | modules.config:init_session_pool:657 - 会话池初始化成功
2025-07-18 08:41:46.820 | DEBUG | modules.config:init_all_modules:675 - 所有模块初始化完成
2025-07-18 08:41:46.820 | INFO  | modules.asr_manager:__init__    :39 - 初始化统一ASR管理器, 支持语种: ['zh', 'en', 'ru', 'kk', 'kkin', 'ug']
2025-07-18 08:41:46.820 | INFO  | modules.asr_manager:load_models :59 - 单语种模式：加载语种 zh
2025-07-18 08:41:46.820 | DEBUG | modules.decoder:load_onnx   :65 - 加载模型: /ws/MODELS/online_onnx_zh, fp16: False, quant: False, device: cpu
2025-07-18 08:41:46.820 | DEBUG | modules.decoder:get_ep_list :61 - ['CPUExecutionProvider']
2025-07-18 08:41:47.192 | DEBUG | modules.decoder:load_onnx   :106 - ONNX Session配置: inter_op_threads=0, intra_op_threads=0, execution_mode=PARALLEL
2025-07-18 08:41:48.010 | DEBUG | modules.onnx_session_pool:register_model:153 - 模型 zh_encoder 注册成功，预创建会话: 8b0dbba2
2025-07-18 08:41:48.010 | DEBUG | modules.decoder:load_onnx   :65 - 加载模型: /ws/MODELS/online_onnx_zh, fp16: False, quant: False, device: cpu
2025-07-18 08:41:48.010 | DEBUG | modules.decoder:get_ep_list :61 - ['CPUExecutionProvider']
2025-07-18 08:41:48.014 | DEBUG | modules.decoder:load_onnx   :106 - ONNX Session配置: inter_op_threads=0, intra_op_threads=0, execution_mode=PARALLEL
2025-07-18 08:41:48.031 | DEBUG | modules.onnx_session_pool:register_model:153 - 模型 zh_ctc 注册成功，预创建会话: 563076ce
2025-07-18 08:41:48.031 | DEBUG | modules.asr_manager:_load_single_language:104 - 语种 zh 模型已注册到会话池
2025-07-18 08:41:48.055 | DEBUG | modules.config:build_model_config:205 - 构建的模型配置: {'feat_configs': {'feat_type': 'fbank', 'num_mel_bins': 80, 'frame_length': 25, 'frame_shift': 10, 'dither': 1.0, 'n_fft': 400, 'hop_length': 160}, 'batch': 1, 'chunk_size': 16, 'left_chunks': 16, 'reverse_weight': 0.3, 'decoding_window': 67, 'subsampling_rate': 4, 'right_context': 6, 'context_list_path': '/ws/MODELS/online_onnx_zh/hotwords.txt', 'context_graph_score': 40, 'output_size': 512, 'num_blocks': 12, 'cnn_module_kernel': 15, 'head': 8, 'feature_size': 80, 'vocab_size': 5538}
2025-07-18 08:41:48.056 | DEBUG | modules.symbol_table:load_dict   :30 - 加载词表: /ws/MODELS/online_onnx_zh/units.txt
2025-07-18 08:41:48.062 | DEBUG | modules.symbol_table:__init__    :24 - 启用后处理: 转全小写字母
2025-07-18 08:41:48.062 | INFO  | modules.asr_manager:load_models :72 - 模型加载完成：成功 1/1 个语种
2025-07-18 08:41:48.062 | INFO  | server :lifespan    :109 - 单语种模式 LID功能不可用
2025-07-18 08:41:48.062 | INFO  | server :lifespan    :114 - Server start, init manager, LID_MANAGER, ASR_MANAGER


2025-07-18 08:41:57.038 | DEBUG | server :websocket_endpoint:231 - client_id:000 - 开始初始化连接
2025-07-18 08:41:57.039 | INFO  | server :websocket_endpoint:237 - client_id:000 - >>> [请求] 新客户连接，当前活跃连接数: 1
2025-07-18 08:41:57.040 | DEBUG | server :websocket_endpoint:231 - client_id:111 - 开始初始化连接
2025-07-18 08:41:57.040 | INFO  | server :websocket_endpoint:237 - client_id:111 - >>> [请求] 新客户连接，当前活跃连接数: 2
2025-07-18 08:41:57.448 | INFO  | modules.connect:on_check    :470 - client_id:000 - 设置自定义分隔符: "，"
2025-07-18 08:41:57.448 | INFO  | modules.connect:_init_decoder:143 - client_id:000 - 初始化解码器, 使用默认语种: zh
2025-07-18 08:41:57.471 | DEBUG | modules.decoder:__init__    :430 - 自定义分隔符: ，
2025-07-18 08:41:57.471 | INFO  | modules.connect:_init_decoder:150 - client_id:000 - 解码器初始化完成, 使用分隔符: "，"
2025-07-18 08:41:57.479 | DEBUG | modules.connect:on_check    :628 - client_id:000 - >>> [解析] 第0个数据包, 累计帧数: 38
2025-07-18 08:41:57.479 | DEBUG | modules.connect:on_decode   :994 - client_id:000 - 所需帧数: 67, 目前帧数: 38, 继续等待数据包
2025-07-18 08:41:57.480 | INFO  | modules.connect:on_check    :470 - client_id:111 - 设置自定义分隔符: "，"
2025-07-18 08:41:57.480 | INFO  | modules.connect:_init_decoder:143 - client_id:111 - 初始化解码器, 使用默认语种: zh
2025-07-18 08:41:57.481 | DEBUG | modules.decoder:__init__    :430 - 自定义分隔符: ，
2025-07-18 08:41:57.481 | INFO  | modules.connect:_init_decoder:150 - client_id:111 - 解码器初始化完成, 使用分隔符: "，"
2025-07-18 08:41:57.484 | DEBUG | modules.connect:on_check    :628 - client_id:111 - >>> [解析] 第0个数据包, 累计帧数: 38
2025-07-18 08:41:57.484 | DEBUG | modules.connect:on_decode   :994 - client_id:111 - 所需帧数: 67, 目前帧数: 38, 继续等待数据包
2025-07-18 08:41:57.858 | DEBUG | modules.connect:on_check    :628 - client_id:000 - >>> [解析] 第1个数据包, 累计帧数: 76
2025-07-18 08:41:57.858 | DEBUG | modules.connect:on_decode   :961 - client_id:000 - 所需帧数: 67, 目前帧数: 76, 开始解码
2025-07-18 08:41:57.859 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 000 获取现有会话: zh_encoder - 8b0dbba2, 池大小: 1
2025-07-18 08:41:57.924 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_encoder - 8b0dbba2, 当前可用: 1/1
2025-07-18 08:41:57.925 | DEBUG | modules.decoder:decode_chunk:493 - client_id:000 - 当前推理累计时点: 16
2025-07-18 08:41:57.925 | DEBUG | modules.decoder:decode_chunk:494 - client_id:000 - ctc 搜索起始时点: 0 搜索片段长度: tensor([16])
2025-07-18 08:41:57.925 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 000 获取现有会话: zh_ctc - 563076ce, 池大小: 1
2025-07-18 08:41:57.926 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_ctc - 563076ce, 当前可用: 1/1
2025-07-18 08:41:57.951 | DEBUG | modules.decoder:search      :374 - ctc token&time: [(1522, 12)]
2025-07-18 08:41:57.951 | DEBUG | modules.decoder:decode_chunk:505 - ctc result: "很"
2025-07-18 08:41:57.951 | DEBUG | modules.connect:on_result   :430 - client_id:000 - <<< [发送] 第0个数据包, 更新识别结果: "很"
2025-07-18 08:41:57.960 | DEBUG | modules.connect:on_check    :628 - client_id:111 - >>> [解析] 第1个数据包, 累计帧数: 76
2025-07-18 08:41:57.961 | DEBUG | modules.connect:on_decode   :961 - client_id:111 - 所需帧数: 67, 目前帧数: 76, 开始解码
2025-07-18 08:41:57.961 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 111 获取现有会话: zh_encoder - 8b0dbba2, 池大小: 1
2025-07-18 08:41:58.020 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_encoder - 8b0dbba2, 当前可用: 1/1
2025-07-18 08:41:58.020 | DEBUG | modules.decoder:decode_chunk:493 - client_id:111 - 当前推理累计时点: 16
2025-07-18 08:41:58.021 | DEBUG | modules.decoder:decode_chunk:494 - client_id:111 - ctc 搜索起始时点: 0 搜索片段长度: tensor([16])
2025-07-18 08:41:58.021 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 111 获取现有会话: zh_ctc - 563076ce, 池大小: 1
2025-07-18 08:41:58.035 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_ctc - 563076ce, 当前可用: 1/1
2025-07-18 08:41:58.058 | DEBUG | modules.decoder:search      :374 - ctc token&time: [(1720, 7), (1664, 11)]
2025-07-18 08:41:58.058 | DEBUG | modules.decoder:decode_chunk:505 - ctc result: "我想"
2025-07-18 08:41:58.058 | DEBUG | modules.connect:on_result   :430 - client_id:111 - <<< [发送] 第0个数据包, 更新识别结果: "我想"
2025-07-18 08:41:58.266 | DEBUG | modules.connect:on_check    :628 - client_id:000 - >>> [解析] 第2个数据包, 累计帧数: 114
2025-07-18 08:41:58.266 | DEBUG | modules.connect:on_decode   :961 - client_id:000 - 所需帧数: 67, 目前帧数: 114, 开始解码
2025-07-18 08:41:58.266 | DEBUG | modules.connect:on_decode   :990 - client_id:000 - chunk 完成解码, 识别结果不变
2025-07-18 08:41:58.270 | DEBUG | modules.connect:on_check    :628 - client_id:111 - >>> [解析] 第2个数据包, 累计帧数: 114
2025-07-18 08:41:58.270 | DEBUG | modules.connect:on_decode   :961 - client_id:111 - 所需帧数: 67, 目前帧数: 114, 开始解码
2025-07-18 08:41:58.270 | DEBUG | modules.connect:on_decode   :990 - client_id:111 - chunk 完成解码, 识别结果不变
2025-07-18 08:41:58.673 | DEBUG | modules.connect:on_check    :628 - client_id:000 - >>> [解析] 第3个数据包, 累计帧数: 152
2025-07-18 08:41:58.673 | DEBUG | modules.connect:on_decode   :961 - client_id:000 - 所需帧数: 67, 目前帧数: 152, 开始解码
2025-07-18 08:41:58.674 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 000 获取现有会话: zh_encoder - 8b0dbba2, 池大小: 1
2025-07-18 08:41:58.739 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_encoder - 8b0dbba2, 当前可用: 1/1
2025-07-18 08:41:58.740 | DEBUG | modules.decoder:decode_chunk:493 - client_id:000 - 当前推理累计时点: 32
2025-07-18 08:41:58.740 | DEBUG | modules.decoder:decode_chunk:494 - client_id:000 - ctc 搜索起始时点: 16 搜索片段长度: tensor([16])
2025-07-18 08:41:58.740 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 000 获取现有会话: zh_ctc - 563076ce, 池大小: 1
2025-07-18 08:41:58.741 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_ctc - 563076ce, 当前可用: 1/1
2025-07-18 08:41:58.820 | DEBUG | modules.decoder:search      :374 - ctc token&time: [(1522, 12), (5380, 17), (364, 22), (2181, 26)]
2025-07-18 08:41:58.821 | DEBUG | modules.decoder:decode_chunk:505 - ctc result: "很高兴来"
2025-07-18 08:41:58.821 | DEBUG | modules.connect:on_result   :430 - client_id:000 - <<< [发送] 第1个数据包, 更新识别结果: "很高兴来"
2025-07-18 08:41:58.824 | DEBUG | modules.connect:on_check    :628 - client_id:111 - >>> [解析] 第3个数据包, 累计帧数: 152
2025-07-18 08:41:58.824 | DEBUG | modules.connect:on_decode   :961 - client_id:111 - 所需帧数: 67, 目前帧数: 152, 开始解码
2025-07-18 08:41:58.824 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 111 获取现有会话: zh_encoder - 8b0dbba2, 池大小: 1
2025-07-18 08:41:58.888 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_encoder - 8b0dbba2, 当前可用: 1/1
2025-07-18 08:41:58.888 | DEBUG | modules.decoder:decode_chunk:493 - client_id:111 - 当前推理累计时点: 32
2025-07-18 08:41:58.888 | DEBUG | modules.decoder:decode_chunk:494 - client_id:111 - ctc 搜索起始时点: 16 搜索片段长度: tensor([16])
2025-07-18 08:41:58.889 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 111 获取现有会话: zh_ctc - 563076ce, 池大小: 1
2025-07-18 08:41:58.889 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_ctc - 563076ce, 当前可用: 1/1
2025-07-18 08:41:58.973 | DEBUG | modules.decoder:search      :374 - ctc token&time: [(1720, 7), (1664, 11), (928, 17), (3010, 22), (1586, 27)]
2025-07-18 08:41:58.973 | DEBUG | modules.decoder:decode_chunk:505 - ctc result: "我想在理性"
2025-07-18 08:41:58.973 | DEBUG | modules.connect:on_result   :430 - client_id:111 - <<< [发送] 第1个数据包, 更新识别结果: "我想在理性"
2025-07-18 08:41:59.080 | DEBUG | modules.connect:on_check    :628 - client_id:000 - >>> [解析] 第4个数据包, 累计帧数: 190
2025-07-18 08:41:59.080 | DEBUG | modules.connect:on_decode   :961 - client_id:000 - 所需帧数: 67, 目前帧数: 190, 开始解码
2025-07-18 08:41:59.081 | DEBUG | modules.connect:on_decode   :990 - client_id:000 - chunk 完成解码, 识别结果不变
2025-07-18 08:41:59.085 | DEBUG | modules.connect:on_check    :628 - client_id:111 - >>> [解析] 第4个数据包, 累计帧数: 190
2025-07-18 08:41:59.085 | DEBUG | modules.connect:on_decode   :961 - client_id:111 - 所需帧数: 67, 目前帧数: 190, 开始解码
2025-07-18 08:41:59.085 | DEBUG | modules.connect:on_decode   :990 - client_id:111 - chunk 完成解码, 识别结果不变
2025-07-18 08:41:59.485 | DEBUG | modules.connect:on_check    :628 - client_id:000 - >>> [解析] 第5个数据包, 累计帧数: 228
2025-07-18 08:41:59.485 | DEBUG | modules.connect:on_decode   :961 - client_id:000 - 所需帧数: 67, 目前帧数: 228, 开始解码
2025-07-18 08:41:59.486 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 000 获取现有会话: zh_encoder - 8b0dbba2, 池大小: 1
2025-07-18 08:41:59.551 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_encoder - 8b0dbba2, 当前可用: 1/1
2025-07-18 08:41:59.551 | DEBUG | modules.decoder:decode_chunk:493 - client_id:000 - 当前推理累计时点: 48
2025-07-18 08:41:59.552 | DEBUG | modules.decoder:decode_chunk:494 - client_id:000 - ctc 搜索起始时点: 32 搜索片段长度: tensor([16])
2025-07-18 08:41:59.552 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 000 获取现有会话: zh_ctc - 563076ce, 池大小: 1
2025-07-18 08:41:59.553 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_ctc - 563076ce, 当前可用: 1/1
2025-07-18 08:41:59.632 | DEBUG | modules.decoder:search      :374 - ctc token&time: [(1522, 12), (5380, 17), (364, 22), (2181, 26), (448, 32), (4765, 35), (4935, 40)]
2025-07-18 08:41:59.632 | DEBUG | modules.decoder:decode_chunk:505 - ctc result: "很高兴来到这里"
2025-07-18 08:41:59.632 | DEBUG | modules.connect:on_result   :430 - client_id:000 - <<< [发送] 第2个数据包, 更新识别结果: "很高兴来到这里"
2025-07-18 08:41:59.635 | DEBUG | modules.connect:on_check    :628 - client_id:111 - >>> [解析] 第5个数据包, 累计帧数: 228
2025-07-18 08:41:59.635 | DEBUG | modules.connect:on_decode   :961 - client_id:111 - 所需帧数: 67, 目前帧数: 228, 开始解码
2025-07-18 08:41:59.636 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 111 获取现有会话: zh_encoder - 8b0dbba2, 池大小: 1
2025-07-18 08:41:59.700 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_encoder - 8b0dbba2, 当前可用: 1/1
2025-07-18 08:41:59.700 | DEBUG | modules.decoder:decode_chunk:493 - client_id:111 - 当前推理累计时点: 48
2025-07-18 08:41:59.701 | DEBUG | modules.decoder:decode_chunk:494 - client_id:111 - ctc 搜索起始时点: 32 搜索片段长度: tensor([16])
2025-07-18 08:41:59.701 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 111 获取现有会话: zh_ctc - 563076ce, 池大小: 1
2025-07-18 08:41:59.702 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_ctc - 563076ce, 当前可用: 1/1
2025-07-18 08:41:59.777 | DEBUG | modules.decoder:search      :374 - ctc token&time: [(1720, 7), (1664, 11), (928, 17), (3010, 22), (1586, 27), (753, 43)]
2025-07-18 08:41:59.777 | DEBUG | modules.decoder:decode_chunk:505 - ctc result: "我想在理性，哲"
2025-07-18 08:41:59.777 | DEBUG | modules.connect:on_result   :430 - client_id:111 - <<< [发送] 第2个数据包, 更新识别结果: "我想在理性，哲"
2025-07-18 08:41:59.892 | DEBUG | modules.connect:on_check    :628 - client_id:000 - >>> [解析] 第6个数据包, 累计帧数: 266
2025-07-18 08:41:59.892 | DEBUG | modules.connect:on_decode   :961 - client_id:000 - 所需帧数: 67, 目前帧数: 266, 开始解码
2025-07-18 08:41:59.893 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 000 获取现有会话: zh_encoder - 8b0dbba2, 池大小: 1
2025-07-18 08:41:59.959 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_encoder - 8b0dbba2, 当前可用: 1/1
2025-07-18 08:41:59.959 | DEBUG | modules.decoder:decode_chunk:493 - client_id:000 - 当前推理累计时点: 64
2025-07-18 08:41:59.960 | DEBUG | modules.decoder:decode_chunk:494 - client_id:000 - ctc 搜索起始时点: 48 搜索片段长度: tensor([16])
2025-07-18 08:41:59.960 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 000 获取现有会话: zh_ctc - 563076ce, 池大小: 1
2025-07-18 08:41:59.961 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_ctc - 563076ce, 当前可用: 1/1
2025-07-18 08:42:00.034 | DEBUG | modules.decoder:search      :374 - ctc token&time: [(1522, 12), (5380, 17), (364, 22), (2181, 26), (448, 32), (4765, 35), (4935, 40), (39, 57), (1041, 62)]
2025-07-18 08:42:00.035 | DEBUG | modules.decoder:decode_chunk:505 - ctc result: "很高兴来到这里，与大"
2025-07-18 08:42:00.035 | DEBUG | modules.connect:on_result   :430 - client_id:000 - <<< [发送] 第3个数据包, 更新识别结果: "很高兴来到这里，与大"
2025-07-18 08:42:00.038 | DEBUG | modules.connect:on_check    :628 - client_id:111 - >>> [解析] 第6个数据包, 累计帧数: 266
2025-07-18 08:42:00.038 | DEBUG | modules.connect:on_decode   :961 - client_id:111 - 所需帧数: 67, 目前帧数: 266, 开始解码
2025-07-18 08:42:00.038 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 111 获取现有会话: zh_encoder - 8b0dbba2, 池大小: 1
2025-07-18 08:42:00.106 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_encoder - 8b0dbba2, 当前可用: 1/1
2025-07-18 08:42:00.107 | DEBUG | modules.decoder:decode_chunk:493 - client_id:111 - 当前推理累计时点: 64
2025-07-18 08:42:00.107 | DEBUG | modules.decoder:decode_chunk:494 - client_id:111 - ctc 搜索起始时点: 48 搜索片段长度: tensor([16])
2025-07-18 08:42:00.107 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 111 获取现有会话: zh_ctc - 563076ce, 池大小: 1
2025-07-18 08:42:00.108 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_ctc - 563076ce, 当前可用: 1/1
2025-07-18 08:42:00.189 | DEBUG | modules.decoder:search      :374 - ctc token&time: [(1720, 7), (1664, 11), (928, 17), (3010, 22), (1586, 27), (753, 43), (1199, 49)]
2025-07-18 08:42:00.189 | DEBUG | modules.decoder:decode_chunk:505 - ctc result: "我想在理性，哲学"
2025-07-18 08:42:00.190 | DEBUG | modules.connect:on_result   :430 - client_id:111 - <<< [发送] 第3个数据包, 更新识别结果: "我想在理性，哲学"
2025-07-18 08:42:00.298 | DEBUG | modules.connect:on_check    :628 - client_id:000 - >>> [解析] 第7个数据包, 累计帧数: 304
2025-07-18 08:42:00.298 | DEBUG | modules.connect:on_decode   :961 - client_id:000 - 所需帧数: 67, 目前帧数: 304, 开始解码
2025-07-18 08:42:00.299 | DEBUG | modules.connect:on_decode   :990 - client_id:000 - chunk 完成解码, 识别结果不变
2025-07-18 08:42:00.304 | DEBUG | modules.connect:on_check    :628 - client_id:111 - >>> [解析] 第7个数据包, 累计帧数: 304
2025-07-18 08:42:00.304 | DEBUG | modules.connect:on_decode   :961 - client_id:111 - 所需帧数: 67, 目前帧数: 304, 开始解码
2025-07-18 08:42:00.304 | DEBUG | modules.connect:on_decode   :990 - client_id:111 - chunk 完成解码, 识别结果不变
2025-07-18 08:42:00.705 | DEBUG | modules.connect:on_check    :628 - client_id:000 - >>> [解析] 第8个数据包, 累计帧数: 342
2025-07-18 08:42:00.706 | DEBUG | modules.connect:on_decode   :961 - client_id:000 - 所需帧数: 67, 目前帧数: 342, 开始解码
2025-07-18 08:42:00.706 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 000 获取现有会话: zh_encoder - 8b0dbba2, 池大小: 1
2025-07-18 08:42:00.777 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_encoder - 8b0dbba2, 当前可用: 1/1
2025-07-18 08:42:00.778 | DEBUG | modules.decoder:decode_chunk:493 - client_id:000 - 当前推理累计时点: 80
2025-07-18 08:42:00.778 | DEBUG | modules.decoder:decode_chunk:494 - client_id:000 - ctc 搜索起始时点: 64 搜索片段长度: tensor([16])
2025-07-18 08:42:00.778 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 000 获取现有会话: zh_ctc - 563076ce, 池大小: 1
2025-07-18 08:42:00.779 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_ctc - 563076ce, 当前可用: 1/1
2025-07-18 08:42:00.856 | DEBUG | modules.decoder:search      :374 - ctc token&time: [(1522, 12), (5380, 17), (364, 22), (2181, 26), (448, 32), (4765, 35), (4935, 40), (39, 57), (1041, 62), (1241, 66), (1899, 71), (4416, 76)]
2025-07-18 08:42:00.856 | DEBUG | modules.decoder:decode_chunk:505 - ctc result: "很高兴来到这里，与大家探讨"
2025-07-18 08:42:00.857 | DEBUG | modules.connect:on_result   :430 - client_id:000 - <<< [发送] 第4个数据包, 更新识别结果: "很高兴来到这里，与大家探讨"
2025-07-18 08:42:00.859 | DEBUG | modules.connect:on_check    :628 - client_id:111 - >>> [解析] 第8个数据包, 累计帧数: 342
2025-07-18 08:42:00.860 | DEBUG | modules.connect:on_decode   :961 - client_id:111 - 所需帧数: 67, 目前帧数: 342, 开始解码
2025-07-18 08:42:00.860 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 111 获取现有会话: zh_encoder - 8b0dbba2, 池大小: 1
2025-07-18 08:42:00.917 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_encoder - 8b0dbba2, 当前可用: 1/1
2025-07-18 08:42:00.917 | DEBUG | modules.decoder:decode_chunk:493 - client_id:111 - 当前推理累计时点: 80
2025-07-18 08:42:00.918 | DEBUG | modules.decoder:decode_chunk:494 - client_id:111 - ctc 搜索起始时点: 64 搜索片段长度: tensor([16])
2025-07-18 08:42:00.918 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 111 获取现有会话: zh_ctc - 563076ce, 池大小: 1
2025-07-18 08:42:00.919 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_ctc - 563076ce, 当前可用: 1/1
2025-07-18 08:42:00.989 | DEBUG | modules.decoder:search      :374 - ctc token&time: [(1720, 7), (1664, 11), (928, 17), (3010, 22), (1586, 27), (753, 43), (1199, 49), (157, 64), (589, 68), (1542, 73), (3010, 77)]
2025-07-18 08:42:00.989 | DEBUG | modules.decoder:decode_chunk:505 - ctc result: "我想在理性，哲学，以及心理"
2025-07-18 08:42:00.989 | DEBUG | modules.connect:on_result   :430 - client_id:111 - <<< [发送] 第4个数据包, 更新识别结果: "我想在理性，哲学，以及心理"
2025-07-18 08:42:01.112 | DEBUG | modules.connect:on_check    :628 - client_id:000 - >>> [解析] 第9个数据包, 累计帧数: 380
2025-07-18 08:42:01.112 | DEBUG | modules.connect:on_decode   :961 - client_id:000 - 所需帧数: 67, 目前帧数: 380, 开始解码
2025-07-18 08:42:01.113 | DEBUG | modules.connect:on_decode   :990 - client_id:000 - chunk 完成解码, 识别结果不变
2025-07-18 08:42:01.118 | DEBUG | modules.connect:on_check    :628 - client_id:111 - >>> [解析] 第9个数据包, 累计帧数: 380
2025-07-18 08:42:01.118 | DEBUG | modules.connect:on_decode   :961 - client_id:111 - 所需帧数: 67, 目前帧数: 380, 开始解码
2025-07-18 08:42:01.119 | DEBUG | modules.connect:on_decode   :990 - client_id:111 - chunk 完成解码, 识别结果不变
2025-07-18 08:42:01.519 | DEBUG | modules.connect:on_check    :628 - client_id:000 - >>> [解析] 第10个数据包, 累计帧数: 418
2025-07-18 08:42:01.519 | DEBUG | modules.connect:on_decode   :961 - client_id:000 - 所需帧数: 67, 目前帧数: 418, 开始解码
2025-07-18 08:42:01.520 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 000 获取现有会话: zh_encoder - 8b0dbba2, 池大小: 1
2025-07-18 08:42:01.583 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_encoder - 8b0dbba2, 当前可用: 1/1
2025-07-18 08:42:01.583 | DEBUG | modules.decoder:decode_chunk:493 - client_id:000 - 当前推理累计时点: 96
2025-07-18 08:42:01.584 | DEBUG | modules.decoder:decode_chunk:494 - client_id:000 - ctc 搜索起始时点: 80 搜索片段长度: tensor([16])
2025-07-18 08:42:01.584 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 000 获取现有会话: zh_ctc - 563076ce, 池大小: 1
2025-07-18 08:42:01.585 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_ctc - 563076ce, 当前可用: 1/1
2025-07-18 08:42:01.666 | DEBUG | modules.decoder:search      :374 - ctc token&time: [(1522, 12), (5380, 17), (364, 22), (2181, 26), (448, 32), (4765, 35), (4935, 40), (39, 57), (1041, 62), (1241, 66), (1899, 71), (4416, 76), (1720, 81), (4765, 85), (67, 90)]
2025-07-18 08:42:01.666 | DEBUG | modules.decoder:decode_chunk:505 - ctc result: "很高兴来到这里，与大家探讨我这为"
2025-07-18 08:42:01.666 | DEBUG | modules.connect:on_result   :430 - client_id:000 - <<< [发送] 第5个数据包, 更新识别结果: "很高兴来到这里，与大家探讨我这为"
2025-07-18 08:42:01.669 | DEBUG | modules.connect:on_check    :628 - client_id:111 - >>> [解析] 第10个数据包, 累计帧数: 418
2025-07-18 08:42:01.669 | DEBUG | modules.connect:on_decode   :961 - client_id:111 - 所需帧数: 67, 目前帧数: 418, 开始解码
2025-07-18 08:42:01.670 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 111 获取现有会话: zh_encoder - 8b0dbba2, 池大小: 1
2025-07-18 08:42:01.727 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_encoder - 8b0dbba2, 当前可用: 1/1
2025-07-18 08:42:01.727 | DEBUG | modules.decoder:decode_chunk:493 - client_id:111 - 当前推理累计时点: 96
2025-07-18 08:42:01.728 | DEBUG | modules.decoder:decode_chunk:494 - client_id:111 - ctc 搜索起始时点: 80 搜索片段长度: tensor([16])
2025-07-18 08:42:01.728 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 111 获取现有会话: zh_ctc - 563076ce, 池大小: 1
2025-07-18 08:42:01.729 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_ctc - 563076ce, 当前可用: 1/1
2025-07-18 08:42:01.808 | DEBUG | modules.decoder:search      :374 - ctc token&time: [(1720, 7), (1664, 11), (928, 17), (3010, 22), (1586, 27), (753, 43), (1199, 49), (157, 64), (589, 68), (1542, 73), (3010, 77), (1199, 81), (3185, 85), (1299, 89), (5214, 94)]
2025-07-18 08:42:01.808 | DEBUG | modules.decoder:decode_chunk:505 - ctc result: "我想在理性，哲学，以及心理学的层面"
2025-07-18 08:42:01.808 | DEBUG | modules.connect:on_result   :430 - client_id:111 - <<< [发送] 第5个数据包, 更新识别结果: "我想在理性，哲学，以及心理学的层面"
2025-07-18 08:42:01.926 | DEBUG | modules.connect:on_check    :628 - client_id:000 - >>> [解析] 第11个数据包, 累计帧数: 456
2025-07-18 08:42:01.926 | DEBUG | modules.connect:on_decode   :961 - client_id:000 - 所需帧数: 67, 目前帧数: 456, 开始解码
2025-07-18 08:42:01.927 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 000 获取现有会话: zh_encoder - 8b0dbba2, 池大小: 1
2025-07-18 08:42:01.991 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_encoder - 8b0dbba2, 当前可用: 1/1
2025-07-18 08:42:01.992 | DEBUG | modules.decoder:decode_chunk:493 - client_id:000 - 当前推理累计时点: 112
2025-07-18 08:42:01.992 | DEBUG | modules.decoder:decode_chunk:494 - client_id:000 - ctc 搜索起始时点: 96 搜索片段长度: tensor([16])
2025-07-18 08:42:01.992 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 000 获取现有会话: zh_ctc - 563076ce, 池大小: 1
2025-07-18 08:42:01.993 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_ctc - 563076ce, 当前可用: 1/1
2025-07-18 08:42:02.078 | DEBUG | modules.decoder:search      :374 - ctc token&time: [(1522, 12), (5380, 17), (364, 22), (2181, 26), (448, 32), (4765, 35), (4935, 40), (39, 57), (1041, 62), (1241, 66), (1899, 71), (4416, 76), (1720, 81), (4765, 85), (67, 90), (363, 96), (1542, 99), (3185, 104), (30, 108)]
2025-07-18 08:42:02.078 | DEBUG | modules.decoder:decode_chunk:505 - ctc result: "很高兴来到这里，与大家探讨我这为关心的一"
2025-07-18 08:42:02.078 | DEBUG | modules.connect:on_result   :430 - client_id:000 - <<< [发送] 第6个数据包, 更新识别结果: "很高兴来到这里，与大家探讨我这为关心的一"
2025-07-18 08:42:02.081 | DEBUG | modules.connect:on_check    :628 - client_id:111 - >>> [解析] 第11个数据包, 累计帧数: 456
2025-07-18 08:42:02.081 | DEBUG | modules.connect:on_decode   :961 - client_id:111 - 所需帧数: 67, 目前帧数: 456, 开始解码
2025-07-18 08:42:02.081 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 111 获取现有会话: zh_encoder - 8b0dbba2, 池大小: 1
2025-07-18 08:42:02.151 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_encoder - 8b0dbba2, 当前可用: 1/1
2025-07-18 08:42:02.151 | DEBUG | modules.decoder:decode_chunk:493 - client_id:111 - 当前推理累计时点: 112
2025-07-18 08:42:02.152 | DEBUG | modules.decoder:decode_chunk:494 - client_id:111 - ctc 搜索起始时点: 96 搜索片段长度: tensor([16])
2025-07-18 08:42:02.152 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 111 获取现有会话: zh_ctc - 563076ce, 池大小: 1
2025-07-18 08:42:02.153 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_ctc - 563076ce, 当前可用: 1/1
2025-07-18 08:42:02.233 | DEBUG | modules.decoder:search      :374 - ctc token&time: [(1720, 7), (1664, 11), (928, 17), (3010, 22), (1586, 27), (753, 43), (1199, 49), (157, 64), (589, 68), (1542, 73), (3010, 77), (1199, 81), (3185, 85), (1299, 89), (5214, 94), (36, 99), (3010, 104), (4399, 108)]
2025-07-18 08:42:02.233 | DEBUG | modules.decoder:decode_chunk:505 - ctc result: "我想在理性，哲学，以及心理学的层面上理解"
2025-07-18 08:42:02.233 | DEBUG | modules.connect:on_result   :430 - client_id:111 - <<< [发送] 第6个数据包, 更新识别结果: "我想在理性，哲学，以及心理学的层面上理解"
2025-07-18 08:42:02.333 | DEBUG | modules.connect:on_check    :628 - client_id:000 - >>> [解析] 第12个数据包, 累计帧数: 494
2025-07-18 08:42:02.333 | DEBUG | modules.connect:on_decode   :961 - client_id:000 - 所需帧数: 67, 目前帧数: 494, 开始解码
2025-07-18 08:42:02.333 | DEBUG | modules.connect:on_decode   :990 - client_id:000 - chunk 完成解码, 识别结果不变
2025-07-18 08:42:02.338 | DEBUG | modules.connect:on_check    :628 - client_id:111 - >>> [解析] 第12个数据包, 累计帧数: 494
2025-07-18 08:42:02.339 | DEBUG | modules.connect:on_decode   :961 - client_id:111 - 所需帧数: 67, 目前帧数: 494, 开始解码
2025-07-18 08:42:02.339 | DEBUG | modules.connect:on_decode   :990 - client_id:111 - chunk 完成解码, 识别结果不变
2025-07-18 08:42:02.741 | DEBUG | modules.connect:on_check    :628 - client_id:000 - >>> [解析] 第13个数据包, 累计帧数: 532
2025-07-18 08:42:02.741 | DEBUG | modules.connect:on_decode   :961 - client_id:000 - 所需帧数: 67, 目前帧数: 532, 开始解码
2025-07-18 08:42:02.742 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 000 获取现有会话: zh_encoder - 8b0dbba2, 池大小: 1
2025-07-18 08:42:02.805 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_encoder - 8b0dbba2, 当前可用: 1/1
2025-07-18 08:42:02.806 | DEBUG | modules.decoder:decode_chunk:493 - client_id:000 - 当前推理累计时点: 128
2025-07-18 08:42:02.806 | DEBUG | modules.decoder:decode_chunk:494 - client_id:000 - ctc 搜索起始时点: 112 搜索片段长度: tensor([16])
2025-07-18 08:42:02.806 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 000 获取现有会话: zh_ctc - 563076ce, 池大小: 1
2025-07-18 08:42:02.807 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_ctc - 563076ce, 当前可用: 1/1
2025-07-18 08:42:02.888 | DEBUG | modules.decoder:search      :374 - ctc token&time: [(1522, 12), (5380, 17), (364, 22), (2181, 26), (448, 32), (4765, 35), (4935, 40), (39, 57), (1041, 62), (1241, 66), (1899, 71), (4416, 76), (1720, 81), (4765, 85), (67, 90), (363, 96), (1542, 99), (3185, 104), (30, 108), (58, 112), (68, 116), (5268, 121)]
2025-07-18 08:42:02.888 | DEBUG | modules.decoder:decode_chunk:505 - ctc result: "很高兴来到这里，与大家探讨我这为关心的一个主题"
2025-07-18 08:42:02.888 | DEBUG | modules.connect:on_result   :430 - client_id:000 - <<< [发送] 第7个数据包, 更新识别结果: "很高兴来到这里，与大家探讨我这为关心的一个主题"
2025-07-18 08:42:02.891 | DEBUG | modules.connect:on_check    :628 - client_id:111 - >>> [解析] 第13个数据包, 累计帧数: 532
2025-07-18 08:42:02.891 | DEBUG | modules.connect:on_decode   :961 - client_id:111 - 所需帧数: 67, 目前帧数: 532, 开始解码
2025-07-18 08:42:02.892 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 111 获取现有会话: zh_encoder - 8b0dbba2, 池大小: 1
2025-07-18 08:42:02.953 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_encoder - 8b0dbba2, 当前可用: 1/1
2025-07-18 08:42:02.953 | DEBUG | modules.decoder:decode_chunk:493 - client_id:111 - 当前推理累计时点: 128
2025-07-18 08:42:02.954 | DEBUG | modules.decoder:decode_chunk:494 - client_id:111 - ctc 搜索起始时点: 112 搜索片段长度: tensor([16])
2025-07-18 08:42:02.954 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 111 获取现有会话: zh_ctc - 563076ce, 池大小: 1
2025-07-18 08:42:02.959 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_ctc - 563076ce, 当前可用: 1/1
2025-07-18 08:42:03.038 | DEBUG | modules.decoder:search      :374 - ctc token&time: [(1720, 7), (1664, 11), (928, 17), (3010, 22), (1586, 27), (753, 43), (1199, 49), (157, 64), (589, 68), (1542, 73), (3010, 77), (1199, 81), (3185, 85), (1299, 89), (5214, 94), (36, 99), (3010, 104), (4399, 108)]
2025-07-18 08:42:03.038 | DEBUG | modules.decoder:decode_chunk:505 - ctc result: "我想在理性，哲学，以及心理学的层面上理解"
2025-07-18 08:42:03.038 | DEBUG | modules.connect:on_decode   :990 - client_id:111 - chunk 完成解码, 识别结果不变
2025-07-18 08:42:03.147 | DEBUG | modules.connect:on_check    :628 - client_id:000 - >>> [解析] 第14个数据包, 累计帧数: 570
2025-07-18 08:42:03.147 | DEBUG | modules.connect:on_decode   :961 - client_id:000 - 所需帧数: 67, 目前帧数: 570, 开始解码
2025-07-18 08:42:03.148 | DEBUG | modules.connect:on_decode   :990 - client_id:000 - chunk 完成解码, 识别结果不变
2025-07-18 08:42:03.152 | DEBUG | modules.connect:on_check    :628 - client_id:111 - >>> [解析] 第14个数据包, 累计帧数: 570
2025-07-18 08:42:03.152 | DEBUG | modules.connect:on_decode   :961 - client_id:111 - 所需帧数: 67, 目前帧数: 570, 开始解码
2025-07-18 08:42:03.153 | DEBUG | modules.connect:on_decode   :990 - client_id:111 - chunk 完成解码, 识别结果不变
2025-07-18 08:42:03.554 | DEBUG | modules.connect:on_check    :628 - client_id:000 - >>> [解析] 第15个数据包, 累计帧数: 608
2025-07-18 08:42:03.554 | DEBUG | modules.connect:on_decode   :961 - client_id:000 - 所需帧数: 67, 目前帧数: 608, 开始解码
2025-07-18 08:42:03.555 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 000 获取现有会话: zh_encoder - 8b0dbba2, 池大小: 1
2025-07-18 08:42:03.621 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_encoder - 8b0dbba2, 当前可用: 1/1
2025-07-18 08:42:03.622 | DEBUG | modules.decoder:decode_chunk:493 - client_id:000 - 当前推理累计时点: 144
2025-07-18 08:42:03.622 | DEBUG | modules.decoder:decode_chunk:494 - client_id:000 - ctc 搜索起始时点: 128 搜索片段长度: tensor([16])
2025-07-18 08:42:03.622 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 000 获取现有会话: zh_ctc - 563076ce, 池大小: 1
2025-07-18 08:42:03.628 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_ctc - 563076ce, 当前可用: 1/1
2025-07-18 08:42:03.703 | DEBUG | modules.decoder:search      :374 - ctc token&time: [(1522, 12), (5380, 17), (364, 22), (2181, 26), (448, 32), (4765, 35), (4935, 40), (39, 57), (1041, 62), (1241, 66), (1899, 71), (4416, 76), (1720, 81), (4765, 85), (67, 90), (363, 96), (1542, 99), (3185, 104), (30, 108), (58, 112), (68, 116), (5268, 121), (4851, 139)]
2025-07-18 08:42:03.703 | DEBUG | modules.decoder:decode_chunk:505 - ctc result: "很高兴来到这里，与大家探讨我这为关心的一个主题，那"
2025-07-18 08:42:03.704 | DEBUG | modules.connect:on_result   :430 - client_id:000 - <<< [发送] 第8个数据包, 更新识别结果: "很高兴来到这里，与大家探讨我这为关心的一个主题，那"
2025-07-18 08:42:03.707 | DEBUG | modules.connect:on_check    :628 - client_id:111 - >>> [解析] 第15个数据包, 累计帧数: 608
2025-07-18 08:42:03.707 | DEBUG | modules.connect:on_decode   :961 - client_id:111 - 所需帧数: 67, 目前帧数: 608, 开始解码
2025-07-18 08:42:03.707 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 111 获取现有会话: zh_encoder - 8b0dbba2, 池大小: 1
2025-07-18 08:42:03.771 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_encoder - 8b0dbba2, 当前可用: 1/1
2025-07-18 08:42:03.771 | DEBUG | modules.decoder:decode_chunk:493 - client_id:111 - 当前推理累计时点: 144
2025-07-18 08:42:03.772 | DEBUG | modules.decoder:decode_chunk:494 - client_id:111 - ctc 搜索起始时点: 128 搜索片段长度: tensor([16])
2025-07-18 08:42:03.772 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 111 获取现有会话: zh_ctc - 563076ce, 池大小: 1
2025-07-18 08:42:03.773 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_ctc - 563076ce, 当前可用: 1/1
2025-07-18 08:42:03.857 | DEBUG | modules.decoder:search      :374 - ctc token&time: [(1720, 7), (1664, 11), (928, 17), (3010, 22), (1586, 27), (753, 43), (1199, 49), (157, 64), (589, 68), (1542, 73), (3010, 77), (1199, 81), (3185, 85), (1299, 89), (5214, 94), (36, 99), (3010, 104), (4399, 108), (3730, 136), (1530, 141)]
2025-07-18 08:42:03.857 | DEBUG | modules.decoder:decode_chunk:505 - ctc result: "我想在理性，哲学，以及心理学的层面上理解，美得"
2025-07-18 08:42:03.857 | DEBUG | modules.connect:on_result   :430 - client_id:111 - <<< [发送] 第7个数据包, 更新识别结果: "我想在理性，哲学，以及心理学的层面上理解，美得"
2025-07-18 08:42:03.960 | DEBUG | modules.connect:on_check    :628 - client_id:000 - >>> [解析] 第16个数据包, 累计帧数: 646
2025-07-18 08:42:03.961 | DEBUG | modules.connect:on_decode   :961 - client_id:000 - 所需帧数: 67, 目前帧数: 646, 开始解码
2025-07-18 08:42:03.961 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 000 获取现有会话: zh_encoder - 8b0dbba2, 池大小: 1
2025-07-18 08:42:04.023 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_encoder - 8b0dbba2, 当前可用: 1/1
2025-07-18 08:42:04.023 | DEBUG | modules.decoder:decode_chunk:493 - client_id:000 - 当前推理累计时点: 160
2025-07-18 08:42:04.024 | DEBUG | modules.decoder:decode_chunk:494 - client_id:000 - ctc 搜索起始时点: 144 搜索片段长度: tensor([16])
2025-07-18 08:42:04.024 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 000 获取现有会话: zh_ctc - 563076ce, 池大小: 1
2025-07-18 08:42:04.025 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_ctc - 563076ce, 当前可用: 1/1
2025-07-18 08:42:04.108 | DEBUG | modules.decoder:search      :374 - ctc token&time: [(1522, 12), (5380, 17), (364, 22), (2181, 26), (448, 32), (4765, 35), (4935, 40), (39, 57), (1041, 62), (1241, 66), (1899, 71), (4416, 76), (1720, 81), (4765, 85), (67, 90), (363, 96), (1542, 99), (3185, 104), (30, 108), (58, 112), (68, 116), (5268, 121), (4851, 139), (1287, 144), (2085, 148), (3730, 154)]
2025-07-18 08:42:04.108 | DEBUG | modules.decoder:decode_chunk:505 - ctc result: "很高兴来到这里，与大家探讨我这为关心的一个主题，那就是美"
2025-07-18 08:42:04.108 | DEBUG | modules.connect:on_result   :430 - client_id:000 - <<< [发送] 第9个数据包, 更新识别结果: "很高兴来到这里，与大家探讨我这为关心的一个主题，那就是美"
2025-07-18 08:42:04.111 | DEBUG | modules.connect:on_check    :628 - client_id:111 - >>> [解析] 第16个数据包, 累计帧数: 646
2025-07-18 08:42:04.111 | DEBUG | modules.connect:on_decode   :961 - client_id:111 - 所需帧数: 67, 目前帧数: 646, 开始解码
2025-07-18 08:42:04.111 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 111 获取现有会话: zh_encoder - 8b0dbba2, 池大小: 1
2025-07-18 08:42:04.176 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_encoder - 8b0dbba2, 当前可用: 1/1
2025-07-18 08:42:04.177 | DEBUG | modules.decoder:decode_chunk:493 - client_id:111 - 当前推理累计时点: 160
2025-07-18 08:42:04.177 | DEBUG | modules.decoder:decode_chunk:494 - client_id:111 - ctc 搜索起始时点: 144 搜索片段长度: tensor([16])
2025-07-18 08:42:04.177 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 111 获取现有会话: zh_ctc - 563076ce, 池大小: 1
2025-07-18 08:42:04.178 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_ctc - 563076ce, 当前可用: 1/1
2025-07-18 08:42:04.254 | DEBUG | modules.decoder:search      :374 - ctc token&time: [(1720, 7), (1664, 11), (928, 17), (3010, 22), (1586, 27), (753, 43), (1199, 49), (157, 64), (589, 68), (1542, 73), (3010, 77), (1199, 81), (3185, 85), (1299, 89), (5214, 94), (36, 99), (3010, 104), (4399, 108), (3730, 136), (1530, 141), (206, 145), (5354, 150)]
2025-07-18 08:42:04.254 | DEBUG | modules.decoder:decode_chunk:505 - ctc result: "我想在理性，哲学，以及心理学的层面上理解，美得体验"
2025-07-18 08:42:04.254 | DEBUG | modules.connect:on_result   :430 - client_id:111 - <<< [发送] 第8个数据包, 更新识别结果: "我想在理性，哲学，以及心理学的层面上理解，美得体验"
2025-07-18 08:42:04.367 | DEBUG | modules.connect:on_check    :628 - client_id:000 - >>> [解析] 第17个数据包, 累计帧数: 684
2025-07-18 08:42:04.368 | DEBUG | modules.connect:on_decode   :961 - client_id:000 - 所需帧数: 67, 目前帧数: 684, 开始解码
2025-07-18 08:42:04.368 | DEBUG | modules.connect:on_decode   :990 - client_id:000 - chunk 完成解码, 识别结果不变
2025-07-18 08:42:04.374 | DEBUG | modules.connect:on_check    :628 - client_id:111 - >>> [解析] 第17个数据包, 累计帧数: 684
2025-07-18 08:42:04.374 | DEBUG | modules.connect:on_decode   :961 - client_id:111 - 所需帧数: 67, 目前帧数: 684, 开始解码
2025-07-18 08:42:04.374 | DEBUG | modules.connect:on_decode   :990 - client_id:111 - chunk 完成解码, 识别结果不变
2025-07-18 08:42:04.774 | DEBUG | modules.connect:on_check    :628 - client_id:000 - >>> [解析] 第18个数据包, 累计帧数: 722
2025-07-18 08:42:04.774 | DEBUG | modules.connect:on_decode   :961 - client_id:000 - 所需帧数: 67, 目前帧数: 722, 开始解码
2025-07-18 08:42:04.775 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 000 获取现有会话: zh_encoder - 8b0dbba2, 池大小: 1
2025-07-18 08:42:04.841 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_encoder - 8b0dbba2, 当前可用: 1/1
2025-07-18 08:42:04.842 | DEBUG | modules.decoder:decode_chunk:493 - client_id:000 - 当前推理累计时点: 176
2025-07-18 08:42:04.842 | DEBUG | modules.decoder:decode_chunk:494 - client_id:000 - ctc 搜索起始时点: 160 搜索片段长度: tensor([16])
2025-07-18 08:42:04.843 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 000 获取现有会话: zh_ctc - 563076ce, 池大小: 1
2025-07-18 08:42:04.844 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_ctc - 563076ce, 当前可用: 1/1
2025-07-18 08:42:04.922 | DEBUG | modules.decoder:search      :374 - ctc token&time: [(1522, 12), (5380, 17), (364, 22), (2181, 26), (448, 32), (4765, 35), (4935, 40), (39, 57), (1041, 62), (1241, 66), (1899, 71), (4416, 76), (1720, 81), (4765, 85), (67, 90), (363, 96), (1542, 99), (3185, 104), (30, 108), (58, 112), (68, 116), (5268, 121), (4851, 139), (1287, 144), (2085, 148), (3730, 154)]
2025-07-18 08:42:04.922 | DEBUG | modules.decoder:decode_chunk:505 - ctc result: "很高兴来到这里，与大家探讨我这为关心的一个主题，那就是美"
2025-07-18 08:42:04.923 | DEBUG | modules.connect:on_decode   :990 - client_id:000 - chunk 完成解码, 识别结果不变
2025-07-18 08:42:04.925 | DEBUG | modules.connect:on_check    :628 - client_id:111 - >>> [解析] 第18个数据包, 累计帧数: 722
2025-07-18 08:42:04.925 | DEBUG | modules.connect:on_decode   :961 - client_id:111 - 所需帧数: 67, 目前帧数: 722, 开始解码
2025-07-18 08:42:04.926 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 111 获取现有会话: zh_encoder - 8b0dbba2, 池大小: 1
2025-07-18 08:42:04.985 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_encoder - 8b0dbba2, 当前可用: 1/1
2025-07-18 08:42:04.986 | DEBUG | modules.decoder:decode_chunk:493 - client_id:111 - 当前推理累计时点: 176
2025-07-18 08:42:04.986 | DEBUG | modules.decoder:decode_chunk:494 - client_id:111 - ctc 搜索起始时点: 160 搜索片段长度: tensor([16])
2025-07-18 08:42:04.986 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 111 获取现有会话: zh_ctc - 563076ce, 池大小: 1
2025-07-18 08:42:04.987 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_ctc - 563076ce, 当前可用: 1/1
2025-07-18 08:42:05.064 | DEBUG | modules.decoder:search      :374 - ctc token&time: [(1720, 7), (1664, 11), (928, 17), (3010, 22), (1586, 27), (753, 43), (1199, 49), (157, 64), (589, 68), (1542, 73), (3010, 77), (1199, 81), (3185, 85), (1299, 89), (5214, 94), (36, 99), (3010, 104), (4399, 108), (3730, 136), (1530, 141), (206, 145), (5354, 150), (3437, 161), (3467, 166), (2085, 171)]
2025-07-18 08:42:05.066 | DEBUG | modules.decoder:decode_chunk:505 - ctc result: "我想在理性，哲学，以及心理学的层面上理解，美得体验究竟是"
2025-07-18 08:42:05.066 | DEBUG | modules.connect:on_result   :430 - client_id:111 - <<< [发送] 第9个数据包, 更新识别结果: "我想在理性，哲学，以及心理学的层面上理解，美得体验究竟是"
2025-07-18 08:42:05.181 | DEBUG | modules.connect:on_check    :628 - client_id:000 - >>> [解析] 第19个数据包, 累计帧数: 760
2025-07-18 08:42:05.181 | DEBUG | modules.connect:on_decode   :961 - client_id:000 - 所需帧数: 67, 目前帧数: 760, 开始解码
2025-07-18 08:42:05.182 | DEBUG | modules.connect:on_decode   :990 - client_id:000 - chunk 完成解码, 识别结果不变
2025-07-18 08:42:05.186 | DEBUG | modules.connect:on_check    :628 - client_id:111 - >>> [解析] 第19个数据包, 累计帧数: 743
2025-07-18 08:42:05.186 | DEBUG | modules.connect:on_decode   :961 - client_id:111 - 所需帧数: 67, 目前帧数: 743, 开始解码
2025-07-18 08:42:05.187 | DEBUG | modules.decoder:decode      :536 - client_id:111 - 第11个chunk, 原始帧: 704~743,  torch.Size([1, 39, 80])
2025-07-18 08:42:05.187 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 111 获取现有会话: zh_encoder - 8b0dbba2, 池大小: 1
2025-07-18 08:42:05.242 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_encoder - 8b0dbba2, 当前可用: 1/1
2025-07-18 08:42:05.243 | DEBUG | modules.decoder:decode_chunk:493 - client_id:111 - 当前推理累计时点: 185
2025-07-18 08:42:05.243 | DEBUG | modules.decoder:decode_chunk:494 - client_id:111 - ctc 搜索起始时点: 176 搜索片段长度: tensor([9])
2025-07-18 08:42:05.243 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 111 获取现有会话: zh_ctc - 563076ce, 池大小: 1
2025-07-18 08:42:05.244 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_ctc - 563076ce, 当前可用: 1/1
2025-07-18 08:42:05.315 | DEBUG | modules.decoder:search      :374 - ctc token&time: [(1720, 7), (1664, 11), (928, 17), (3010, 22), (1586, 27), (753, 43), (1199, 49), (157, 64), (589, 68), (1542, 73), (3010, 77), (1199, 81), (3185, 85), (1299, 89), (5214, 94), (36, 99), (3010, 104), (4399, 108), (3730, 136), (1530, 141), (206, 145), (5354, 150), (3437, 161), (3467, 166), (2085, 171), (132, 176), (73, 179)]
2025-07-18 08:42:05.315 | DEBUG | modules.decoder:decode_chunk:505 - ctc result: "我想在理性，哲学，以及心理学的层面上理解，美得体验究竟是什么"
2025-07-18 08:42:05.315 | INFO  | modules.connect:on_decode   :982 - client_id:111 - *** 最后一个数据包完成解码 ***
2025-07-18 08:42:05.315 | DEBUG | modules.connect:on_result   :430 - client_id:111 - <<< [发送] 第10个数据包, 更新识别结果: "我想在理性，哲学，以及心理学的层面上理解，美得体验究竟是什么"
2025-07-18 08:42:05.315 | INFO  | modules.connect:on_result   :432 - client_id:111 - <<< [响应] 最终识别结果: 我想在理性，哲学，以及心理学的层面上理解，美得体验究竟是什么
2025-07-18 08:42:05.315 | DEBUG | server :receive     :322 - client_id:111 - 已发送最后一个识别结果, 主动关闭客户连接
2025-07-18 08:42:05.315 | DEBUG | modules.connect:disconnect  :292 - client_id:111 - 开始断开连接，当前活跃连接数: 2
2025-07-18 08:42:05.318 | DEBUG | modules.connect:disconnect  :309 - client_id:111 - 开始清理客户端状态和缓存
2025-07-18 08:42:05.318 | DEBUG | modules.decoder:__del__     :454 - ASRDecoder 显式释放资源
2025-07-18 08:42:05.318 | DEBUG | modules.connect:disconnect  :328 - client_id:111 - 已经断开连接，活跃连接数已更新: 1
2025-07-18 08:42:05.318 | INFO  | server :receive     :329 - client_id: 111 - 关闭客户连接，当前活跃连接数: 1
2025-07-18 08:42:05.589 | DEBUG | modules.connect:on_check    :628 - client_id:000 - >>> [解析] 第20个数据包, 累计帧数: 798
2025-07-18 08:42:05.589 | DEBUG | modules.connect:on_decode   :961 - client_id:000 - 所需帧数: 67, 目前帧数: 798, 开始解码
2025-07-18 08:42:05.590 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 000 获取现有会话: zh_encoder - 8b0dbba2, 池大小: 1
2025-07-18 08:42:05.654 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_encoder - 8b0dbba2, 当前可用: 1/1
2025-07-18 08:42:05.655 | DEBUG | modules.decoder:decode_chunk:493 - client_id:000 - 当前推理累计时点: 192
2025-07-18 08:42:05.655 | DEBUG | modules.decoder:decode_chunk:494 - client_id:000 - ctc 搜索起始时点: 176 搜索片段长度: tensor([16])
2025-07-18 08:42:05.655 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 000 获取现有会话: zh_ctc - 563076ce, 池大小: 1
2025-07-18 08:42:05.656 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_ctc - 563076ce, 当前可用: 1/1
2025-07-18 08:42:05.735 | DEBUG | modules.decoder:search      :374 - ctc token&time: [(1522, 12), (5380, 17), (364, 22), (2181, 26), (448, 32), (4765, 35), (4935, 40), (39, 57), (1041, 62), (1241, 66), (1899, 71), (4416, 76), (1720, 81), (4765, 85), (67, 90), (363, 96), (1542, 99), (3185, 104), (30, 108), (58, 112), (68, 116), (5268, 121), (4851, 139), (1287, 144), (2085, 148), (3730, 154), (1720, 178), (3300, 183), (3437, 188)]
2025-07-18 08:42:05.735 | DEBUG | modules.decoder:decode_chunk:505 - ctc result: "很高兴来到这里，与大家探讨我这为关心的一个主题，那就是美，我研究"
2025-07-18 08:42:05.735 | DEBUG | modules.connect:on_result   :430 - client_id:000 - <<< [发送] 第10个数据包, 更新识别结果: "很高兴来到这里，与大家探讨我这为关心的一个主题，那就是美，我研究"
2025-07-18 08:42:05.995 | DEBUG | modules.connect:on_check    :628 - client_id:000 - >>> [解析] 第21个数据包, 累计帧数: 836
2025-07-18 08:42:05.996 | DEBUG | modules.connect:on_decode   :961 - client_id:000 - 所需帧数: 67, 目前帧数: 836, 开始解码
2025-07-18 08:42:05.996 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 000 获取现有会话: zh_encoder - 8b0dbba2, 池大小: 1
2025-07-18 08:42:06.064 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_encoder - 8b0dbba2, 当前可用: 1/1
2025-07-18 08:42:06.064 | DEBUG | modules.decoder:decode_chunk:493 - client_id:000 - 当前推理累计时点: 208
2025-07-18 08:42:06.065 | DEBUG | modules.decoder:decode_chunk:494 - client_id:000 - ctc 搜索起始时点: 192 搜索片段长度: tensor([16])
2025-07-18 08:42:06.065 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 000 获取现有会话: zh_ctc - 563076ce, 池大小: 1
2025-07-18 08:42:06.087 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_ctc - 563076ce, 当前可用: 1/1
2025-07-18 08:42:06.147 | DEBUG | modules.decoder:search      :374 - ctc token&time: [(1522, 12), (5380, 17), (364, 22), (2181, 26), (448, 32), (4765, 35), (4935, 40), (39, 57), (1041, 62), (1241, 66), (1899, 71), (4416, 76), (1720, 81), (4765, 85), (67, 90), (363, 96), (1542, 99), (3185, 104), (30, 108), (58, 112), (68, 116), (5268, 121), (4851, 139), (1287, 144), (2085, 148), (3730, 154), (1720, 178), (3300, 183), (3437, 188), (3957, 193), (2158, 197), (39, 201)]
2025-07-18 08:42:06.147 | DEBUG | modules.decoder:decode_chunk:505 - ctc result: "很高兴来到这里，与大家探讨我这为关心的一个主题，那就是美，我研究艺术与"
2025-07-18 08:42:06.147 | DEBUG | modules.connect:on_result   :430 - client_id:000 - <<< [发送] 第11个数据包, 更新识别结果: "很高兴来到这里，与大家探讨我这为关心的一个主题，那就是美，我研究艺术与"
2025-07-18 08:42:06.403 | DEBUG | modules.connect:on_check    :628 - client_id:000 - >>> [解析] 第22个数据包, 累计帧数: 874
2025-07-18 08:42:06.403 | DEBUG | modules.connect:on_decode   :961 - client_id:000 - 所需帧数: 67, 目前帧数: 874, 开始解码
2025-07-18 08:42:06.404 | DEBUG | modules.connect:on_decode   :990 - client_id:000 - chunk 完成解码, 识别结果不变
2025-07-18 08:42:06.809 | DEBUG | modules.connect:on_check    :628 - client_id:000 - >>> [解析] 第23个数据包, 累计帧数: 912
2025-07-18 08:42:06.809 | DEBUG | modules.connect:on_decode   :961 - client_id:000 - 所需帧数: 67, 目前帧数: 912, 开始解码
2025-07-18 08:42:06.810 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 000 获取现有会话: zh_encoder - 8b0dbba2, 池大小: 1
2025-07-18 08:42:06.867 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_encoder - 8b0dbba2, 当前可用: 1/1
2025-07-18 08:42:06.868 | DEBUG | modules.decoder:decode_chunk:493 - client_id:000 - 当前推理累计时点: 224
2025-07-18 08:42:06.868 | DEBUG | modules.decoder:decode_chunk:494 - client_id:000 - ctc 搜索起始时点: 208 搜索片段长度: tensor([16])
2025-07-18 08:42:06.868 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 000 获取现有会话: zh_ctc - 563076ce, 池大小: 1
2025-07-18 08:42:06.869 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_ctc - 563076ce, 当前可用: 1/1
2025-07-18 08:42:06.952 | DEBUG | modules.decoder:search      :374 - ctc token&time: [(1522, 12), (5380, 17), (364, 22), (2181, 26), (448, 32), (4765, 35), (4935, 40), (39, 57), (1041, 62), (1241, 66), (1899, 71), (4416, 76), (1720, 81), (4765, 85), (67, 90), (363, 96), (1542, 99), (3185, 104), (30, 108), (58, 112), (68, 116), (5268, 121), (4851, 139), (1287, 144), (2085, 148), (3730, 154), (1720, 178), (3300, 183), (3437, 188), (3957, 193), (2158, 197), (39, 201), (1229, 208), (3730, 211), (753, 216), (1199, 221)]
2025-07-18 08:42:06.952 | DEBUG | modules.decoder:decode_chunk:505 - ctc result: "很高兴来到这里，与大家探讨我这为关心的一个主题，那就是美，我研究艺术与审美哲学"
2025-07-18 08:42:06.952 | DEBUG | modules.connect:on_result   :430 - client_id:000 - <<< [发送] 第12个数据包, 更新识别结果: "很高兴来到这里，与大家探讨我这为关心的一个主题，那就是美，我研究艺术与审美哲学"
2025-07-18 08:42:07.215 | DEBUG | modules.connect:on_check    :628 - client_id:000 - >>> [解析] 第24个数据包, 累计帧数: 950
2025-07-18 08:42:07.216 | DEBUG | modules.connect:on_decode   :961 - client_id:000 - 所需帧数: 67, 目前帧数: 950, 开始解码
2025-07-18 08:42:07.216 | DEBUG | modules.connect:on_decode   :990 - client_id:000 - chunk 完成解码, 识别结果不变
2025-07-18 08:42:07.622 | DEBUG | modules.connect:on_check    :628 - client_id:000 - >>> [解析] 第25个数据包, 累计帧数: 988
2025-07-18 08:42:07.622 | DEBUG | modules.connect:on_decode   :961 - client_id:000 - 所需帧数: 67, 目前帧数: 988, 开始解码
2025-07-18 08:42:07.623 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 000 获取现有会话: zh_encoder - 8b0dbba2, 池大小: 1
2025-07-18 08:42:07.689 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_encoder - 8b0dbba2, 当前可用: 1/1
2025-07-18 08:42:07.689 | DEBUG | modules.decoder:decode_chunk:493 - client_id:000 - 当前推理累计时点: 240
2025-07-18 08:42:07.690 | DEBUG | modules.decoder:decode_chunk:494 - client_id:000 - ctc 搜索起始时点: 224 搜索片段长度: tensor([16])
2025-07-18 08:42:07.690 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 000 获取现有会话: zh_ctc - 563076ce, 池大小: 1
2025-07-18 08:42:07.691 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_ctc - 563076ce, 当前可用: 1/1
2025-07-18 08:42:07.763 | DEBUG | modules.decoder:search      :374 - ctc token&time: [(1522, 12), (5380, 17), (364, 22), (2181, 26), (448, 32), (4765, 35), (4935, 40), (39, 57), (1041, 62), (1241, 66), (1899, 71), (4416, 76), (1720, 81), (4765, 85), (67, 90), (363, 96), (1542, 99), (3185, 104), (30, 108), (58, 112), (68, 116), (5268, 121), (4851, 139), (1287, 144), (2085, 148), (3730, 154), (1720, 178), (3300, 183), (3437, 188), (3957, 193), (2158, 197), (39, 201), (1229, 208), (3730, 211), (753, 216), (1199, 221)]
2025-07-18 08:42:07.763 | DEBUG | modules.decoder:decode_chunk:505 - ctc result: "很高兴来到这里，与大家探讨我这为关心的一个主题，那就是美，我研究艺术与审美哲学"
2025-07-18 08:42:07.764 | DEBUG | modules.connect:on_decode   :990 - client_id:000 - chunk 完成解码, 识别结果不变
2025-07-18 08:42:08.028 | DEBUG | modules.connect:on_check    :628 - client_id:000 - >>> [解析] 第26个数据包, 累计帧数: 1026
2025-07-18 08:42:08.029 | DEBUG | modules.connect:on_decode   :961 - client_id:000 - 所需帧数: 67, 目前帧数: 1026, 开始解码
2025-07-18 08:42:08.029 | DEBUG | modules.connect:on_decode   :990 - client_id:000 - chunk 完成解码, 识别结果不变
2025-07-18 08:42:08.435 | DEBUG | modules.connect:on_check    :628 - client_id:000 - >>> [解析] 第27个数据包, 累计帧数: 1064
2025-07-18 08:42:08.435 | DEBUG | modules.connect:on_decode   :961 - client_id:000 - 所需帧数: 67, 目前帧数: 1064, 开始解码
2025-07-18 08:42:08.436 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 000 获取现有会话: zh_encoder - 8b0dbba2, 池大小: 1
2025-07-18 08:42:08.497 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_encoder - 8b0dbba2, 当前可用: 1/1
2025-07-18 08:42:08.497 | DEBUG | modules.decoder:decode_chunk:493 - client_id:000 - 当前推理累计时点: 256
2025-07-18 08:42:08.498 | DEBUG | modules.decoder:decode_chunk:494 - client_id:000 - ctc 搜索起始时点: 240 搜索片段长度: tensor([16])
2025-07-18 08:42:08.498 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 000 获取现有会话: zh_ctc - 563076ce, 池大小: 1
2025-07-18 08:42:08.499 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_ctc - 563076ce, 当前可用: 1/1
2025-07-18 08:42:08.578 | DEBUG | modules.decoder:search      :374 - ctc token&time: [(1522, 12), (5380, 17), (364, 22), (2181, 26), (448, 32), (4765, 35), (4935, 40), (39, 57), (1041, 62), (1241, 66), (1899, 71), (4416, 76), (1720, 81), (4765, 85), (67, 90), (363, 96), (1542, 99), (3185, 104), (30, 108), (58, 112), (68, 116), (5268, 121), (4851, 139), (1287, 144), (2085, 148), (3730, 154), (1720, 178), (3300, 183), (3437, 188), (3957, 193), (2158, 197), (39, 201), (1229, 208), (3730, 211), (753, 216), (1199, 221), (102, 245), (1227, 249), (36, 254)]
2025-07-18 08:42:08.578 | DEBUG | modules.decoder:decode_chunk:505 - ctc result: "很高兴来到这里，与大家探讨我这为关心的一个主题，那就是美，我研究艺术与审美哲学，事实上"
2025-07-18 08:42:08.578 | DEBUG | modules.connect:on_result   :430 - client_id:000 - <<< [发送] 第13个数据包, 更新识别结果: "很高兴来到这里，与大家探讨我这为关心的一个主题，那就是美，我研究艺术与审美哲学，事实上"
2025-07-18 08:42:08.841 | DEBUG | modules.connect:on_check    :628 - client_id:000 - >>> [解析] 第28个数据包, 累计帧数: 1102
2025-07-18 08:42:08.842 | DEBUG | modules.connect:on_decode   :961 - client_id:000 - 所需帧数: 67, 目前帧数: 1102, 开始解码
2025-07-18 08:42:08.842 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 000 获取现有会话: zh_encoder - 8b0dbba2, 池大小: 1
2025-07-18 08:42:08.907 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_encoder - 8b0dbba2, 当前可用: 1/1
2025-07-18 08:42:08.908 | DEBUG | modules.decoder:decode_chunk:493 - client_id:000 - 当前推理累计时点: 272
2025-07-18 08:42:08.908 | DEBUG | modules.decoder:decode_chunk:494 - client_id:000 - ctc 搜索起始时点: 256 搜索片段长度: tensor([16])
2025-07-18 08:42:08.909 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 000 获取现有会话: zh_ctc - 563076ce, 池大小: 1
2025-07-18 08:42:08.909 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_ctc - 563076ce, 当前可用: 1/1
2025-07-18 08:42:08.982 | DEBUG | modules.decoder:search      :374 - ctc token&time: [(1522, 12), (5380, 17), (364, 22), (2181, 26), (448, 32), (4765, 35), (4935, 40), (39, 57), (1041, 62), (1241, 66), (1899, 71), (4416, 76), (1720, 81), (4765, 85), (67, 90), (363, 96), (1542, 99), (3185, 104), (30, 108), (58, 112), (68, 116), (5268, 121), (4851, 139), (1287, 144), (2085, 148), (3730, 154), (1720, 178), (3300, 183), (3437, 188), (3957, 193), (2158, 197), (39, 201), (1229, 208), (3730, 211), (753, 216), (1199, 221), (102, 245), (1227, 249), (36, 254), (4765, 271)]
2025-07-18 08:42:08.982 | DEBUG | modules.decoder:decode_chunk:505 - ctc result: "很高兴来到这里，与大家探讨我这为关心的一个主题，那就是美，我研究艺术与审美哲学，事实上，这"
2025-07-18 08:42:08.982 | DEBUG | modules.connect:on_result   :430 - client_id:000 - <<< [发送] 第14个数据包, 更新识别结果: "很高兴来到这里，与大家探讨我这为关心的一个主题，那就是美，我研究艺术与审美哲学，事实上，这"
2025-07-18 08:42:09.248 | DEBUG | modules.connect:on_check    :628 - client_id:000 - >>> [解析] 第29个数据包, 累计帧数: 1140
2025-07-18 08:42:09.248 | DEBUG | modules.connect:on_decode   :961 - client_id:000 - 所需帧数: 67, 目前帧数: 1140, 开始解码
2025-07-18 08:42:09.249 | DEBUG | modules.connect:on_decode   :990 - client_id:000 - chunk 完成解码, 识别结果不变
2025-07-18 08:42:09.655 | DEBUG | modules.connect:on_check    :628 - client_id:000 - >>> [解析] 第30个数据包, 累计帧数: 1178
2025-07-18 08:42:09.655 | DEBUG | modules.connect:on_decode   :961 - client_id:000 - 所需帧数: 67, 目前帧数: 1178, 开始解码
2025-07-18 08:42:09.656 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 000 获取现有会话: zh_encoder - 8b0dbba2, 池大小: 1
2025-07-18 08:42:09.724 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_encoder - 8b0dbba2, 当前可用: 1/1
2025-07-18 08:42:09.725 | DEBUG | modules.decoder:decode_chunk:493 - client_id:000 - 当前推理累计时点: 288
2025-07-18 08:42:09.725 | DEBUG | modules.decoder:decode_chunk:494 - client_id:000 - ctc 搜索起始时点: 272 搜索片段长度: tensor([16])
2025-07-18 08:42:09.725 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 000 获取现有会话: zh_ctc - 563076ce, 池大小: 1
2025-07-18 08:42:09.726 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_ctc - 563076ce, 当前可用: 1/1
2025-07-18 08:42:09.798 | DEBUG | modules.decoder:search      :374 - ctc token&time: [(1522, 12), (5380, 17), (364, 22), (2181, 26), (448, 32), (4765, 35), (4935, 40), (39, 57), (1041, 62), (1241, 66), (1899, 71), (4416, 76), (1720, 81), (4765, 85), (67, 90), (363, 96), (1542, 99), (3185, 104), (30, 108), (58, 112), (68, 116), (5268, 121), (4851, 139), (1287, 144), (2085, 148), (3730, 154), (1720, 178), (3300, 183), (3437, 188), (3957, 193), (2158, 197), (39, 201), (1229, 208), (3730, 211), (753, 216), (1199, 221), (102, 245), (1227, 249), (36, 254), (4765, 271), (2085, 275), (1720, 279), (3185, 283)]
2025-07-18 08:42:09.799 | DEBUG | modules.decoder:decode_chunk:505 - ctc result: "很高兴来到这里，与大家探讨我这为关心的一个主题，那就是美，我研究艺术与审美哲学，事实上，这是我的"
2025-07-18 08:42:09.799 | DEBUG | modules.connect:on_result   :430 - client_id:000 - <<< [发送] 第15个数据包, 更新识别结果: "很高兴来到这里，与大家探讨我这为关心的一个主题，那就是美，我研究艺术与审美哲学，事实上，这是我的"
2025-07-18 08:42:10.056 | DEBUG | modules.connect:on_check    :628 - client_id:000 - >>> [解析] 第31个数据包, 累计帧数: 1181
2025-07-18 08:42:10.056 | DEBUG | modules.connect:on_decode   :961 - client_id:000 - 所需帧数: 67, 目前帧数: 1181, 开始解码
2025-07-18 08:42:10.056 | DEBUG | modules.decoder:decode      :536 - client_id:000 - 第18个chunk, 原始帧: 1152~1181,  torch.Size([1, 29, 80])
2025-07-18 08:42:10.057 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 000 获取现有会话: zh_encoder - 8b0dbba2, 池大小: 1
2025-07-18 08:42:10.111 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_encoder - 8b0dbba2, 当前可用: 1/1
2025-07-18 08:42:10.112 | DEBUG | modules.decoder:decode_chunk:493 - client_id:000 - 当前推理累计时点: 294
2025-07-18 08:42:10.112 | DEBUG | modules.decoder:decode_chunk:494 - client_id:000 - ctc 搜索起始时点: 288 搜索片段长度: tensor([6])
2025-07-18 08:42:10.113 | DEBUG | modules.onnx_session_pool:get_session :189 - 客户 000 获取现有会话: zh_ctc - 563076ce, 池大小: 1
2025-07-18 08:42:10.127 | DEBUG | modules.onnx_session_pool:release_session:264 - 释放会话: zh_ctc - 563076ce, 当前可用: 1/1
2025-07-18 08:42:10.187 | DEBUG | modules.decoder:search      :374 - ctc token&time: [(1522, 12), (5380, 17), (364, 22), (2181, 26), (448, 32), (4765, 35), (4935, 40), (39, 57), (1041, 62), (1241, 66), (1899, 71), (4416, 76), (1720, 81), (4765, 85), (67, 90), (363, 96), (1542, 99), (3185, 104), (30, 108), (58, 112), (68, 116), (5268, 121), (4851, 139), (1287, 144), (2085, 148), (3730, 154), (1720, 178), (3300, 183), (3437, 188), (3957, 193), (2158, 197), (39, 201), (1229, 208), (3730, 211), (753, 216), (1199, 221), (102, 245), (1227, 249), (36, 254), (4765, 271), (2085, 275), (1720, 279), (3185, 283), (1381, 288), (213, 293)]
2025-07-18 08:42:10.187 | DEBUG | modules.decoder:decode_chunk:505 - ctc result: "很高兴来到这里，与大家探讨我这为关心的一个主题，那就是美，我研究艺术与审美哲学，事实上，这是我的工作"
2025-07-18 08:42:10.187 | INFO  | modules.connect:on_decode   :982 - client_id:000 - *** 最后一个数据包完成解码 ***
2025-07-18 08:42:10.187 | DEBUG | modules.connect:on_result   :430 - client_id:000 - <<< [发送] 第16个数据包, 更新识别结果: "很高兴来到这里，与大家探讨我这为关心的一个主题，那就是美，我研究艺术与审美哲学，事实上，这是我的工作"
2025-07-18 08:42:10.187 | INFO  | modules.connect:on_result   :432 - client_id:000 - <<< [响应] 最终识别结果: 很高兴来到这里，与大家探讨我这为关心的一个主题，那就是美，我研究艺术与审美哲学，事实上，这是我的工作
2025-07-18 08:42:10.188 | DEBUG | server :receive     :322 - client_id:000 - 已发送最后一个识别结果, 主动关闭客户连接
2025-07-18 08:42:10.188 | DEBUG | modules.connect:disconnect  :292 - client_id:000 - 开始断开连接，当前活跃连接数: 1
2025-07-18 08:42:10.190 | DEBUG | modules.connect:disconnect  :309 - client_id:000 - 开始清理客户端状态和缓存
2025-07-18 08:42:10.190 | DEBUG | modules.decoder:__del__     :454 - ASRDecoder 显式释放资源
2025-07-18 08:42:10.190 | DEBUG | modules.connect:disconnect  :328 - client_id:000 - 已经断开连接，活跃连接数已更新: 0
2025-07-18 08:42:10.190 | INFO  | server :receive     :329 - client_id: 000 - 关闭客户连接，当前活跃连接数: 0
2025-07-18 08:42:16.511 | INFO  | server :lifespan    :120 - 正在关闭ASR服务...
2025-07-18 08:42:21.511 | INFO  | server :lifespan    :127 - 系统监控已关闭
2025-07-18 08:42:26.512 | DEBUG | modules.onnx_session_pool:shutdown    :371 - 关闭模型 zh_encoder 的会话 8b0dbba2, 使用次数: 31
2025-07-18 08:42:26.512 | DEBUG | modules.onnx_session_pool:shutdown    :371 - 关闭模型 zh_ctc 的会话 563076ce, 使用次数: 31
2025-07-18 08:42:26.560 | INFO  | server :lifespan    :134 - ONNX会话池已关闭
2025-07-18 08:42:26.560 | INFO  | server :lifespan    :144 - 实时转写已关闭
2025-07-18 08:42:26.561 | INFO  | server :lifespan    :148 - Server shutdown, delete all global resources
