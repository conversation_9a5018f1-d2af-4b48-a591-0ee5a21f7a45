#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
编译验证脚本
检查.so文件是否可以正常导入和使用
"""

import sys
import os
import importlib.util

def verify_compiled_modules():
    """验证编译后的模块"""
    print("=" * 60)
    print("验证编译后的模块...")
    print("=" * 60)

    # 检查主要模块
    modules_to_check = [
        "server",
        "modules.config",
        "modules.asr_manager",
        "modules.connect",
        "modules.decoder",
        "modules.feature",
        "modules.logger",
        "modules.monitoring",
        "utils.common",
        "utils.search"
    ]

    success_count = 0
    total_count = len(modules_to_check)

    for module_name in modules_to_check:
        try:
            # 尝试导入模块
            module = __import__(module_name, fromlist=[''])
            print(f"✅ {module_name}: 导入成功")

            # 检查是否是编译后的模块
            if hasattr(module, '__file__') and module.__file__:
                if module.__file__.endswith('.so'):
                    print(f"   📦 编译文件: {module.__file__}")
                else:
                    print(f"   ⚠️  Python文件: {module.__file__}")

            success_count += 1

        except ImportError as e:
            print(f"❌ {module_name}: 导入失败 - {e}")
        except Exception as e:
            print(f"❌ {module_name}: 未知错误 - {e}")

    print("=" * 60)
    print(f"验证结果: {success_count}/{total_count} 模块导入成功")

    if success_count == total_count:
        print("🎉 所有模块编译成功！可以正常启动服务")
        print("启动命令: python -c 'from server import start_server; start_server()' zh")
    else:
        print("⚠️  部分模块编译失败，请检查错误信息")

    print("=" * 60)

    return success_count == total_count

if __name__ == "__main__":
    verify_compiled_modules()
