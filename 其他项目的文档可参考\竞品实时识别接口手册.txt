语音识别服务接口协议
2025-04-24

[toc]

实时语音识别
对长时间的语音数据流进行识别，适用于会议演讲、视频直播等长时间不间断识别的场景。

实时语音识别仅提供 WebSocket（流式）接口。一次连接中，理论上支持的音频时长上限约 37 小时。

服务地址
ws://{ip_address}:7100/ws/v1
请将 {ip_address} 替换为主机实际IP。

交互流程
流程图

1.开始并发送参数
客户端发起请求，服务端确认请求有效。其中在请求消息中需要进行参数设置。

发送参数（header对象）：

参数	类型	是否必需	说明
namespace	String	是	消息所属的命名空间：SpeechTranscriber 表示实时语音识别
name	String	是	事件名称：StartTranscription 表示开始阶段
发送参数（payload对象）：

参数	类型	是否必需	说明	默认值
lang_type	String	是	语种选项，参见《语音识别服务使用手册》“语言支持”部分	必填
format	String	否	音频编码格式，参见《语音识别服务使用手册》“音频编码”部分	pcm
sample_rate	Integer	否	模型采样率（而不是音频采样率）	16000
bit_depth	Integer	否	音频位深度，参见《语音识别服务使用手册》“基本术语”部分（2.5.12及以上版本支持）
支持的参数值为：8/16/24/32	16
enable_intermediate_result	Boolean	否	是否返回中间识别结果	2.6.1及以上版本：true
2.6.0及以下版本：false
enable_punctuation_prediction	Boolean	否	是否在后处理中添加标点	2.6.1及以上版本：true
2.6.0及以下版本：false
enable_inverse_text_normalization	Boolean	否	是否在后处理中执行ITN，参见《语音识别服务使用手册》“基本术语”部分	2.6.1及以上版本：true
2.6.0及以下版本：false
max_sentence_silence	Integer	否	语音断句检测阈值，静音时长超过该阈值会被认为断句
合法参数范围[200, 5000]，单位：毫秒
2.5.9及以下版本，参数范围[200, 2000]，单位：毫秒	450
enable_words	Boolean	否	是否开启返回最终结果词信息，参见《语音识别服务使用手册》“基本术语”部分	false
enable_intermediate_words	Boolean	否	是否开启返回中间结果词信息，参见《语音识别服务使用手册》“基本术语”部分	false
enable_modal_particle_filter	Boolean	否	是否开启语气词过滤，参见《语音识别服务使用手册》“实用功能”部分	2.6.1及以上版本：true
2.6.0及以下版本：false
hotwords_id	String	否	热词ID，参见《语音识别服务使用手册》“实用功能”部分以及《语音识别服务热词接口协议》	无
hotwords_list	List<String>	否	热词列表，仅在本次连接中生效，与hotwords_id参数同时存在时，以hotwords_list为准（2.5.12及以上版本支持）	无
hotwords_weight	Float	否	热词权重，取值范围[0.1, 1.0]	0.4
correction_words_id	String	否	强制替换词库ID，参见《语音识别服务使用手册》“实用功能”部分以及《语音识别服务强制替换接口协议》
支持使用多个强制替换词库ID，用竖线 | 分隔每个ID；all 表示使用全部强制替换词库ID	无
forbidden_words_id	String	否	敏感词ID，参见《语音识别服务使用手册》“实用功能”部分以及《语音识别服务敏感词接口协议》
支持使用多个敏感词ID，用竖线 | 分隔每个敏感词ID；all 表示使用全部敏感词ID	无
gain	Integer	否	表示振幅增益系数，范围[1, 20]，参见《语音识别服务使用手册》“基础知识”及“实用功能”部分
1表示不放大，2表示为原振幅2倍（放大1倍），以此类推	1
enable_sse	Boolean	否	是否开启 HTTP SSE 流式返回结果，参见本文“附加功能”部分	false
user_id	String	否	用户自定义信息，将在响应消息中原样返回
2.6.1及以上版本，最长不超过 128 个字符
2.6.0及以下版本，最长不超过 36 个字符	无
source_url	String	否	音频源地址，填写后将从该地址获取音频作为语音识别的输入（需指定 format 参数为支持的音频编码格式）
1. 支持RTSP流，参见《语音识别服务使用手册》“音频编码”部分
2. 支持网络声卡，参见《语音识别服务使用手册》“网络声卡”部分、本文“附加功能”部分。	无
enable_lang_label	Boolean	否	切换语种时在识别结果中返回语种代码，仅对英语混说（如中英混说、日英混说）语种生效。注意：开启此功能后，在切换语种时会产生响应延迟（2.5.11及以上版本支持）	false
paragraph_condition	Integer	否	控制分段字符数，同一个 speaker_id 内达到设定字数时在下一句中返回新的段落编号，范围[100, 2000]，范围外的值表示不开启此功能（2.5.11及以上版本支持）	0
decode_silence_segment	Boolean	否	控制是否将VAD判定的静音段也做语音识别处理，适合远场或外放录音环境（2.5.11及以上版本支持）	false
short_sentence_length	Integer	否	控制语音识别最终结果字符数，在 SentenceEnd 事件中返回较短的语音识别结果（开启后一定程度上会影响语音识别准确率）
合法参数范围[5, 20]，0表示不开启此功能
（2.6.2及以上版本支持）	0
发送示例：

{
    "header": {
        "namespace": "SpeechTranscriber",
        "name": "StartTranscription"
    },
    "payload": {
        "lang_type": "zh-cmn-Hans-CN",
        "format": "pcm",
        "sample_rate": 16000,
        "enable_intermediate_result": true,
        "enable_punctuation_prediction": true,
        "enable_inverse_text_normalization": true,
        "max_sentence_silence": 800,
        "enable_words":true,
        "user_id":"conversation_001"
    }
}
返回参数（header对象）：

参数	类型	说明
namespace	String	消息所属的命名空间：SpeechTranscriber 表示实时语音识别
name	String	事件名称：TranscriptionStarted 表示开始阶段
status	String	状态码
status_text	String	状态码说明
task_id	String	任务全局唯一ID，请记录该值，便于排查问题。
message_id	String	本次消息的ID
user_id	String	建立连接时传入的 user_id
返回示例：

{
    "header":{
        "namespace":"SpeechTranscriber",
        "name":"TranscriptionStarted",
        "appkey":"",
        "status":"00000",
        "status_text":"success",
        "task_id":"0220a729ac9d4c9997f51592ecc83847",
        "message_id":"49b680abe737488cf50f3cd9e3953b97",
        "user_id":"conversation_001"
    },
    "payload":{
        "index":0,
        "time":0,
        "begin_time":0,
        "speaker_id":"",
        "result":"",
        "confidence":0,
        "words":null
    }
}
2.发送语音数据与接收识别结果
注意：

如在连接时设置了 source_url 参数，则系统自动从指定的音频源获取语音数据，不需要再按照本章节内容发送语音数据。

循环发送语音数据，持续接收识别结果。建议每次发送数据包大小为7680 Byte。

实时语音识别服务的智能断句功能，会判断出一句话的开始与结束，以返回结果中的事件表示。 SentenceBegin 、 SentenceEnd 事件分别表示一句话的开始和结束，TranscriptionResultChanged 事件表示句子的中间识别结果。

SentenceBegin 事件
SentenceBegin 事件表示服务端检测到了一句话的开始。

返回参数（header对象）：

参数	类型	说明
namespace	String	消息所属的命名空间，SpeechTranscriber 表示实时语音识别
name	String	消息名称，SentenceBegin 表示一个句子的开始
status	Integer	状态码，表示请求是否成功，见服务状态码
status_text	String	状态消息
task_id	String	任务全局唯一ID，请记录该值，便于排查问题
message_id	String	本次消息的ID
user_id	String	建立连接时传入的 user_id
返回参数（payload对象）：

参数	类型	说明
lang_type	String	开启 enable_lang_label 时，返回语种代码（2.5.11及以上版本支持）
paragraph	Integer	段落编号，从1开始递增（2.5.11及以上版本支持）
index	Integer	句子编号，从1开始递增
time	Integer	当前已处理的音频时长，单位是毫秒
begin_time	Integer	当前句子对应的 SentenceBegin 事件的时间，单位是毫秒
speaker_id	String	说话人编号或网络声卡通道编号，见下文“附加功能-说话人编号”及“附加功能-网络声卡”部分
result	String	识别结果，有可能为空
confidence	Float	当前结果的置信度，范围 [0, 1]
volume	Integer	当前的音量，范围 [0, 100]
返回示例：

{
    "header": {
        "namespace": "SpeechTranscriber",
        "name": "SentenceBegin",
        "status": "00000",
        "status_text": "success",
        "task_id": "3ee284e922dd4554bb6ccda7989d1973",
        "message_id": "9b680abe73748f50f83cd9e3953b974c",
        "user_id":"conversation_001"
    },
    "payload": {
        "lang_type": "zh-cmn-Hans-CN",
        "paragraph": 1,
        "index": 1,
        "time": 240,
        "begin_time": 0,
        "speaker_id": "speaker1",
        "result": "",
        "confidence": 0,
        "volume": 0        
    }
}
TranscriptionResultChanged 事件
识别结果分为“中间结果”和“最终结果”，详细说明请参考《语音识别服务使用手册》“基本术语”部分。

TranscriptionResultChanged 事件表示识别结果发生了变化，即一句话的中间结果。

若 enable_intermediate_result 设置为 true ， 服务端会持续多次返回 TranscriptionResultChanged  消息，即识别的中间结果。

若 enable_intermediate_result 设置为 false ， 此步骤服务端不返回任何消息。

注意：

最后一次获取的中间结果与最终结果不一定相同，请以 SentenceEnd 事件对应的结果为最终识别结果。

返回参数（header对象）：

header 对象参数同上表（见 SentenceBegin 事件 header 对象返回参数）， name 为 TranscriptionResultChanged 表示句子的中间识别结果。

返回参数（payload对象）：

参数	类型	说明
lang_type	String	开启 enable_lang_label 时，返回语种代码（2.5.11及以上版本支持）
paragraph	Integer	段落编号，从1开始递增（2.5.11及以上版本支持）
index	Integer	句子编号，从1开始递增
time	Integer	当前已处理的音频时长，单位是毫秒
begin_time	Integer	本句对应的SentenceBegin事件的时间，单位是毫秒
speaker_id	String	说话人编号或网络声卡通道编号，见下文“附加功能-说话人编号”及“附加功能-网络声卡”部分
result	String	本句的中间识别结果
confidence	Float	当前结果的置信度，范围 [0, 1]
words	Dict[]	本句的中间结果词信息，仅当 enable_intermediate_words 设置为 true 时有结果返回
volume	Integer	当前的音量，范围 [0, 100]
其中，中间结果词信息对象结构为：

参数	类型	说明
word	String	文本
start_time	Integer	词开始时间，单位为毫秒
end_time	Integer	词结束时间，单位为毫秒
stable	Boolean	词状态是否已固定
如为 false，则后续中间结果中该词有可能会发生变化；否则不会变化。
返回示例：

{
    "header": {
        "namespace": "SpeechTranscriber",
        "name": "TranscriptionResultChanged",
        "status": "00000",
        "status_text": "success",
        "task_id": "3ee284e922dd4554bb6ccda7989d1973",
        "message_id": "749b680abe733cd488cf50f9e3953b97",
        "user_id":"conversation_001"
    },
    "payload": {
        "lang_type": "zh-cmn-Hans-CN",
        "paragraph": 1,
        "index": 1,
        "time": 1920,
        "begin_time": 0,
        "speaker_id": "speaker1",
        "result": "优化",
        "confidence": 1,
        "volume": 79,        
        "words": [
            {
                "word": "优化",
                "start_time": 0,
                "end_time": 1920,
                "stable": false
            }
        ]
    }
}
SentenceEnd 事件
SentenceEnd 事件表示服务端检测到了一句话的结束，并附带返回该句话的最终识别结果。

返回参数（header对象）：

header 对象参数同上表（见 SentenceBegin 事件 header 对象返回参数）， name 为 SentenceEnd 表示识别到句子的结束。

返回参数（payload对象）：

参数	类型	说明
lang_type	String	开启 enable_lang_label 时，返回语种代码（2.5.11及以上版本支持）
paragraph	Integer	段落编号，从1开始递增（2.5.11及以上版本支持）
index	Integer	句子编号，从1开始递增
time	Integer	当前已处理的音频时长，单位是毫秒
begin_time	Integer	本句对应的 SentenceBegin 事件的时间，单位是毫秒
speaker_id	String	说话人编号或网络声卡通道编号，见下文“附加功能-说话人编号”及“附加功能-网络声卡”部分
result	String	本句的最终识别结果
confidence	Float	当前结果的置信度，范围 [0, 1]
words	Dict[]	本句的最终结果词信息，仅当 enable_words 设置为 true 时有结果返回
volume	Integer	当前的音量，范围 [0, 100]
其中，最终结果词信息对象结构为：

参数	类型	说明
word	String	文本
start_time	Integer	词开始时间，单位为毫秒
end_time	Integer	词结束时间，单位为毫秒
type	String	类型
normal 表示普通文本，forbidden 表示敏感词，modal 表示语气词，punc 表示标点符号
返回示例：

{
    "header": {
        "namespace": "SpeechTranscriber",
        "name": "SentenceEnd",
        "status": "00000",
        "status_text": "success",
        "task_id": "3ee284e922dd4554bb6ccda7989d1973",
        "message_id": "749b680abe737488cf50f3cd9e3953b9",
        "user_id":"conversation_001"
    },
    "payload": {
        "lang_type": "zh-cmn-Hans-CN",
        "paragraph": 1,
        "index": 1,
        "time": 5670,
        "begin_time": 390,
        "speaker_id": "speaker1",
        "result": "优化和改进外商投资房地产管理。",
        "confidence": 0.9,
        "volume": 76,
        "words": [{
            "word": "优化",
            "start_time": 390,
            "end_time": 1110,
            "type": "normal"
        }, {
            "word": "和",
            "start_time": 1110,
            "end_time": 1440,
            "type": "normal"
        }, {
            "word": "改进",
            "start_time": 1440,
            "end_time": 2130,
            "type": "normal"
        }, {
            "word": "外商投资",
            "start_time": 2160,
            "end_time": 3570,
            "type": "normal"
        }, {
            "word": "房地产",
            "start_time": 3600,
            "end_time": 4290,
            "type": "normal"
        }, {
            "word": "管理",
            "start_time": 4290,
            "end_time": 4860,
            "type": "normal"
        },  {
            "word": "。",
            "start_time": 4290,
            "end_time": 4860,
            "type": "punc"
        }]
    }
}
附加功能
以下 4 项附加功能仅在实时语音识别中可用。

注意：

如在连接时设置了 source_url 参数，则强制分句、自定义说话人编号功能不可用。

强制分句
在发送语音数据的过程中，发送 SentenceEnd 事件，服务端将强制在当前位置进行断句处理。服务器处理断句后，客户端将收到一个 SentenceEnd 消息，带有该句的最终识别结果。此功能在使用网络声卡时不可用。

发送示例：

{
    "header": {
        "namespace": "SpeechTranscriber",
        "name": "SentenceEnd"
    }
}
自定义说话人编号
在发送语音数据前，发送 SpeakerStart 事件并指定 speaker_id 作为说话人编号，服务端会将一个 SpeakerStart 事件至下一个 SpeakerStart 或 StopTranscription 事件之间的语音数据视为指定的说话人，并将在识别结果的 speaker_id 字段返回该信息。此功能在使用网络声卡时不可用。

注意：

speaker_id 最长支持 36 个字符，超出部分将被截断并舍弃。
如果在 SpeakerStart 事件中没有传入 speaker_id 参数，返回结果中的 speaker_id 将为空值。
SpeakerStart 事件会触发强制分句。因此，请仅在切换说话人前，发送一次 SpeakerStart 事件。

发送示例：

尖括号内表示音频数据包，花括号内表示 JSON 数据包

<二进制音频数据包1>

{
    "header": {
        "namespace": "SpeechTranscriber",
        "name": "SpeakerStart"
    },
    "payload": {
        "speaker_id": "001"
    }
}

<二进制音频数据包2>

<...>

<二进制音频数据包n>

{
    "header": {
        "namespace": "SpeechTranscriber",
        "name": "SpeakerStart"
    }
    "payload": {
        "speaker_id": "002"
    }
}

<二进制音频数据包n+1>

<二进制音频数据包n+2>

SSE 方式返回结果
当 enable_sse = true 时，建立实时语音识别 WebSocket 连接、返回 TranscriptionStarted 事件后，另一客户端可通过以下 HTTP 请求行来获取识别结果：

GET http://server_ip:7100/getAsrResult?task_id=******
识别结果通过 Server-sent events（SSE）流式返回，返回格式与WebSocket 返回内容一致。识别结束时自动断开连接。

快速测试示例（Linux）：

curl -X GET 'http://localhost:7100/getAsrResult?task_id=******'
快速测试示例（Windows）：

curl.exe -X GET "http://localhost:7100/getAsrResult?task_id=******"
使用网络声卡
实时语音识别支持将网络声卡（Audio Network Interface, ANI）作为语音识别的音频源，系统将从指定声卡和通道编号获取单路（或多路）音频并进行语音识别处理。目前支持以下型号的网络声卡：

source_url 格式	source_url 示例	format字段支持
ani01://通道1[|通道2|通道n…]@声卡IP	ani01://0|2|4@********
含义：从IP为********的网络声卡获取编号为0、2、4的三个通道音频作为语音识别输入	pcm
发送示例：

{
    "header": {
        "namespace": "SpeechTranscriber",
        "name": "StartTranscription"
    },
    "payload": {
        "lang_type": "zh-cmn-Hans-CN",
        "format": "pcm",
        "sample_rate": 16000,
        "enable_intermediate_result": true,
        "enable_punctuation_prediction": true,
        "enable_inverse_text_normalization": true,
        "source_url":"ani01://0|2|4@********"
    }
}
识别结果中的 speaker_id 字段将返回通道编号信息，同一个音频源的识别结果包含相同的通道编号。

在一次连接中将返回与通道数相同数量的 TranscriptionStarted 和 TranscriptionCompleted 事件。

返回示例：

{
    "header":{
        <略>
    },
    "payload":{
        "paragraph": 1,
        "index":1,
        "time":0,
        "begin_time":0,
        "speaker_id":"2",
        "result":"2号通道的语音识别结果",
        "confidence":1,
        "volume": 76,        
        "words":null
    }
}
3. 保持连接（心跳机制）
在不发送音频时，需要每 10 秒内至少发送一次心跳包，否则将自动断开连接。建议客户端设置每 8 秒发送一次。

注意：

需要先发送 StartRecognition 或 StartTranscription 事件后，再发送心跳包。

如在连接时设置了 source_url 参数，则心跳机制不适用。

发送示例：

{
    "header":{
        "namespace":"SpeechTranscriber",
        "name":"Ping"
    }
}
返回示例：

{
    "header": {
        "namespace": "SpeechTranscriber",
        "name": "Pong",
        "task_id": "71c5cb9b-fbc3-4489-843c-e902b102a569",
        "message_id": "6f9ea191-1624-4d3c-9286-0003d323f731"
    },
    "payload": {}
}
如果 10 秒内没有任何数据传输给服务端，服务端将返回错误信息，随后自动断开连接。

4.停止与获取最终结果
客户端发送停止实时语音识别请求，通知服务端语音数据发送结束并停止语音识别。服务端返回最终识别结果，随后自动断开连接。

发送参数（header对象）：

参数	类型	说明
namespace	String	消息所属的命名空间，SpeechTranscriber 表示实时语音识别
name	String	消息名称，StopTranscription 表示停止实时语音识别
发送示例：

{
    "header": {
        "namespace": "SpeechTranscriber",
        "name": "StopTranscription"
    }
}
返回参数（header对象）：

参数	类型	说明
namespace	String	消息所属的命名空间，SpeechTranscriber 表示实时语音识别
name	String	消息名称，TranscriptionCompleted 表示识别完成
status	Integer	状态码，表示请求是否成功，见服务状态码
status_text	String	状态消息
task_id	String	任务全局唯一ID，请记录该值，便于排查问题
message_id	String	本次消息的ID
user_id	String	建立连接时传入的 user_id
返回参数（payload 对象）：与 SentenceEnd 事件格式相同，但 result 、words 字段有可能为空。

返回示例：

{
    "header":{
        "namespace":"SpeechTranscriber",
        "name":"TranscriptionCompleted",
        "status":"00000",
        "status_text":"success",
        "task_id":"3ee284e922dd4554bb6ccda7989d1973",
        "message_id":"7e729bf2d4064fee83143c4d962dc6f1",
        "user_id":""
    },
    "payload":{
        "index":1,
        "time":4765,
        "begin_time":180,
        "speaker_id":"",
        "result":"",
        "confidence": 0,
        "volume": 0,        
        "words":[]
    }
}
5.错误信息
错误信息通过 WebSocket 连接返回，然后服务端自动关闭连接。

返回示例：

{
    "header": {
        "namespace": "SpeechTranscriber",
        "name": "TaskFailed",
        "status": "20105",
        "status_text": "JSON serialization failed",
        "task_id": "df2c1604e31d4f46a7a064db73cd3b5e",
        "message_id": "",
        "user_id": ""
    },
    "payload": {
        "index": 1,
        "time": 0,
        "begin_time": 0,
        "speaker_id": "",
        "result": "",
        "confidence": 1,
        "volume": 0,        
        "words": null
    }
}
服务状态码
注意：

从 V2.6.0 版本开始，状态码分类进一步细化，具体变化对照请参考 V2.6.X 版本状态码更新对照表 。

状态码	原因
20001	参数解析错误（如 JSON 格式错误）（2.6.0 及以上版本）
20002、20003	文件处理失败
20105	参数解析错误（如 JSON 格式错误）（2.5.12 及以下版本）
20111	WebSocket 升级失败
20114	请求体为空
20115	语音长度或大小超限
20116	不支持的音频采样率
20190	参数缺失
20191	参数无效
20192	处理失败（解码器层）
20193	处理失败（服务层）
20194	连接超时（长时间未接收到客户端数据）
20195	其他错误
常见问题
如何使用 16000 Hz 的模型来处理 8000 Hz 的音频？

在 2.5.12 及以上版本中，如果授权中同时存在16000 Hz 和 8000 Hz 采样率的授权项，则启动 16000 Hz 模型后可以处理 8000 Hz 音频。使用时，如音频格式为 WAV，请传参数 sample_rate = 16000 、format = wav 并传入 8000 Hz 的 WAV 音频流；如音频格式为 PCM，请传参数 sample_rate = 16000 、format = pcm_8000 并传入 8000 Hz 的 PCM 音频流。

一句话识别
对 60s 内的短语音进行识别，适用于对话聊天，控制口令等较短的语音识别场景。

WebSocket 接口（流式）
服务地址
ws://{ip_address}:7100/ws/v1
请将 {ip_address} 替换为主机实际 IP。

交互流程
流程图

1. 开始并发送参数
客户端发起一句话识别请求，服务端确认请求有效。其中在请求消息中需要进行参数设置，以 JSON 格式发送。

发送参数（header对象）：

参数	类型	是否必需	说明
namespace	String	是	消息所属的命名空间：SpeechRecognizer 表示一句话识别
name	String	是	事件名称：StartRecognition 表示开始阶段
发送参数（payload对象）：

参数	类型	是否必需	说明	默认值
lang_type	String	是	语种选项，参见《语音识别服务使用手册》“语言支持”部分	必填
format	String	否	音频编码格式，参见《语音识别服务使用手册》“音频编码”部分	pcm
sample_rate	Integer	否	模型采样率（而不是音频采样率）	16000
bit_depth	Integer	否	音频位深度，参见《语音识别服务使用手册》“基本术语”部分（2.5.12及以上版本支持）
支持的参数值为：8/16/24/32	16
enable_intermediate_result	Boolean	否	是否返回中间识别结果	2.6.1及以上版本：true
2.6.0及以下版本：false
enable_punctuation_prediction	Boolean	否	是否在后处理中添加标点	2.6.1及以上版本：true
2.6.0及以下版本：false
enable_inverse_text_normalization	Boolean	否	是否在后处理中执行ITN，参见《语音识别服务使用手册》“基本术语”部分	2.6.1及以上版本：true
2.6.0及以下版本：false
enable_words	Boolean	否	是否开启返回词信息，参见《语音识别服务使用手册》“基本术语”部分	false
enable_intermediate_words	Boolean	否	是否开启返回中间结果词信息，参见《语音识别服务使用手册》“基本术语”部分	false
enable_modal_particle_filter	Boolean	否	是否开启语气词过滤，参见《语音识别服务使用手册》“实用功能”部分	2.6.1及以上版本：true
2.6.0及以下版本：false
hotwords_id	String	否	热词ID，参见《语音识别服务使用手册》“实用功能”部分以及《语音识别服务热词接口协议》	无
hotwords_list	List<String>	否	热词列表，仅在本次连接中生效，与hotwords_id参数同时存在时，以hotwords_list为准（2.5.12及以上版本支持）	空
hotwords_weight	Float	否	热词权重，取值范围[0.1, 1.0]	0.4
correction_words_id	String	否	强制替换词库ID，参见《语音识别服务使用手册》“实用功能”部分以及《语音识别服务强制替换接口协议》
支持使用多个强制替换词库ID，用竖线 | 分隔每个ID；all 表示使用全部强制替换词库ID	无
forbidden_words_id	String	否	敏感词ID，参见《语音识别服务使用手册》“实用功能”部分以及《语音识别服务敏感词接口协议》
支持使用多个敏感词ID，用竖线 | 分隔每个ID；all 表示使用全部敏感词ID	无
gain	Int	否	表示振幅增益系数，范围[1, 20]，参见《语音识别服务使用手册》“基础知识”及“实用功能”部分
1表示不放大，2表示为原振幅2倍（放大1倍），以此类推	1
max_suffix_silence	Float	否	语音后置静音检测阈值（秒），句尾静音时长超过该阈值会自动结束识别。
2.6.1及以上版本：范围 (0.0, 10.0]，参数值传0或不传该参数时，不开启后置静音检测功能，参数值传-1表示触发句尾VAD时立刻停止识别。
2.6.0及以下版本：范围 [0, 10] 整数，参数值传0或不传该参数时，不开启后置静音检测功能。	0
user_id	String	否	用户自定义信息，将在响应消息中原样返回
2.6.1及以上版本，最长不超过 128 个字符
2.6.0及以下版本，最长不超过 36 个字符	无
发送示例：

{
    "header": {
        "namespace": "SpeechRecognizer",
        "name": "StartRecognition"
    },
    "payload": {
        "lang_type": "zh-cmn-Hans-CN",
        "format": "pcm",
        "sample_rate": 16000,
        "enable_intermediate_result": true,
        "enable_punctuation_prediction": true,
        "enable_inverse_text_normalization": true,
        "enable_words":true,
        "user_id":"conversation_001"
    }
}
返回参数（header对象）：

参数	类型	说明
namespace	String	消息所属的命名空间：SpeechRecognizer 表示一句话识别
name	String	事件名称：RecognitionStarted 表示开始阶段
status	String	状态码
status_text	String	状态码说明
task_id	String	任务全局唯一ID，请记录该值，便于排查问题。
user_id	String	建立连接时传入的 user_id
返回示例：

{
    "header":{
        "namespace":"SpeechRecognizer",
        "name":"RecognitionStarted",
        "appkey":"",
        "status":"00000",
        "status_text":"success",
        "task_id":"0220a729ac9d4c9997f51592ecc83847",
        "message_id":"",
        "user_id":"conversation_001"
    },
    "payload":{
        "paragraph": 0,
        "index":0,
        "time":0,
        "begin_time":0,
        "speaker_id":"",
        "result":"",
        "confidence":0,
        "volume": 0,        
        "words":null
    }
}
2. 发送语音数据，接收识别结果
循环发送语音数据，持续接收识别结果。

识别结果分为“中间结果”和“最终结果”，详细说明请参考《语音识别服务使用手册》“基本术语”部分。

若 enable_intermediate_result 设置为 true ， 服务端会持续多次返回 RecognitionResultChanged 消息，即识别的中间结果。

若 enable_intermediate_result 设置为 false ， 此步骤服务端不返回任何消息。

注意：

最后一次获取的中间结果与最终结果不一定相同，请以 RecognitionCompleted 事件对应的结果为最终识别结果。

发送参数：分次发送二进制语音数据，建议每次发送数据包大小为7680 Byte。

返回参数（header对象）：

参数	类型	说明
namespace	String	消息所属的命名空间，SpeechRecognizer 表示一句话识别
name	String	消息名称，RecognitionResultChanged 表示识别的中间结果
status	Integer	状态码，表示请求是否成功，见服务状态码
status_text	String	状态消息
task_id	String	任务全局唯一ID，请记录该值，便于排查问题
message_id	String	本次消息的ID
user_id	String	建立连接时传入的 user_id
返回参数（payload对象）：

参数	类型	说明
index	Integer	对于一句话识别，恒为1
time	Integer	当前已处理的音频时长，单位是毫秒
begin_time	Integer	当前句子对应的开始时间，单位是毫秒
speaker_id	String	对于一句话识别，恒为空值
result	String	当前句子的识别结果
confidence	Float	当前结果的置信度，范围 [0, 1]
words	List<iWord>	当前句子的中间结果词信息，仅当 enable_intermediate_words 设置为 true 时有结果返回
volume	Integer	当前的音量，范围 [0, 100]
其中，中间结果词信息 iWord 对象：

参数	类型	说明
word	String	文本
start_time	Integer	词开始时间，单位为毫秒
end_time	Integer	词结束时间，单位为毫秒
stable	Boolean	词状态是否已固定
如为 false，则后续中间结果中该词有可能会发生变化；否则不会变化。
返回示例：

{
    "header": {
        "namespace": "SpeechRecognizer",
        "name": "RecognitionResultChanged",
        "status": "00000",
        "status_text": "success",
        "task_id": "0220a729ac9d4c9997f51592ecc83847",
        "message_id": "43u134hcih2lcp7q1c94dhm5ic2op9l2",
        "user_id":"conversation_001"
    },
    "payload": {
        "index": 1,
        "time": 1920,
        "begin_time": 0,
        "speaker_id": "",
        "result": "优化",
        "confidence": 1,
        "volume": 79,        
        "words": [
            {
                "word": "优化",
                "start_time": 0,
                "end_time": 1920,
                "stable": false
            }
        ]
    }
}
3. 停止与获取最终结果
客户端发送停止一句话识别请求，通知服务端语音数据发送结束并停止语音识别。服务端返回最终识别结果，随后自动断开连接。

发送参数（header对象）：

参数	类型	说明
namespace	String	消息所属的命名空间，SpeechRecognizer 表示一句话识别
name	String	消息名称，StopRecognition 表示停止识别
发送示例：

{
    "header": {
        "namespace": "SpeechRecognizer",
        "name": "StopRecognition"
    }
}
返回参数（header对象）：

参数	类型	说明
namespace	String	消息所属的命名空间，SpeechRecognizer 表示一句话识别
name	String	消息名称，RecognitionCompleted 表示识别完成
status	Integer	状态码，表示请求是否成功，见服务状态码
status_text	String	状态消息
task_id	String	任务全局唯一ID，请记录该值，便于排查问题
message_id	String	本次消息的ID
user_id	String	建立连接时传入的 user_id
返回参数（payload对象）：

参数	类型	说明
index	Integer	对于一句话识别，恒为1
time	Integer	当前已处理的音频时长，单位是毫秒
begin_time	Integer	当前句子对应的开始时间，单位是毫秒
speaker_id	String	对于一句话识别，恒为空值
result	String	当前句子的识别结果
confidence	Float	当前结果的置信度，范围 [0, 1]
words	List<eWord>	当前句子的词信息，仅当 enable_words设置为 true 时返回
volume	Integer	当前的音量，范围 [0, 100]
其中，最终结果词信息 ewords 对象：

参数	类型	说明
word	String	文本
start_time	Integer	词开始时间，单位为毫秒
end_time	Integer	词结束时间，单位为毫秒
type	String	类型
normal 表示普通文本，forbidden 表示敏感词，modal 表示语气词，punc 表示标点符号
返回示例：

{
    "header": {
        "namespace": "SpeechRecognizer",
        "name": "RecognitionCompleted",
        "status": "00000",
        "status_text": "success",
        "task_id": "0220a729ac9d4c9997f51592ecc83847",
        "message_id": "45kbrouk4yvz81fjueyao2s7y7o6gjz6",
        "user_id":"conversation_001"
    },
    "payload": {
        "index": 1,
        "time": 5292,
        "begin_time": 0,
        "speaker_id": "",
        "result": "优化和改进外商投资房地产管理。",
        "confidence": 0.9,
        "volume": 76,        
        "words": [{
            "word": "优化",
            "start_time": 390,
            "end_time": 1110,
            "type": "normal"
        }, {
            "word": "和",
            "start_time": 1110,
            "end_time": 1440,
            "type": "normal"
        }, {
            "word": "改进",
            "start_time": 1440,
            "end_time": 2130,
            "type": "normal"
        }, {
            "word": "外商投资",
            "start_time": 2160,
            "end_time": 3570,
            "type": "normal"
        }, {
            "word": "房地产",
            "start_time": 3600,
            "end_time": 4290,
            "type": "normal"
        }, {
            "word": "管理",
            "start_time": 4290,
            "end_time": 4860,
            "type": "normal"
        }]
    }
}
HTTP 接口（非流式）
HTTP 请求行
协议	URL	方法
HTTP/1.1	http://{ip_address}:7100/api/v1	POST
请将 {ip_address} 替换为主机实际 IP。

注意：
原有接口 /stream/v1 已在V2.6.0版本中弃用，请改用新接口地址 /api/v1

请求头部
HTTP 请求头部由“关键字/值”对组成，每行一对，关键字和值用英文冒号 : 分隔，设置内容如下：

名称	类型	是否必需	描述
Content-type	String	是	必须为"application/octet-stream"，表明 HTTP body 的数据为二进制数据
请求参数
客户端发送语音识别请求，其中在请求查询参数（query）中需要进行参数设置。各参数含义如下：

参数名称	类型	是否必需	说明	默认值
lang_type	String	是	识别语种，参见《语音识别服务使用手册》“语言支持”部分	zh-cmn-Hans-CN
format	String	否	音频编码格式，参见《语音识别服务使用手册》“音频编码”部分	pcm
sample_rate	Integer	否	音频采样率，参见《语音识别服务使用手册》“基本术语”部分	16000
bit_depth	Integer	否	音频位深度，参见《语音识别服务使用手册》“基本术语”部分（2.5.12及以上版本支持）
支持的参数值为：8/16/24/32	16
enable_punctuation_prediction	Boolean	否	是否在后处理中添加标点	2.6.1及以上版本：true
2.6.0及以下版本：false
enable_inverse_text_normalization	Boolean	否	是否在后处理中执行ITN，参见《语音识别服务使用手册》“基本术语”部分	2.6.1及以上版本：true
2.6.0及以下版本：false
enable_modal_particle_filter	Boolean	否	是否开启语气词过滤，参见《语音识别服务使用手册》“实用功能”部分	2.6.1及以上版本：true
2.6.0及以下版本：false
hotwords_id	String	否	热词ID，参见《语音识别服务使用手册》“实用功能”部分以及《语音识别服务热词接口协议》	无
hotwords_weight	Float	否	热词权重，取值范围[0.1, 1.0]	0.4
correction_words_id	String	否	强制替换词库ID，参见《语音识别服务使用手册》“实用功能”部分以及《语音识别服务强制替换接口协议》
支持使用多个强制替换词库ID，用竖线 | 分隔每个ID；all 表示使用全部强制替换词库ID	无
forbidden_words_id	String	否	敏感词ID，参见《语音识别服务使用手册》“实用功能”部分以及《语音识别服务敏感词接口协议》
支持使用多个敏感词ID，用竖线 | 分隔每个敏感词ID；all 表示使用全部敏感词ID	无
gain	Integer	否	表示振幅增益系数，范围[1, 20]，参见《语音识别服务使用手册》“基础知识”及“实用功能”部分
1表示不放大，2表示为原振幅2倍（放大1倍），以此类推	1
请求体
HTTP 请求体传入的是二进制音频数据，因此在 HTTP 请求头部中的 Content-Type 必须设置为 application/octet-stream 。

请求示例
curl --location --request POST 'http://127.0.0.1:7100/stream/v1?lang_type=zh-cmn-Hans-CN&format=pcm&sample_rate=16000&enable_punctuation_prediction=true&enable_inverse_text_normalization=true' \
--header 'Content-Type: application/octet-stream' \
--data-binary '@audio.pcm'
响应结果
响应结果在 Body 中。 响应结果字段如下：

名称	类型	描述
task_id	String	请记录该值，以便于排查错误
result	String	语音识别结果
status	String	服务状态码
message	String	服务状态描述
成功响应

body中 status 字段为 00000 表示成功响应。

{
    "task_id": "cf7b0c5339244ee29cd4e43fb97fd52e",
    "result": "优化和改进外商投资房地产管理。",
    "status":"00000",
    "message":"SUCCESS"
}
失败响应

body中 status 字段不为 00000 ，均为失败响应，可将该字段作为响应是否成功的标志。

服务状态码
注意：

从 V2.6.0 版本开始，状态码分类进一步细化，具体变化对照请参考 V2.6.X 版本状态码更新对照表 。

状态码	原因
20001	参数解析错误（如 JSON 格式错误）（2.6.0 及以上版本）
20002、20003	文件处理失败
20105	参数解析错误（如 JSON 格式错误）（2.5.12 及以下版本）
20111	WebSocket 升级失败
20114	请求体为空
20115	语音长度或大小超限
20116	不支持的音频采样率
20190	参数缺失
20191	参数无效
20192	处理失败（解码器层）
20193	处理失败（服务层）
20194	连接超时（长时间未接收到客户端数据）
20195	其他错误
常见问题
如何使用 16000 Hz 的模型来处理 8000 Hz 的音频？

在 2.5.12 及以上版本中，如果授权中同时存在16000 Hz 和 8000 Hz 采样率的授权项，则启动 16000 Hz 模型后可以处理 8000 Hz 音频。使用时，如音频格式为 WAV，请传参数 sample_rate = 16000 、format = wav 并传入 8000 Hz 的 WAV 音频流或音频文件；如音频格式为 PCM，请传参数 sample_rate = 16000 、format = pcm_8000 并传入 8000 Hz 的 PCM 音频流。

更新时间：2025-05-23