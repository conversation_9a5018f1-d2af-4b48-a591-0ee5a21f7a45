#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
ASR流式语音识别服务 Cython 封装脚本
使用Cython将Python源码编译为.so文件，提供代码保护和性能优化

使用方法:
1. 安装依赖: pip install cython
2. 编译所有模块: python setup.py build_ext --inplace
3. 清理编译文件: python setup.py clean --all

注意事项:
- 编译后的.so文件与Python版本和系统架构相关
- 需要保留原始的配置文件(.yaml)和其他资源文件
- 编译过程中会自动排除测试文件和示例文件
"""

import os
import sys
import glob
import shutil
from pathlib import Path
from distutils.core import setup
from distutils.extension import Extension
from Cython.Build import cythonize
from Cython.Distutils import build_ext


class CustomBuildExt(build_ext):
    """自定义构建扩展类，处理编译后的文件组织"""
    
    def run(self):
        # 执行标准构建
        build_ext.run(self)
        
        # 创建编译输出目录
        build_dir = Path("build/compiled")
        build_dir.mkdir(parents=True, exist_ok=True)
        
        # 移动编译后的.so文件到指定目录
        for ext in self.extensions:
            # 获取编译后的文件路径
            built_path = self.get_ext_fullpath(ext.name)
            if os.path.exists(built_path):
                # 计算目标路径
                rel_path = os.path.relpath(built_path, ".")
                target_path = build_dir / rel_path
                target_path.parent.mkdir(parents=True, exist_ok=True)
                
                # 复制文件
                shutil.copy2(built_path, target_path)
                print(f"已复制编译文件: {built_path} -> {target_path}")


def find_python_files():
    """查找需要编译的Python文件"""
    python_files = []
    
    # 需要编译的目录
    compile_dirs = [
        "modules",
        "utils"
    ]
    
    # 需要编译的根目录文件
    root_files = [
        "server.py",
        # "app.py",  # 简单的导入文件，可以不编译
    ]
    
    # 排除的文件模式
    exclude_patterns = [
        "*test*.py",
        "*example*.py", 
        "*demo*.py",
        "client.py",  # 测试客户端不需要编译
        "__pycache__",
        "*.pyc"
    ]
    
    # 添加根目录文件
    for file in root_files:
        if os.path.exists(file):
            python_files.append(file)
    
    # 遍历指定目录
    for directory in compile_dirs:
        if os.path.exists(directory):
            for root, dirs, files in os.walk(directory):
                # 排除__pycache__目录
                dirs[:] = [d for d in dirs if d != "__pycache__"]
                
                for file in files:
                    if file.endswith('.py'):
                        file_path = os.path.join(root, file)
                        
                        # 检查是否需要排除
                        should_exclude = False
                        for pattern in exclude_patterns:
                            if pattern in file_path.lower():
                                should_exclude = True
                                break
                        
                        if not should_exclude:
                            python_files.append(file_path)
    
    return python_files


def create_extensions():
    """创建Cython扩展"""
    python_files = find_python_files()
    extensions = []
    
    for py_file in python_files:
        # 将文件路径转换为模块名
        module_name = py_file.replace(os.sep, '.').replace('.py', '')
        
        # 创建扩展
        ext = Extension(
            name=module_name,
            sources=[py_file],
            language="c++",  # 使用C++编译器以支持更多特性
        )
        extensions.append(ext)
        print(f"添加编译目标: {py_file} -> {module_name}")
    
    return extensions


def copy_resource_files():
    """复制资源文件到编译目录"""
    build_dir = Path("build/compiled")
    
    # 需要复制的资源文件和目录
    resources = [
        "conf/",           # 配置文件目录
        "requirements.txt", # 依赖文件
        "README.md",       # 说明文件（如果存在）
    ]
    
    for resource in resources:
        src_path = Path(resource)
        if src_path.exists():
            dst_path = build_dir / resource
            
            if src_path.is_dir():
                # 复制目录
                if dst_path.exists():
                    shutil.rmtree(dst_path)
                shutil.copytree(src_path, dst_path)
                print(f"已复制目录: {src_path} -> {dst_path}")
            else:
                # 复制文件
                dst_path.parent.mkdir(parents=True, exist_ok=True)
                shutil.copy2(src_path, dst_path)
                print(f"已复制文件: {src_path} -> {dst_path}")


def create_launcher_script():
    """创建启动脚本"""
    build_dir = Path("build/compiled")
    launcher_content = '''#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
ASR流式语音识别服务启动脚本
编译版本启动器
"""

import sys
import os

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

# 导入并启动服务
try:
    from server import start_server
    start_server()
except ImportError as e:
    print(f"导入错误: {e}")
    print("请确保所有编译文件都在正确位置")
    sys.exit(1)
except Exception as e:
    print(f"启动错误: {e}")
    sys.exit(1)
'''
    
    launcher_path = build_dir / "start_server.py"
    with open(launcher_path, 'w', encoding='utf-8') as f:
        f.write(launcher_content)
    
    # 设置执行权限（Unix系统）
    if hasattr(os, 'chmod'):
        os.chmod(launcher_path, 0o755)
    
    print(f"已创建启动脚本: {launcher_path}")


def main():
    """主函数"""
    print("=" * 60)
    print("ASR流式语音识别服务 Cython 编译脚本")
    print("=" * 60)
    
    # 检查Cython是否安装
    try:
        import Cython
        print(f"Cython版本: {Cython.__version__}")
    except ImportError:
        print("错误: 未安装Cython，请运行: pip install cython")
        sys.exit(1)
    
    # 创建扩展
    extensions = create_extensions()
    
    if not extensions:
        print("警告: 未找到需要编译的Python文件")
        return
    
    print(f"找到 {len(extensions)} 个文件需要编译")
    
    # 设置编译选项
    compiler_directives = {
        'language_level': 3,        # Python 3
        'boundscheck': False,       # 关闭边界检查以提高性能
        'wraparound': False,        # 关闭负索引检查
        'initializedcheck': False,  # 关闭初始化检查
        'cdivision': True,          # 使用C除法
    }
    
    # 执行setup
    setup(
        name="asr-streaming-service",
        version="1.0.0",
        description="ASR流式语音识别服务",
        ext_modules=cythonize(
            extensions,
            compiler_directives=compiler_directives,
            build_dir="build/cython_build"
        ),
        cmdclass={'build_ext': CustomBuildExt},
        zip_safe=False,
    )
    
    # 复制资源文件
    print("\n复制资源文件...")
    copy_resource_files()
    
    # 创建启动脚本
    print("\n创建启动脚本...")
    create_launcher_script()
    
    print("\n" + "=" * 60)
    print("编译完成！")
    print("编译文件位置: build/compiled/")
    print("启动命令: python build/compiled/start_server.py")
    print("=" * 60)


if __name__ == "__main__":
    main()
