#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
ASR流式语音识别服务 Cython 封装脚本
使用Cython将Python源码编译为.so文件，提供代码保护和性能优化

使用方法:
1. 安装依赖: pip install cython
2. 编译所有模块: python setup.py build_ext --inplace
3. 清理编译文件: python setup.py clean --all

注意事项:
- 编译后的.so文件与Python版本和系统架构相关
- 需要保留原始的配置文件(.yaml)和其他资源文件
- 编译过程中会自动排除测试文件和示例文件
"""

import os
import sys
import glob
import shutil
from pathlib import Path
from distutils.core import setup
from distutils.extension import Extension
from Cython.Build import cythonize
from Cython.Distutils import build_ext


class CustomBuildExt(build_ext):
    """自定义构建扩展类，处理编译后的文件组织和替换"""

    def run(self):
        # 执行标准构建
        build_ext.run(self)

        # 记录需要删除的原始Python文件
        self.original_py_files = []

        # 处理编译后的文件
        for ext in self.extensions:
            # 获取编译后的文件路径
            built_path = self.get_ext_fullpath(ext.name)
            if os.path.exists(built_path):
                # 计算原始Python文件路径
                original_py_file = ext.sources[0]  # 原始.py文件
                self.original_py_files.append(original_py_file)

                # 计算目标.so文件路径（与原始.py文件同目录）
                target_dir = os.path.dirname(original_py_file)

                # 保持编译后的完整文件名（包含Python版本和架构信息）
                so_filename = os.path.basename(built_path)

                if target_dir:
                    # 子目录中的文件
                    target_path = os.path.join(target_dir, so_filename)
                else:
                    # 根目录文件
                    target_path = so_filename

                # 移动编译后的文件到目标位置（替换原始.py文件的目录）
                if built_path != target_path:
                    shutil.move(built_path, target_path)
                    print(f"已替换编译文件: {built_path} -> {target_path}")
                else:
                    print(f"编译文件已在正确位置: {target_path}")

        # 删除原始Python文件
        self.remove_original_files()

        # 清理编译临时文件
        self.cleanup_build_files()

    def remove_original_files(self):
        """删除原始Python文件"""
        for py_file in self.original_py_files:
            if os.path.exists(py_file):
                os.remove(py_file)
                print(f"已删除原始文件: {py_file}")

                # 同时删除对应的.pyc文件（如果存在）
                pyc_file = py_file + 'c'
                if os.path.exists(pyc_file):
                    os.remove(pyc_file)
                    print(f"已删除字节码文件: {pyc_file}")

    def cleanup_build_files(self):
        """清理编译过程中产生的临时文件"""
        # 删除.c文件（Cython生成的中间文件）
        for ext in self.extensions:
            original_py_file = ext.sources[0]

            # 删除对应的.c文件
            c_file = original_py_file.replace('.py', '.c')
            if os.path.exists(c_file):
                os.remove(c_file)
                print(f"已删除中间文件: {c_file}")

        # 清理build目录中的临时文件
        build_temp = Path("build")
        if build_temp.exists():
            # 删除所有临时编译文件
            for item in build_temp.rglob("*"):
                if item.is_file() and item.suffix in ['.o', '.c', '.cpp']:
                    item.unlink()
                    print(f"已删除临时文件: {item}")

        # 清理__pycache__目录
        self.cleanup_pycache()

        print("编译临时文件清理完成")

    def cleanup_pycache(self):
        """清理所有__pycache__目录"""
        pycache_dirs = []

        # 查找所有__pycache__目录
        for root, dirs, _ in os.walk("."):
            if "__pycache__" in dirs:
                pycache_path = os.path.join(root, "__pycache__")
                pycache_dirs.append(pycache_path)

        # 删除__pycache__目录
        for pycache_dir in pycache_dirs:
            if os.path.exists(pycache_dir):
                shutil.rmtree(pycache_dir)
                print(f"已删除缓存目录: {pycache_dir}")

        print(f"共清理了 {len(pycache_dirs)} 个__pycache__目录")


def find_python_files():
    """查找需要编译的Python文件"""
    python_files = []
    
    # 需要编译的目录
    compile_dirs = [
        "modules",
        "utils"
    ]
    
    # 需要编译的根目录文件
    root_files = [
        "server.py",
    ]
    
    # 排除的文件模式
    exclude_patterns = [
        "*test*.py",
        "*example*.py", 
        "*demo*.py",
        "client.py",  # 测试客户端不需要编译
        "__pycache__",
        "*.pyc"
    ]
    
    # 添加根目录文件
    for file in root_files:
        if os.path.exists(file):
            python_files.append(file)
    
    # 遍历指定目录
    for directory in compile_dirs:
        if os.path.exists(directory):
            for root, dirs, files in os.walk(directory):
                # 排除__pycache__目录
                dirs[:] = [d for d in dirs if d != "__pycache__"]
                
                for file in files:
                    if file.endswith('.py'):
                        file_path = os.path.join(root, file)
                        
                        # 检查是否需要排除
                        should_exclude = False
                        for pattern in exclude_patterns:
                            if pattern in file_path.lower():
                                should_exclude = True
                                break
                        
                        if not should_exclude:
                            python_files.append(file_path)
    
    return python_files


def create_extensions():
    """创建Cython扩展"""
    python_files = find_python_files()
    extensions = []
    
    for py_file in python_files:
        # 将文件路径转换为模块名
        module_name = py_file.replace(os.sep, '.').replace('.py', '')
        
        # 创建扩展
        ext = Extension(
            name=module_name,
            sources=[py_file],
            language="c++",  # 使用C++编译器以支持更多特性
        )
        extensions.append(ext)
        print(f"添加编译目标: {py_file} -> {module_name}")
    
    return extensions


def backup_original_files():
    """备份原始Python文件到backup目录"""
    backup_dir = Path("backup/original_py_files")
    backup_dir.mkdir(parents=True, exist_ok=True)

    python_files = find_python_files()

    for py_file in python_files:
        src_path = Path(py_file)
        if src_path.exists():
            # 保持目录结构
            rel_path = src_path
            dst_path = backup_dir / rel_path
            dst_path.parent.mkdir(parents=True, exist_ok=True)

            shutil.copy2(src_path, dst_path)
            print(f"已备份原始文件: {src_path} -> {dst_path}")

    print(f"原始Python文件已备份到: {backup_dir}")

def create_restore_script():
    """创建恢复脚本，用于恢复原始Python文件"""
    restore_script = '''#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
恢复原始Python文件脚本
用于将备份的.py文件恢复，删除编译的.so文件
"""

import os
import shutil
import glob
from pathlib import Path

def restore_files():
    """恢复原始Python文件"""
    backup_dir = Path("backup/original_py_files")

    if not backup_dir.exists():
        print("错误: 未找到备份目录")
        return False

    print("开始恢复原始Python文件...")

    # 删除编译文件（包含Python版本信息的.so和.pyd文件）
    compiled_files = []
    compiled_files.extend(glob.glob("*.so"))
    compiled_files.extend(glob.glob("*cpython*.so"))
    compiled_files.extend(glob.glob("modules/*.so"))
    compiled_files.extend(glob.glob("modules/*cpython*.so"))
    compiled_files.extend(glob.glob("utils/*.so"))
    compiled_files.extend(glob.glob("utils/*cpython*.so"))
    compiled_files.extend(glob.glob("*.pyd"))
    compiled_files.extend(glob.glob("*cpython*.pyd"))
    compiled_files.extend(glob.glob("modules/*.pyd"))
    compiled_files.extend(glob.glob("modules/*cpython*.pyd"))
    compiled_files.extend(glob.glob("utils/*.pyd"))
    compiled_files.extend(glob.glob("utils/*cpython*.pyd"))

    # 去重并删除
    unique_compiled_files = list(set(compiled_files))
    for compiled_file in unique_compiled_files:
        if os.path.exists(compiled_file):
            os.remove(compiled_file)
            print(f"已删除编译文件: {compiled_file}")

    # 恢复Python文件
    for backup_file in backup_dir.rglob("*.py"):
        rel_path = backup_file.relative_to(backup_dir)
        target_path = Path(rel_path)

        # 创建目标目录
        target_path.parent.mkdir(parents=True, exist_ok=True)

        # 复制文件
        shutil.copy2(backup_file, target_path)
        print(f"已恢复文件: {backup_file} -> {target_path}")

    print("恢复完成！")
    return True

if __name__ == "__main__":
    restore_files()
'''

    with open("restore_original_files.py", 'w', encoding='utf-8') as f:
        f.write(restore_script)

    # 设置执行权限（Unix系统）
    if hasattr(os, 'chmod'):
        os.chmod("restore_original_files.py", 0o755)

    print("已创建恢复脚本: restore_original_files.py")

def copy_resource_files():
    """复制资源文件（现在不需要复制到单独目录）"""
    # 由于.so文件直接替换.py文件，资源文件保持在原位置
    print("资源文件保持在原位置，无需复制")


def create_launcher_script():
    """创建启动脚本（现在直接在根目录）"""
    # 由于.so文件直接替换了.py文件，启动方式不变
    # 但我们创建一个验证脚本来确认编译是否成功

    verify_script = '''#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
编译验证脚本
检查.so文件是否可以正常导入和使用
"""

import sys
import os
import importlib.util

def verify_compiled_modules():
    """验证编译后的模块"""
    print("=" * 60)
    print("验证编译后的模块...")
    print("=" * 60)

    # 检查主要模块
    modules_to_check = [
        "server",
        "modules.config",
        "modules.asr_manager",
        "modules.connect",
        "modules.decoder",
        "modules.feature",
        "modules.logger",
        "modules.monitoring",
        "utils.common",
        "utils.search"
    ]

    success_count = 0
    total_count = len(modules_to_check)

    for module_name in modules_to_check:
        try:
            # 尝试导入模块
            module = __import__(module_name, fromlist=[''])
            print(f"✅ {module_name}: 导入成功")

            # 检查是否是编译后的模块
            if hasattr(module, '__file__') and module.__file__:
                if module.__file__.endswith('.so') or module.__file__.endswith('.pyd'):
                    print(f"   📦 编译文件: {module.__file__}")
                else:
                    print(f"   ⚠️  Python文件: {module.__file__}")

            success_count += 1

        except ImportError as e:
            print(f"❌ {module_name}: 导入失败 - {e}")
        except Exception as e:
            print(f"❌ {module_name}: 未知错误 - {e}")

    print("=" * 60)
    print(f"验证结果: {success_count}/{total_count} 模块导入成功")

    if success_count == total_count:
        print("🎉 所有模块编译成功！可以正常启动服务")
        print("启动命令: python -c 'from server import start_server; start_server()' zh")
    else:
        print("⚠️  部分模块编译失败，请检查错误信息")

    print("=" * 60)

    return success_count == total_count

if __name__ == "__main__":
    verify_compiled_modules()
'''

    with open("verify_compilation.py", 'w', encoding='utf-8') as f:
        f.write(verify_script)

    # 设置执行权限（Unix系统）
    if hasattr(os, 'chmod'):
        os.chmod("verify_compilation.py", 0o755)

    print("已创建验证脚本: verify_compilation.py")


def cleanup_all():
    """清理所有编译文件和临时文件"""
    print("开始清理编译文件...")

    # 清理编译文件（包含Python版本信息的.so文件）
    compiled_files = []

    # 查找所有编译后的文件（.so和.pyd，包含cpython版本信息）
    compiled_files.extend(glob.glob("*.so"))
    compiled_files.extend(glob.glob("*cpython*.so"))
    compiled_files.extend(glob.glob("modules/*.so"))
    compiled_files.extend(glob.glob("modules/*cpython*.so"))
    compiled_files.extend(glob.glob("utils/*.so"))
    compiled_files.extend(glob.glob("utils/*cpython*.so"))

    # Windows编译产物
    compiled_files.extend(glob.glob("*.pyd"))
    compiled_files.extend(glob.glob("*cpython*.pyd"))
    compiled_files.extend(glob.glob("modules/*.pyd"))
    compiled_files.extend(glob.glob("modules/*cpython*.pyd"))
    compiled_files.extend(glob.glob("utils/*.pyd"))
    compiled_files.extend(glob.glob("utils/*cpython*.pyd"))

    # 去重
    all_compiled_files = list(set(compiled_files))

    for compiled_file in all_compiled_files:
        if os.path.exists(compiled_file):
            os.remove(compiled_file)
            print(f"已删除编译文件: {compiled_file}")

    # 清理.c文件
    c_files = []
    c_files.extend(glob.glob("*.c"))
    c_files.extend(glob.glob("modules/*.c"))
    c_files.extend(glob.glob("utils/*.c"))

    for c_file in c_files:
        if os.path.exists(c_file):
            os.remove(c_file)
            print(f"已删除中间文件: {c_file}")

    # 清理build目录
    build_dir = Path("build")
    if build_dir.exists():
        shutil.rmtree(build_dir)
        print(f"已删除构建目录: {build_dir}")

    # 清理__pycache__目录
    for root, dirs, _ in os.walk("."):
        if "__pycache__" in dirs:
            pycache_path = os.path.join(root, "__pycache__")
            if os.path.exists(pycache_path):
                shutil.rmtree(pycache_path)
                print(f"已删除缓存目录: {pycache_path}")

    print("清理完成！")


def restore_from_backup():
    """从备份恢复原始Python文件"""
    backup_dir = Path("backup/original_py_files")

    if not backup_dir.exists():
        print("错误: 未找到备份目录，无法恢复")
        print("备份目录应该位于: backup/original_py_files/")
        return False

    print("开始从备份恢复原始Python文件...")

    # 首先清理现有的.so文件
    cleanup_all()

    # 恢复Python文件
    restored_count = 0
    for backup_file in backup_dir.rglob("*.py"):
        rel_path = backup_file.relative_to(backup_dir)
        target_path = Path(rel_path)

        # 创建目标目录
        target_path.parent.mkdir(parents=True, exist_ok=True)

        # 复制文件
        shutil.copy2(backup_file, target_path)
        print(f"已恢复文件: {backup_file} -> {target_path}")
        restored_count += 1

    print(f"恢复完成！共恢复了 {restored_count} 个Python文件")
    return True


def main():
    """主函数"""
    print("=" * 60)
    print("ASR流式语音识别服务 Cython 编译脚本")
    print("=" * 60)

    # 检查命令行参数
    if len(sys.argv) > 1 and sys.argv[1] == "clean":
        print("清理编译文件...")
        cleanup_all()
        return

    if len(sys.argv) > 1 and sys.argv[1] == "restore":
        print("恢复原始Python文件...")
        restore_from_backup()
        return

    # 检查Cython是否安装
    try:
        import Cython
        print(f"Cython版本: {Cython.__version__}")
    except ImportError:
        print("错误: 未安装Cython，请运行: pip install cython")
        sys.exit(1)
    
    # 创建扩展
    extensions = create_extensions()
    
    if not extensions:
        print("警告: 未找到需要编译的Python文件")
        return
    
    print(f"找到 {len(extensions)} 个文件需要编译")
    
    # 设置编译选项
    compiler_directives = {
        'language_level': 3,        # Python 3
        'boundscheck': False,       # 关闭边界检查以提高性能
        'wraparound': False,        # 关闭负索引检查
        'initializedcheck': False,  # 关闭初始化检查
        'cdivision': True,          # 使用C除法
    }
    
    # 执行setup
    setup(
        name="asr-streaming-service",
        version="1.0.0",
        description="ASR流式语音识别服务",
        ext_modules=cythonize(
            extensions,
            compiler_directives=compiler_directives,
            build_dir="build/cython_build"
        ),
        cmdclass={'build_ext': CustomBuildExt},
        zip_safe=False,
    )
    
    # 备份原始文件
    print("\n备份原始Python文件...")
    backup_original_files()

    # 创建恢复脚本
    print("\n创建恢复脚本...")
    create_restore_script()

    # 复制资源文件（实际上不需要复制）
    print("\n处理资源文件...")
    copy_resource_files()

    # 创建验证脚本
    print("\n创建验证脚本...")
    create_launcher_script()

    print("\n" + "=" * 60)
    print("编译完成！")
    print("✅ 原始.py文件已被.so文件替换")
    print("✅ 原始文件已备份到: backup/original_py_files/")
    print("✅ 可使用 python restore_original_files.py 恢复原始文件")
    print("")
    print("验证编译: python verify_compilation.py")
    print("启动服务: python -c 'from server import start_server; start_server()'")
    print("或直接: python server.py")
    print("")
    print("其他命令:")
    print("  python setup.py clean    # 清理所有编译文件")
    print("  python setup.py restore  # 恢复原始Python文件")
    print("=" * 60)


def print_usage():
    """打印使用说明"""
    print("ASR流式语音识别服务 Cython 编译脚本")
    print("")
    print("使用方法:")
    print("  python setup.py build_ext --inplace  # 编译并替换Python文件")
    print("  python setup.py clean                # 清理编译文件")
    print("  python setup.py restore              # 恢复原始Python文件")
    print("")
    print("编译后:")
    print("  - 原始.py文件被.so文件替换")
    print("  - 原始文件备份在 backup/original_py_files/")
    print("  - 使用 restore_original_files.py 可恢复原始文件")
    print("  - 使用 verify_compilation.py 验证编译结果")
    print("")
    print("注意事项:")
    print("  - 编译前请确保代码无语法错误")
    print("  - 编译后的.so文件与Python版本和系统架构相关")
    print("  - 配置文件(.yaml)和资源文件不会被编译")


if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] in ["-h", "--help", "help"]:
        print_usage()
    else:
        main()
