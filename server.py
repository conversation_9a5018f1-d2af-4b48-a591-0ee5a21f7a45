#! /user/bin/env python
#! -*- coding: utf-8 -*-
# File    :  server.py
# Time    :  2025/04/03 12:05:24
# Author  :  lh
# Version :  1.0
# Description:
"""
服务端主程序, 实现基于 WebSocket 的流式语音识别服务。
服务器初始化时会加载 ONNX 模型, 并为每个客户端维护独立的识别状态。
支持多客户端同时连接, 并提供流式识别结果。
注意：使用前需确保已通过 wenet/bin/export_onnx_cpu.py 导出相应模型文件:
encoder.onnx ctc.onnx decoder.onnx
"""

import os
import sys

dirpath =  os.path.abspath(os.path.dirname(sys.argv[0]))
envs = [dirpath]
envs_add = [path for path in envs if path not in sys.path]
sys.path = envs_add + sys.path

import asyncio
import json
import os
import time
from contextlib import asynccontextmanager

from fastapi import FastAPI, WebSocket, WebSocketDisconnect

from modules.asr_manager import ASRManager
from modules.config import ModuleManager, parse_args
from modules.connect import ConnectionManager
from modules.error_codes import ErrorCode
from modules.lid_manager import LIDManager
# 检查授权
from utils.verify_license import verify_license

if not verify_license():
    print(f"License check error.")
    exit(1)

# 定义全局变量
args = parse_args()
logger = None
HEARTBEAT_INTERVAL = None

# 定义全局组件（简化后的架构）
# LID管理器
LID_MANAGER = None
# ASR管理器（统一管理所有语种的模型、特征管道、词表等）
ASR_MANAGER = None
# 模块管理器
MODULE_MANAGER = None
# 连接管理器
manager = None

@asynccontextmanager
async def lifespan(app: FastAPI):
    """
    生命周期管理函数, 在应用启动时初始化资源, 在关闭时释放资源。
    Args:
        app: FastAPI 应用实例
    Returns:
        异步上下文管理器
    """
    global logger, HEARTBEAT_INTERVAL
    global LID_MANAGER, ASR_MANAGER, MODULE_MANAGER, manager
    
    HEARTBEAT_INTERVAL = args.server.heartbeat_interval

    # 创建模块管理器并初始化所有模块
    MODULE_MANAGER = ModuleManager(args.global_config)

    # 初始化所有模块（包括日志、监控、会话池等）
    MODULE_MANAGER.init_all_modules(args.lang_code, args.server.debug)

    # 获取logger实例
    logger = MODULE_MANAGER.get_logger()

    # 初始化ASR管理器
    ASR_MANAGER = ASRManager(args.global_config)

    # 根据模式加载模型
    if args.multi_lang:
        # 多语种模式：预加载所有支持的语种模型
        if not ASR_MANAGER.load_models('multi'):
            logger.error("多语种模式启动失败：无法加载任何语种模型")
            raise RuntimeError("多语种模式启动失败")
    else:
        # 单语种模式
        if not ASR_MANAGER.load_models(args.lang_code):
            logger.error(f"单语种模式启动失败：无法加载语种 {args.lang_code} 的模型")
            raise RuntimeError("单语种模式启动失败")

    # 初始化LID管理器（仅在多语种模式下需要）
    LID_MANAGER = None
    if args.multi_lang and args.lid.enabled and args.lid.model_path:
        try:
            LID_MANAGER = LIDManager(args.lid, ASR_MANAGER.supported_languages)
            logger.info(f"LID管理器初始化成功: {args.lid.model_path}")
        except Exception as e:
            logger.warning(f"LID管理器初始化失败: {e}")
            LID_MANAGER = None
    elif args.multi_lang:
        logger.info("多语种模式 LID功能未启用或未配置模型路径")
    else:
        logger.info("单语种模式 LID功能不可用")

    # 初始化连接管理器
    # 统一的初始化方式，所有配置都通过ASR管理器获取
    manager = ConnectionManager(args, ASR_MANAGER, LID_MANAGER)
    logger.info("Server start, init manager, LID_MANAGER, ASR_MANAGER\n\n")

    yield
    # unload
    logger = MODULE_MANAGER.get_logger()
    if logger:
        logger.info("正在关闭ASR服务...")

    # 关闭系统监控
    monitoring = MODULE_MANAGER.get_monitoring()
    if monitoring:
        monitoring.shutdown()
        if logger:
            logger.info("系统监控已关闭")

    # 关闭ONNX会话池
    session_pool = MODULE_MANAGER.get_session_pool()
    if session_pool:
        session_pool.shutdown()
        if logger:
            logger.info("ONNX会话池已关闭")

    if LID_MANAGER:
        del LID_MANAGER
        if logger:
            logger.info("语种检测已关闭")
    if ASR_MANAGER:
        ASR_MANAGER.cleanup()
        del ASR_MANAGER
        if logger:
            logger.info("实时转写已关闭")
    del manager
    manager, LID_MANAGER, ASR_MANAGER, MODULE_MANAGER = None, None, None, None
    if logger:
        logger.info("Server shutdown, delete all global resources")


app = FastAPI(
    lifespan=lifespan,
    title="流式语音识别服务",
    description="流式语音识别 WebSocket 服务接口",
    version="1.0"
)

# 数据传输规则通知接口
@app.get("/api/transmission-rules")
async def get_transmission_rules():
    """获取数据传输规则"""
    rules = {
        "audio_format": {
            "sample_rate": args.expected_sample_rate,
            "sample_width": args.expected_sample_width,
            "channels": args.expected_sample_channels,
            "encoding": "PCM",
            "data_encoding": "base64"
        },
        "packet_format": {
            "max_packet_size": args.expected_data_size,
            "packet_interval_ms": 400,
            "timeout_seconds": args.packet_interval
        },
        "protocol": {
            "websocket_endpoint": "/ws/{client_id}",
            "heartbeat_interval": args.heartbeat_interval,
            "supported_sample_rates": args.valid_sample_rate_list
        },
        "features": {
            "multi_language_support": args.multi_lang,
            "lid_enabled": hasattr(args, 'lid_model_path') and args.lid_model_path,
            "supported_languages": getattr(args, 'supported_languages', ['zh', 'en'])
        }
    }
    return rules

# 简单的健康检查接口（主服务端口）
@app.get("/health")
async def health_check():
    """简单的健康检查接口"""
    monitor = MODULE_MANAGER.get_monitoring() if MODULE_MANAGER else None
    if monitor:
        health_status = monitor.get_health_status()
        return {
            "status": health_status.status,
            "timestamp": health_status.timestamp,
            "uptime_seconds": health_status.uptime_seconds,
            "active_connections": health_status.active_connections
        }
    else:
        return {
            "status": "healthy",
            "timestamp": time.time(),
            "message": "监控未启用"
        }

@app.websocket("/ws/{client_id}")
async def websocket_endpoint(
    websocket: WebSocket, 
    client_id: str):
    """
    WebSocket 连接处理函数, 管理客户端连接和消息通信。
    Args:
        websocket: WebSocket 实例
        client_id: 客户端唯一标识
    Returns:
        异步协程
    """
    try:
        # 获取logger和monitor实例
        logger = MODULE_MANAGER.get_logger() if MODULE_MANAGER else None
        monitor = MODULE_MANAGER.get_monitoring() if MODULE_MANAGER else None

        # 更新监控统计
        if monitor:
            monitor.increment_request_count()

        # 初始化连接
        if logger:
            logger.debug(f"client_id:{client_id} - 开始初始化连接")

        await manager.connect(websocket, client_id)
        last_activity = time.time()

        if logger:
            logger.info(f"client_id:{client_id} - >>> [请求] 新客户连接，当前活跃连接数: {len(manager.client_states)}")

        # 更新活跃连接数
        if monitor:
            monitor.set_active_connections(len(manager.client_states))
    
        # 启动协程 receive 用于接受客户消息 并在合适时机向客户发送消息
        receive_task = asyncio.create_task(receive(websocket, client_id, last_activity))

        # 等待任务完成或中断
        done, pending = await asyncio.wait(
            {receive_task},
            return_when=asyncio.FIRST_COMPLETED
        )

        for task in pending:
            task.cancel()
    
    except Exception as e:
        if logger:
            logger.warning(f"client_id: {client_id} - 未预期的错误发生: {e}")
            logger.info(f"client_id: {client_id} - 关闭连接，清理资源")

        # 更新错误统计
        if monitor:
            monitor.increment_error_count()

        await manager.disconnect(client_id)

        # 更新活跃连接数
        if monitor:
            monitor.set_active_connections(len(manager.client_states))


async def receive(
    websocket: WebSocket,
    client_id: str,
    last_activity: float):
    """
    接收客户端消息并处理数据包。
    Args:
        websocket: WebSocket 实例
        client_id: 客户端唯一标识
        last_activity: 上次活动(即建立连接)的时间
    Returns:
        异步协程
    """
    # 获取logger实例
    logger = MODULE_MANAGER.get_logger() if MODULE_MANAGER else None

    try:
        while True:
            # 1. 接收 JSON 格式数据包
            # if logger:
            #     logger.debug(f"client_id:{client_id} - 等待接收数据包，超时时间: {HEARTBEAT_INTERVAL}秒")

            json_data = await asyncio.wait_for(
                    websocket.receive_json(),
                    timeout=HEARTBEAT_INTERVAL
            ) # 可能抛出 asyncio.TimeoutError 等待数据包超时 / json.JSONDecodeError (客户端发送了错误格式的数据包, 由客户端造成关闭连接)
            last_activity = time.time()  # 更新最后活动时间
            
            # 2. 解析数据包
            pass_check = await manager.on_check(client_id, json_data)
            if not pass_check:
                if logger:
                    logger.warning(f"client_id:{client_id} - 数据包解析失败, 服务端主动断开连接")
                raise WebSocketDisconnect

            # 3. 持续解码和返回结果
            decode_success, decode_something = await manager.on_decode(client_id)
            if not decode_success:
                if logger:
                    logger.warning(f"client_id:{client_id} - 服务段解码失败, 服务端主动断开连接")
                raise WebSocketDisconnect

            if decode_something:
                await manager.on_result(decode_something, client_id)


            # 4. 发送最后结果后关闭连接
            # 正常来说 客户端会关闭连接 就会接收不到客户数据包 这样就触发 WebSocketDisconnect 退出了轮询,
            # 以防万一 客户端不关闭连接 服务段在发送了最后识别结果之后 关闭ws连接
            if manager.client_states[client_id]['is_final_result']:
                if logger:
                    logger.debug(f"client_id:{client_id} - 已发送最后一个识别结果, 主动关闭客户连接")
                raise WebSocketDisconnect

    # 关闭连接触发
    except WebSocketDisconnect:
        await manager.disconnect(client_id)
        if logger:
            logger.info(f"client_id: {client_id} - 关闭客户连接，当前活跃连接数: {len(manager.client_states)}")
            
        # 更新活跃连接数
        monitor = MODULE_MANAGER.get_monitoring() if MODULE_MANAGER else None
        if monitor:
            monitor.set_active_connections(len(manager.client_states))

    # 等待数据包超时
    except asyncio.TimeoutError:
        # 心跳检查
        if logger:
            logger.debug(f"心跳检查 {time.time() - last_activity}")
        if time.time() - last_activity > HEARTBEAT_INTERVAL:
            if logger:
                logger.warning(f"client_id:{client_id} - 心跳超时，未在{HEARTBEAT_INTERVAL}秒内接收到数据包。")
            await manager.on_error(ErrorCode.HEARTBEAT_TIMEOUT, client_id, timeout=HEARTBEAT_INTERVAL)
            # manager.on_error 中会调用 disconnect 和 更新活跃连接数, 无需再调用一次

    # receive_json() 触发
    except json.JSONDecodeError as e:
        if logger:
            logger.warning(f"client_id:{client_id} - 接收数据包失败(json.JSONDecodeError), 断开连接: {e}")
        await manager.on_error(ErrorCode.JSON_DECODE_ERROR, client_id, details=str(e))
        # manager.on_error 中会调用 disconnect 和 更新活跃连接数, 无需再调用一次

def start_server():
    """
    启动 FastAPI 服务器。
    Returns:
        None
    """
    import uvicorn

    # 在启动时创建临时的模块管理器来获取logger
    print(f"Run server on {args.server.host}:{args.server.port}")
    uvicorn.run("server:app", host=args.server.host, port=args.server.port, reload=False, workers=1)

if __name__ == '__main__':
    start_server()
