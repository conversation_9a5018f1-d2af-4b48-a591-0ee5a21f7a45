服务部署文档（Linux版）
2025-04-02

[TOC]

1. 系统及软件要求
指标	要求
系统版本	CentOS 7.4 以上 或 Ubuntu 18.04 以上，64位系统
依赖软件	unzip
如选配声纹组件用于说话人归档，还需 Docker 18.09 及以上版本
系统时间	CST时区（北京时间），误差在5分钟内
CPU	建议 4 核以上，主频 2.0 GHz 以上
x86（AMD64）架构需支持 avx2、fma、sse4.2 指令集
ARMv8 架构需支持 neon、vfpv4 指令集
内存	16 GB 以上
磁盘	500 GB 以上
占用端口	7100（默认值），注意在防火墙中放行该端口（TCP连接）
如选配声纹组件用于说话人归档，还将占用7810端口
其他	需使用 root 账户或者使用 sudo 权限
请部署实施前检查相关项是否满足要求。

请使用 root 账户，或者非 root 账户用 sudo 权限执行以下所有命令。

2. 资源包
部署须知：

基础资源文件必须存在，是服务引擎的基础。

语音识别服务不同语种对应不同的模型文件。

语音识别服务的语种可以叠加使用，根据提供的模型和授权来决定。

资源包名称	说明
check_env_linux_{arch}.tar.gz	环境检测工具，{arch}表示平台架构，区分amd64和ARMv8
asr-integrated.zip	主程序及安装程序
asr-model_xxxxx.iso
asr_xxxxx.zip
asr-file_xxxxx.zip	语音识别模型（及安装程序）
3. 文件结构
文件名	说明
ffmpeg/	用于转换音频文件格式（高级版无该组件）
hotword/	热词功能组件
log/	日志文件
model/	模型文件
audio/	保存的音频文件
lib/	动态链接库
script/	预置脚本
asr-integrated	主程序
service.toml	语音识别服务配置文件
start.conf	启动项文件
*.db 等文件	数据库文件
4. 快速入门
请使用 root 账户，或者非 root 账户用 sudo 权限执行以下所有命令。

4.1. 运行环境检测（基础）
解压环境检测文件，以 amd64（x86_64）架构为例：

tar xzvf check_env_linux_amd64.tar.gz
进入解压后的目录，运行./check_env.sh，若各项均为 pass ，则说明硬件环境支持系统运行。若存在 error 则说明硬件环境无法满足系统运行条件。

完成后，按回车键退出环境检测。

4.2. 安装语音识别系统主程序
下载得到压缩文件：asr-integrated.zip 。

解压后，得到主程序目录 asr-integrated ，以解压到 /work/ 目录为例：
unzip asr-integrated.zip -d /work/
进入主程序目录，运行安装程序 install.run
cd /work/asr-integrated
./install.run
根据安装向导进行安装，首先选择安装的版本，输入对应数字并回车。如不清楚，请与售前工程师确认适合您的版本。

选项	功能
Pro Edition	一句话识别、实时语音识别、录音文件转写
Premium Edition	一句话识别、实时语音识别
File Edition	录音文件转写
Cancel	退出安装程序
再次确认安装版本后，输入 Y 开始安装，否则输入其他字符退出安装向导。

Please enter your choice: (1~4)
1) Pro Edition
2) Premium Edition
3) File Edition
4) Cancel
#? 1【※此处仅作为示例，请根据资源清单中的说明文字或联系售前确认适合您的版本】
Please comfirm to install Pro Edition (Y/N):y
显示 Installation completed. 表示主程序安装完成。下一步需要安装模型，请按部署文档继续操作。

4.3. 安装语音识别模型
模型有 iso 格式和 zip 格式两种打包形式（以技术人员提供的为准），安装方式有所区别。

4.3.1. iso 格式的安装包
如果下载得到的文件是 iso 格式镜像文件（如 asr-model_zh-cmn-Hans-CN.iso ），则：

确认镜像将要加载的位置，如 /mnt ，或使用 mkdir 命令创建一个空目录。
以回环模式加载镜像文件至指定位置
mount asr-model_zh-cmn-Hans-CN.iso /mnt -o loop
​ 提示 mounted read-only 表示加载完成。

创建符号链接到主程序下的 model 目录
ln -s /mnt/* /work/asr-integrated/model/
进入模型目录，运行模型安装程序 install.run
cd /work/asr-integrated/model
sh install.run
根据安装向导进行安装，安装向导将自动检测已安装的主程序版本。输入 Y 确认安装版本。
Pro Edition is detected.
Please comfirm to install zh-cmn-Hans-CN model for Pro Edition (Y/N):Y
显示 Installation completed. 表示模型安装完成。
执行 umount /mnt 移除镜像挂载，移除挂载后即可移动或删除 .iso 镜像文件。
4.3.2. zip 格式的压缩包
如果下载得到的文件是 zip 格式压缩文件（如asr_zh-cmn-Hans-CN.zip ），则：

将文件解压到主程序下的 model 目录

unzip -o asr_zh-cmn-Hans-CN.zip -d /work/asr-integrated/model/
如有多个模型压缩文件，则需要重复上述步骤，全部解压到相应路径。

4.4. 启动项配置
V2.5 及以上版本：请跳过本节设置。

V2.4 及以下版本：默认配置为汉语（普通话），如只需要汉语（普通话）功能，请跳过本节设置。

进入主程序目录 asr-integrated ，编辑启动项文件 start.conf

vi start.conf
打开文件后，文件内容格式如下，分别表示启动汉语（普通话）实时语音识别/一句话识别、汉语（普通话）录音文件转写功能。

asr,zh-cmn-Hans-CN
asr-file,zh-cmn-Hans-CN
每行信息对应一个语种的功能，格式为：功能代码,语种代码 。 语种代码参见《语音识别服务使用手册》“语言支持”部分。功能代码如下：
实时语音识别/一句话识别：asr
录音文件转写：asr-file
根据所需功能配置启动项文件，配置完成后按 :wq 保存退出。
请根据模型、授权和实际使用需求，添加或删除启动项配置文件中的内容。
4.5. 首次启动
进入主程序目录 asr-integrated，运行 ./script/start.sh 启动程序。

4.5.1. 命令行方式激活
首次启动，系统将显示以下字样，表示需要激活。请按照屏幕提示，将屏幕上的内容复制并发送给商务人员，获取授权码。在此期间，请勿退出程序。

提示：

如不方便从命令行复制出机器码，可参考 4.5.2. 网页方式激活 部分进行操作。

[root@server asr-integrated]# ./script/start.sh
Loading ASR model ...
请将以下内容发给商务：M90C5iimJUBg9QhmrPtK+AiSLUt85dtJbet787/D9Mo6qCiMgjR0rTvaBWVjroeKxgHrOdrULmfc2Z+HPwCR9oQA6iHmJ8oFpX5U/PxttGAI6RreNSGcoRDWu0ldeM/e4eDTE3LUFUC6rGzVotPAMWFiqlIuKWh70InGW596lHSOEwWMV85iwCGkufJw3DJsbMS0aRpOz7Kir6IvWIOqNrdLG3PmYnUGYkx58AWx0PUSZjwk6zeCAbQNuZAjFOGhTBXMpYwOp+5G2twhFUEb3OCwLTD0toLmSLqAQEJcOl2o1wObKXCKqQ==
请填入商务提供的授权码:
▋
将收到的授权码粘贴到命令行窗口中。然后按回车键进入下一步。

注意：

复制操作：鼠标选中文字即为复制成功，无需其他操作。
粘贴操作：单击鼠标右键，或按 Shift + Insert(Ins) 快捷键。

请勿使用其他快捷键复制机器码、粘贴授权码，会引起程序退出导致机器码/授权码失效。

等待几秒后，显示以下内容，表示模型加载成功，语音识别服务（ASR）和录音文件转写（ASR-FILE）服务已正确启动。屏幕将显示服务所在的端口，默认为7100。

[root@server asr-integrated]# ./script/start.sh
Loading ASR model ...
Loading zh-cmn-Hans-CN model... [OK]
ASR model was loaded successfully.
Loading ASR-FILE model ...
Loading zh-cmn-Hans-CN model... [OK]
ASR-FILE model was loaded successfully.
Loading VAD model ...
VAD model was loaded successfully.
Loading hotwords ...
Hotwords were loaded successfully.
ASR module was launched successfully, listening on http://localhost:7100 and ws://localhost:7100
ASR-FILE module was launched successfully, listening on http://localhost:7100
如果输入授权码后提示错误，表示激活失败。此时请重新运行 ./script/start.sh 进行激活操作。

注意：

完成激活后，激活信息将保存在本机，请勿更换硬件，否则可能导致激活失效。

4.5.2. 网页方式激活
保持语音识别服务为运行状态，如未运行可进入主程序目录 asr-integrated，运行 ./script/start.sh 启动程序。

在本机或局域网内其他主机的浏览器中输入 语音识别服务器IP:端口号/admin ，如 ***********:7100/admin，打开管理后台。

点击 生成机器码 按钮，将机器码复制（或扫描右侧二维码）并发送给商务人员，获取授权码。在此期间，请勿重复生成机器码，否则之前生成的机器码将失效。

收到授权码后，复制并粘贴到下方文本框中，然后点击 确定 按钮完成激活。以网页方式激活后，需要手动重启语音识别服务。

4.6. 关闭程序
如需停止语音识别服务，可直接按 Ctrl + C，即可退出程序。

4.7. 再次启动
直接运行 ./script/start.sh 启动程序，等待屏幕显示成功字样的提示信息后，即可正常使用。

5. 进阶操作
请使用 root 账户，或者非 root 账户用 sudo 权限执行以下所有命令。

5.1. 命令参数
格式：./script/start.sh [option]

参数	功能
-d	后台启动服务
-c	查询授权信息
-g	生成机器码
-p="授权码"	输入授权码
--conf="文件路径"	服务配置文件路径
--remove-license	清除授权（请谨慎操作）
--export-license="文件路径（精确到目录）"	导出授权备份文件
仅支持导出永久授权的备份文件
非永久授权仅能导出演示文件用于调试接口，演示文件不包含授权信息，无法用于实际用途
--import-license="文件路径（精确到文件）"	导入授权备份文件
仅支持导入永久授权的备份文件
非永久授权仅能导入演示文件用于调试接口，演示文件不包含授权信息，无法用于实际用途
--factory-reset	恢复出厂设置（请谨慎操作）
将重置配置文件，清除数据库、用户数据、日志，不会影响授权、模型配置文件
-v	查看组件版本号
-m	查看模型文件大小（2.5.11及以上版本支持）
-h	查看帮助
5.2. 后台启动服务
以下两种方式支持的功能稍有不同，请根据您的需要，在两种方式中任选其一。

方式	后台启动	进程守护（意外退出后自动重启）	开机自启动	日志管理
简易方法	√	√	×	×
注册系统服务	√	√	√	√
5.2.1 简易方法
运行 ./script/start.sh -d 命令可实现简单的后台启动、进程守护。

关闭后台启动的服务，请运行 ./script/stop.sh 命令。

5.2.2. 注册系统服务
通过脚本将程序注册为系统服务，借助 systemd 组件实现开机自启、后台启动、进程守护、日志管理等高级功能。

运行 ./script/systemd.run 脚本（依赖 systemd 组件）。

根据向导进行选择，包括注册为系统服务、注销系统服务。输入选项回车后输入 Y 确认执行。

选项	文字描述	实际操作
1	Register System Service	1. 注册为系统服务
2. 设置为开机自启（如不需要开机自启，请在注册系统服务后执行 systemctl disable asr 取消设置）
3. 设置进程守护，意外退出时自动拉起服务
2	Unregister System Service	1. 停止语音识别服务
2. 从系统服务中删除
3. 取消开机自启
3	Cancel	1. 无操作，退出向导
示例：

Please enter your choice: (1~3)
1) Register System Service    3) Cancel
2) Unregister System Service
#? 1
Please comfirm to Register System Service (Y/N):y
Register system service successfully.
提示：

如 Linux 服务端没有安装 systemd，可以使用 yum install systemd 或 apt install systemd 等命令安装，安装 systemd 后再运行 ./script/systemd.run 脚本。

注册为系统服务后，可以使用 systemctl 命令控制语音识别服务。

# 启动服务
systemctl start asr
# 停止服务
systemctl stop asr
# 重启服务
systemctl restart asr
# 查看服务状态
systemctl status asr
# 取消开机自启动
systemctl disable asr
# 设置开机自启动
systemctl enable asr
# 查看实时日志
journalctl -fu asr
5.3. 修改服务端口
默认服务端口为 7100 。如需修改，请使用文本编辑器打开根目录下的 service.toml 文件，修改第二行 port =  后的端口号，如：port = 8888 。

保存退出后重启服务即可生效。

5.4. 服务管理接口
5.4.1. 授权管理相关接口
*******. 获取机器码
请求行

GET /auth/machine_code
返回参数

名称	类型	说明
status	string	状态码
message	string	状态码描述
data	string	机器码
*******. 输入授权码
请求行

POST /auth/auth_code
请求参数（JSON）

名称	类型	是否必需	说明	默认值
auth_code	string	是	授权码	必填
返回参数

名称	类型	说明
status	string	状态码
message	string	状态码描述
data	string	null
5.4.1.3. 查询授权状态
请求行

GET /auth/auth_status
返回参数

名称	类型	说明
status	string	状态码
message	string	状态码描述
data	object[]	机器码
├─ expiration_time	string	过期时间
"Persistent authorization."表示永久授权
├─ expired	boolean	是否已过期
├─ max_connections	integer	最大并发数量
├─ name	string	服务名称
├─ sample_rate	integer	采样率
├─ service	string	服务代码
5.4.1.4. 导出授权备份文件
仅支持导出永久授权的备份文件；非永久授权仅能导出演示文件用于调试接口，演示文件不包含授权信息，无法用于实际用途。

请求行

GET /auth/license
返回参数

授权备份文件 license.dat

5.4.1.5. 导入授权备份文件
仅支持导入永久授权的备份文件；非永久授权仅能导入演示文件用于调试接口，演示文件不包含授权信息，无法用于实际用途。

请求行

POST /auth/license
请求参数（form-data）

名称	类型	是否必需	说明	默认值
file	file	是	授权备份文件	必填
返回参数

名称	类型	说明
status	string	状态码
message	string	状态码描述
data	object	数据
5.4.2. 信息查询相关接口
5.4.2.1. 查询系统版本
请求行

GET /version
返回参数

名称	类型	说明
status	string	状态码
message	string	状态码描述
data	object[]	数据
├─ file	string	组件名
├─ version	string	组件版本号
组件包括：

组件名	说明
bin	语音识别服务主程序
libe2easr.so	语音识别动态链接库
libvad.so	VAD动态链接库
5.4.2.2. 查询实时语音识别、一句话识别连接状态
也可用于检查连通性。

请求行

GET /asr
返回参数

名称	类型	说明
status	string	状态码
message	string	状态码描述
data	object[]	数据
├─ lang_type	string	语种
├─ name	string	语种显示名
├─ sample_rate	int	采样率
├─ max_connections	int	最大连接数
├─ current_connections	int	当前连接数
5.4.2.3. 查询录音文件转写连接状态
也可用于检查连通性。

请求行

GET /asr-file
返回参数

名称	类型	说明
status	string	状态码
message	string	状态码描述
data	object[]	数据
├─ lang_type	string	语种
├─ name	string	语种显示名
├─ sample_rate	int	采样率
├─ max_connections	int	最大连接数
├─ current_connections	int	当前连接数
├─ queue_task_numbers	int	队列中的任务数量
├─ queue_task_duration	int	队列中的任务总时长
5.4.2.4. 获取引擎保存的音频文件
一句话识别、实时语音识别完成后、录音文件转写任务创建后，都将在工作目录下临时保存音频文件用于调试。

注意：

音频文件将按照配置的清理规则进行定期定量清理，因此在生产环境中不建议直接从引擎获取音频文件，推荐做法是由客户端转储音频数据。

请求行

GET /audio/{task_id_short}
其中 task_id_short 表示去掉连字符的 task_id ，共 32 位字符。

返回参数

WAV 格式音频文件。

5.5. 将软件移动或复制到其他设备
将 asr-integrated 目录打包并复制到目标设备后，解压缩至合适位置。
提示：

其中 audio 和 log 文件夹内保存的是音频和日志文件，可能占用空间较大，视情况可以不复制到目标设备。

在目标设备上执行 ./script/start.sh --remove-license，否则将报错。
参考 4.5. 首次启动 部分完成激活。
注意：如安装了声纹组件，需在旧设备上解绑，在新设备上重新安装声纹组件并激活。
5.6. 可选功能配件
需要先安装主程序和相应语种模型，再安装可选功能配件。

5.6.1. 标点模型更新
*******. 法律标点模型
将技术人员提供的 post_*.zip 文件解压到主程序下的相应模型目录，注意将 /work/asr-integrated 替换为实际工作路径、将 {lang_type} 替换为实际的语种代码：

#实时语音识别、一句话识别
unzip -o post_zh-Hans.zip -d /work/asr-integrated/model/asr/{lang_type}/

#录音文件转写
unzip -o post_zh-Hans.zip -d /work/asr-integrated/model/asr-file/{lang_type}/
重启引擎后生效。

*******. 通用标点大模型
V2.5.12 及以上版本支持，仅支持 Linux x86 系统。

将技术人员提供的 large-punc_for_linux_x86.zip 文件解压到主程序下的相应模型目录，注意将 /work/asr-integrated 替换为实际工作路径、将 {lang_type} 替换为实际的语种代码：

#实时语音识别、一句话识别
unzip -o large-punc_for_linux_x86.zip -d /work/asr-integrated/model/asr/{lang_type}/post/

#录音文件转写
unzip -o large-punc_for_linux_x86.zip -d /work/asr-integrated/model/asr-file/{lang_type}/post/
修改配置文件

实时语音识别、一句话识别： /work/asr-integrated/model/asr/{lang_type}/config
录音文件转写： /work/asr-integrated/model/asr-file/{lang_type}/config
添加一行内容：

large-punc-path = ./post/large-punc
保存并退出编辑器，重启引擎后生效。

5.6.2. 自动热词提取组件
V2.5.6 及以上版本支持，仅支持汉语（普通话及方言）。仅 x86 版本需要增加该组件。Linux ARMv8 架构 V2.5.6 及以上版本版本已内置自动热词提取模块，不需要手动增加组件。

将技术人员提供的 module_hwe_zh-Hans.zip 文件解压到主程序目录下，注意将 /work/asr-integrated 替换为实际工作路径：

unzip -o module_hwe_zh-Hans.zip -d /work/asr-integrated/
运行安装脚本：

cd /work/asr-integrated/
./install_hwe.run
根据提示选择语种或手动输入语种代码。

显示 Installed HotWords Extraction (HWE) module successfully. 字样表示安装成功，重启引擎后生效。

5.6.3. 声纹组件
V2.5.10 及以上版本支持。需要提前安装 Docker 18.09 或更高版本。

安装加密锁驱动并激活

声纹组件默认提供软锁形式的加密锁，请参考 加密锁操作手册 完成加密锁驱动安装和激活。如需使用硬锁，请与售前工程师联系。

查看激活状态

通过如下命令可以列举许可信息：

ssclt -l
返回结果中的 LicenseId 表示已有的许可 ID。使用声纹组件，需要加密锁中存在许可ID 813000 。

导入 Docker 镜像

将技术人员提供的 module_vpr_{version}.zip 文件解压到主程序目录下，注意替换其中的 /work/asr-integrated 为实际工作路径：

unzip -o module_vpr_1.0.5.zip -d /work/asr-integrated/
docker import module/module_vpr_1.0.5.tar module_vpr:1.0.5
启动 Docker 容器

docker run -itd --name=module_vpr --shm-size=2g -p 7810:7810  \
--restart=always --privileged -v /tmp:/tmp \
--log-opt max-size=10m --log-opt max-file=3 \
module_vpr:1.0.5 /bin/bash -c "cd /home/<USER>/ && ./run.sh"
查看启动状态

docker logs -f module_vpr
出现以下信息表示启动成功：

[INFO]: vad model is loaded.
[INFO]: emb model is loaded.
[INFO]: cluster model is loaded.
6. 常见问题
6.1. 安装模型时显示 Please install ASR System first!

主程序未安装，参考 4.2. 安装语音识别系统主程序  完成安装操作。

确认模型安装文件的位置，应该在主程序下的 model 目录下。

6.2. 发送请求没有响应

请检查服务是否正常运行，执行 ps -ef|grep asr-integrated 命令查看是否有对应进程。

请检查防火墙设置，需放行 TCP 协议的对应服务端口（默认为7100）。

6.3. 如何配置 HTTPS/WSS 连接

语音识别引擎本身只提供 HTTP/WS 接口，您可通过 Nginx 配置 SSL 证书并对原接口进行反向代理，实现 HTTPS/WSS 接口。

Nginx 配置文件参考：

server {
    listen       443 ssl;
    server_name   mydomain.com;   #Nginx所在服务器的域名
    ssl_certificate     /etc/nginx/ssl/nginx.cer; # 证书路径（域名对应的证书）
    ssl_certificate_key  /etc/nginx/ssl/nginx.key; # 私钥路径（域名对应的私钥）
    client_max_body_size 10240m;   #为录音文件转写服务放开上传文件大小的限制
    location /asr/ {                  
        proxy_pass http://127.0.0.1:7100/;   #语音识别服务的地址
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }
}
上述配置生效后，您可以：

请求 wss://mydomain.com/asr/ws/v1 作为实时语音识别、一句话识别服务的访问地址。
请求 https://mydomain.com/asr/request 作为创建录音文件转写任务的访问地址。
请求 https://mydomain.com/asr/version 作为查询版本号的访问地址。
其他接口以此类推。
6.4. 在大小核架构的 ARM 处理器上如何提高运行速度

当使用标准版语音识别模型时，语音识别引擎需要运行在处理器的大核心上。

首先执行以下命令，查看 CPU 的大小核情况。

cat /proc/cpuinfo|grep "CPU part"|uniq
以 RK3588 芯片为例，输出结果为：

CPU part        : 0xd05
CPU part        : 0xd0b
说明本机有 0xd05 以及 0xd0b 两种核心。

在下表中查询具体指代型号：

CPU Part	CPU型号	类型		CPU Part	CPU型号	类型
0xd01	鲲鹏 920	【大核】		0xd47	CORTEX_A710	【大核】
0xd03	CORTEX_A53	小核		0xd48	CORTEX_X2	【大核】
0xd04	CORTEX_A35	小核		0xd49	NEOVERSE_N2	【大核】
0xd05	CORTEX_A55	小核		0xd4b	CORTEX_A78C	【大核】
0xd07	CORTEX_A57	小核		0xd4c	CORTEX_X1C	【大核】
0xd08	CORTEX_A72	【大核】		0xd4e	CORTEX_X3	【大核】
0xd09	CORTEX_A73	【大核】		0xd4f	NEOVERSE_V2	【大核】
0xd0a	CORTEX_A75	【大核】		0xd81	CORTEX_A720	【大核】
0xd0b	CORTEX_A76	【大核】		0xd82	CORTEX_X4	【大核】
0xd0c	NEOVERSE_N1	【大核】		0xd84	NEOVERSE_V3	【大核】
0xd0d	CORTEX_A77	【大核】		0xd85	CORTEX_X925	【大核】
0xd40	NEOVERSE_V1	【大核】		0xd87	CORTEX_A725	【大核】
0xd41	CORTEX_A78	【大核】		0xd8e	NEOVERSE_N3	【大核】
0xd42	CORTEX_A78AE	【大核】		0x663	飞腾 D2000 / FT-2000 / S2500	小核
0xd44	CORTEX_X1	【大核】		0x862	飞腾 D3000 / S5000C	【大核】
0xd46	CORTEX_A510	小核				
查表可知，本机的 0xd0b 为大核（Cortex A76），而 0xd05 为小核（Cortex A55）。

注意：

如果本机所有核心都为大核，则无需执行以下操作。
如果本机所有核心都为小核，则只能使用精简版语音识别模型。

如果本机同时具有大小核，则继续以下操作。

将大核的 CPU Part 写入 CPU_PART 变量，并运行以下代码（注意修改 CPU_PART 变量值）

CPU_PART="填入本机大核对应的CPU_Part"   #例如：CPU_PART="0xd0b"
awk -v p="${CPU_PART,,}" '$1=="processor" {r=tolower($3)}$0~"CPU part.*" p {printf "%s%s",(f++?",":""),r}END {print ""}' /proc/cpuinfo
输出为 4,5,6,7 ，表示本机 4~7 核心为大核。

备份启动脚本和注册服务脚本。

cd /work/asr-integrated/script
cp start.sh start.sh.bak && cp systemd.run systemd.run.bak
修改 start.sh 文件，将第 18 行左右直到结尾改为以下内容（注意修改 taskset -c 后的内容）

if command -v nice >/dev/null 2>&1; then
    nice -n -20 taskset -c 4,5,6,7 ./asr-integrated "$1"   # `taskset -c` 后的内容改为大核的编号，即第3步的输出结果
fi
修改 systemd.run 文件，将第 52 行左右的部分改为以下内容（注意修改 taskset -c 后的内容）

ExecStart=/bin/bash -c "$nice -n -20 taskset -c 4,5,6,7 ${ROOT_PATH}/asr-integrated"  # `taskset -c` 后的内容改为大核的编号，即第3步的输出结果
保存后重新执行 ./install.run 完成服务注册。

启动语音识别服务，进程将绑定在大核心上运行，可发挥设备最大性能。

6.5. 用 systemctl 命令启动服务时阻塞

检查操作系统启动状态

systemctl is-system-running
如果开机很长时间之后仍显示 starting ，说明操作系统启动阻塞，无法进入语音识别服务依赖的系统模式。

手动切换操作系统模式

systemctl isolate multi-user.target 
再次执行第一步的命令，检查操作系统启动状态，如状态变为 degraded ，此时可手动执行 systemctl start asr 启动语音识别服务。

更新时间：2025-05-23