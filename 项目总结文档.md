# ASR流式语音识别服务项目总结文档

## 项目概述

### 项目名称
ASR流式语音识别服务 (ASR Streaming Recognition Service)

### 项目描述
基于WebSocket的实时流式语音识别服务，支持多语种识别、语种自动检测、高并发处理等功能。采用ONNX模型推理，提供高性能的语音转文字服务。

### 技术架构
- **后端框架**: FastAPI + WebSocket
- **推理引擎**: ONNX Runtime
- **音频处理**: PyTorch + Torchaudio + Kaldi
- **语种识别**: 集成LID (Language Identification) 模块
- **并发处理**: ONNX会话池 + 异步处理
- **监控系统**: 内存监控 + 健康检查
- **日志系统**: Loguru 结构化日志

## 核心功能特性

### 1. 实时流式识别
- **WebSocket连接**: 支持长连接实时音频流处理
- **分包传输**: 400ms音频包，支持实时率控制
- **流式解码**: CTC前缀束搜索算法，实时返回识别结果
- **断句功能**: 基于时间间隔的智能断句

### 2. 多语种支持
- **支持语种**: 中文(zh)、英文(en)、俄语(ru)、哈萨克语(kk)、柯尔克孜语(kkin)、维吾尔语(ug)
- **语种检测**: 自动语种识别(LID)，支持语种切换
- **配置管理**: 每个语种独立配置文件
- **模型管理**: 统一ASR管理器，支持多语种模型加载

### 3. 高并发架构
- **会话池**: ONNX会话池管理，支持动态扩容
- **连接管理**: 统一连接管理器，支持多客户端并发
- **异步处理**: 全异步架构，提高并发性能
- **资源管理**: 智能缓存清理，防止内存泄漏

### 4. 音频处理能力
- **音频格式**: 支持PCM、WAV格式
- **采样率**: 支持8kHz、16kHz、44.1kHz
- **特征提取**: FBANK、MFCC、Log-Mel特征
- **VAD检测**: WebRTC VAD语音活动检测

### 5. 系统监控
- **健康检查**: HTTP健康检查接口
- **性能监控**: CPU、内存、磁盘使用率监控
- **连接统计**: 实时连接数、请求数统计
- **错误追踪**: 详细错误码和错误信息

## 系统架构设计

### 模块架构
```
ASR服务
├── server.py              # 主服务器入口
├── modules/               # 核心模块
│   ├── config.py         # 配置管理
│   ├── asr_manager.py    # ASR模型管理
│   ├── lid_manager.py    # 语种识别管理
│   ├── connect.py        # 连接管理
│   ├── decoder.py        # 解码器
│   ├── feature.py        # 特征提取
│   ├── onnx_session_pool.py # 会话池
│   ├── monitoring.py     # 系统监控
│   ├── logger.py         # 日志系统
│   ├── error_codes.py    # 错误码定义
│   └── symbol_table.py   # 符号表
├── utils/                # 工具模块
│   ├── context_graph.py  # 上下文图
│   ├── search.py         # 搜索算法
│   ├── vad_utils.py      # VAD工具
│   ├── file_utils.py     # 文件工具
│   └── verify_license.py # 授权验证
└── conf/                 # 配置文件
    ├── config.yaml       # 全局配置
    └── *.yaml            # 各语种配置
```

### 数据流程
1. **连接建立**: 客户端通过WebSocket连接到服务器
2. **音频接收**: 服务器接收Base64编码的音频数据包
3. **特征提取**: 将音频转换为FBANK等特征
4. **语种识别**: LID模块识别音频语种(可选)
5. **模型推理**: 使用对应语种的ONNX模型进行推理
6. **结果解码**: CTC解码得到文本结果
7. **结果返回**: 通过WebSocket返回识别结果

## 配置系统

### 全局配置 (config.yaml)
- **服务器配置**: 主机、端口、心跳间隔
- **音频配置**: 采样率、数据包大小、缓存管理
- **LID配置**: 语种识别模型和参数
- **会话池配置**: 最大会话数、超时时间
- **监控配置**: 内存阈值、健康检查端口
- **日志配置**: 日志级别、轮转策略

### 语种配置 (zh.yaml, en.yaml等)
- **模型路径**: ONNX模型文件路径
- **设备配置**: CPU/GPU/NPU选择
- **优化选项**: FP16、量化等
- **热词配置**: 自定义热词文件
- **后处理**: 大小写转换、符号处理

## API接口设计

### WebSocket接口
- **连接地址**: `ws://host:port/ws/{client_id}`
- **消息格式**: JSON格式
- **请求参数**:
  - `index`: 数据包索引
  - `audio_data`: Base64编码音频
  - `sample_rate`: 采样率
  - `is_final`: 是否最后一包
- **响应格式**:
  - `code`: 状态码
  - `result`: 识别结果
  - `voice_id`: 客户端ID
  - `final`: 是否最终结果

### HTTP接口
- **传输规则**: `GET /api/transmission-rules`
- **健康检查**: `GET /health` (端口8081)
- **系统指标**: `GET /metrics`

## 性能优化

### 1. 模型优化
- **ONNX推理**: 使用ONNX Runtime提高推理速度
- **量化支持**: 支持INT8量化模型
- **FP16支持**: 支持半精度浮点运算
- **设备选择**: 支持CPU/GPU/NPU推理

### 2. 并发优化
- **会话池**: 预创建ONNX会话，避免重复加载
- **异步处理**: 全异步架构，提高并发能力
- **连接复用**: 支持长连接，减少连接开销
- **内存管理**: 智能缓存清理，防止内存泄漏

### 3. 网络优化
- **数据压缩**: Base64编码传输
- **心跳机制**: 30秒心跳检测连接状态
- **超时控制**: 多级超时控制，及时释放资源

## 错误处理

### 错误码分类
- **4000-4999**: 客户端错误
- **5000-5999**: 服务器错误  
- **6000-6999**: 系统错误

### 常见错误
- **4002**: 音频格式错误
- **4003**: 采样率不支持
- **4007**: 数据包超时
- **5001**: 解码错误
- **5002**: 模型加载错误

## 部署特性

### 容器化支持
- 支持Docker部署
- 环境变量配置
- 健康检查集成

### 监控集成
- Prometheus指标导出
- 日志结构化输出
- 系统资源监控

### 扩展性
- 水平扩展支持
- 负载均衡兼容
- 配置热更新

## 技术亮点

1. **统一架构**: 单语种和多语种使用统一架构
2. **智能会话池**: 动态扩容的ONNX会话池
3. **实时语种切换**: 支持对话中语种动态切换
4. **高并发设计**: 支持10+并发连接
5. **完善监控**: 全方位系统监控和健康检查
6. **模块化设计**: 高内聚低耦合的模块设计
7. **配置驱动**: 灵活的配置管理系统

## 项目规模

- **代码行数**: 约5000+行Python代码
- **模块数量**: 15个核心模块
- **配置文件**: 7个配置文件
- **支持语种**: 6种语言
- **API接口**: 3个HTTP接口 + 1个WebSocket接口

## 开发团队建议

### 维护重点
1. **模型更新**: 定期更新ONNX模型
2. **性能调优**: 监控系统性能指标
3. **错误处理**: 完善错误处理机制
4. **文档更新**: 保持文档与代码同步

### 扩展方向
1. **更多语种**: 增加更多语种支持
2. **模型优化**: 集成更先进的模型
3. **功能增强**: 添加标点符号、情感识别等
4. **部署优化**: 优化容器化部署方案
