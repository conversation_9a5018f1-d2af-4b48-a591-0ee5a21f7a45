2025-07-11 10:00:55.713 | INFO  | modules.config:load_global_config:260 - 成功加载全局配置: /ws/ministream_v2_refactor/modules/../conf/config_debug.yaml
2025-07-11 10:00:55.713 | INFO  | modules.config:load_language_config:405 - 加载语种配置: /ws/ministream_v2_refactor/modules/../conf/zh.yaml
2025-07-11 10:00:55.774 | INFO  | modules.config:load_global_config:260 - 成功加载全局配置: /ws/ministream_v2_refactor/modules/../conf/config_debug.yaml
2025-07-11 10:00:55.780 | INFO  | modules.config:init_logger :593 - 日志系统初始化成功, 配置: {'level': 'DEBUG', 'rotation': '1 day', 'retention': '7 days', 'compression': 'zip', 'max_file_size': '100 MB'}
2025-07-11 10:00:55.794 | INFO  | modules.monitoring:_start_health_server:308 - 健康检查服务器已启动, 端口: 8081
2025-07-11 10:00:55.795 | INFO  | modules.monitoring:__init__    :176 - 系统监控已启用, 内存检查间隔: 60秒
2025-07-11 10:00:55.796 | INFO  | modules.asr_manager:__init__    :39 - 初始化统一ASR管理器, 支持语种: ['zh', 'en', 'ru', 'kk', 'kkin', 'ug']
2025-07-11 10:00:55.797 | INFO  | modules.asr_manager:load_models :59 - 单语种模式：加载语种 zh
2025-07-11 10:00:57.305 | INFO  | modules.asr_manager:load_models :72 - 模型加载完成：成功 1/1 个语种
2025-07-11 10:00:57.305 | INFO  | server :lifespan    :109 - 单语种模式 LID功能不可用
2025-07-11 10:00:57.305 | INFO  | modules.connect:__init__    :87 - 启用会话池动态扩容, 最大会话数: 4
2025-07-11 10:00:57.306 | INFO  | server :lifespan    :114 - Server start, init manager, LID_MANAGER, ASR_MANAGER


2025-07-11 10:01:02.034 | INFO  | server :websocket_endpoint:237 - client_id:000 - >>> [请求] 新客户连接，当前活跃连接数: 1
2025-07-11 10:01:02.036 | INFO  | server :websocket_endpoint:237 - client_id:111 - >>> [请求] 新客户连接，当前活跃连接数: 2
2025-07-11 10:01:02.446 | INFO  | modules.connect:on_check    :468 - client_id:000 - 设置自定义分隔符: "，"
2025-07-11 10:01:02.447 | INFO  | modules.connect:_init_decoder:141 - client_id:000 - 初始化解码器, 使用默认语种: zh
2025-07-11 10:01:02.481 | INFO  | modules.connect:_init_decoder:148 - client_id:000 - 解码器初始化完成, 使用分隔符: "，"
2025-07-11 10:01:02.500 | INFO  | modules.connect:on_check    :468 - client_id:111 - 设置自定义分隔符: "，"
2025-07-11 10:01:02.500 | INFO  | modules.connect:_init_decoder:141 - client_id:111 - 初始化解码器, 使用默认语种: zh
2025-07-11 10:01:02.502 | INFO  | modules.connect:_init_decoder:148 - client_id:111 - 解码器初始化完成, 使用分隔符: "，"
2025-07-11 10:01:10.291 | INFO  | modules.connect:on_decode   :980 - client_id:111 - *** 最后一个数据包完成解码 ***
2025-07-11 10:01:10.292 | INFO  | modules.connect:on_result   :430 - client_id:111 - <<< [响应] 最终识别结果: 我想在理性，哲学，以及心理学的层面上理解，美得体验究竟是什么
2025-07-11 10:01:10.296 | INFO  | server :receive     :329 - client_id: 111 - 关闭客户连接，当前活跃连接数: 1
2025-07-11 10:01:15.190 | INFO  | modules.connect:on_decode   :980 - client_id:000 - *** 最后一个数据包完成解码 ***
2025-07-11 10:01:15.191 | INFO  | modules.connect:on_result   :430 - client_id:000 - <<< [响应] 最终识别结果: 很高兴来到这里，与大家探讨我这为关心的一个主题，那就是美，我研究艺术与审美哲学，事实上，这是我的工作
2025-07-11 10:01:15.194 | INFO  | server :receive     :329 - client_id: 000 - 关闭客户连接，当前活跃连接数: 0
2025-07-11 10:04:28.133 | INFO  | server :lifespan    :120 - 正在关闭ASR服务...
2025-07-11 10:04:33.134 | INFO  | server :lifespan    :127 - 系统监控已关闭
2025-07-11 10:04:38.233 | INFO  | server :lifespan    :134 - ONNX会话池已关闭
2025-07-11 10:04:38.237 | INFO  | server :lifespan    :144 - 实时转写已关闭
2025-07-11 10:04:38.237 | INFO  | server :lifespan    :148 - Server shutdown, delete all global resources
