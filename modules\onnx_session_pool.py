#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ONNX会话池管理器
解决多个WebSocket连接并发访问ONNX模型时的性能问题
"""

import queue
import threading
import time
from dataclasses import dataclass
from typing import Any, Dict, List, Optional
import uuid
import onnxruntime as ort

from modules.error_codes import ErrorCode
from modules.logger import logger


@dataclass
class SessionInfo:
    """会话信息"""
    uid: str
    session: ort.InferenceSession
    input_names: List[str]
    metadata: Dict
    created_time: float
    last_used_time: float
    in_use: bool = False
    use_count: int = 0


class ONNXSessionPool:
    """ONNX会话池管理器"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化会话池
        Args:
            config: 会话池配置
        """
        self.max_sessions_per_model = config.get('max_sessions_per_model', 4)
        self.session_timeout = config.get('session_timeout', 300)
        # 是否保留最少会话数（为了内存友好，默认允许清理所有过期会话）
        self.min_sessions_to_keep = config.get('min_sessions_to_keep', 0)
        
        # 会话池存储 {model_name: [SessionInfo, ...]}
        self.session_pools: Dict[str, List[SessionInfo]] = {}
        # 等待队列 {model_name: queue.Queue}
        self.waiting_queues: Dict[str, queue.Queue] = {}
        # 线程锁
        self.locks: Dict[str, threading.RLock] = {}
        # 全局锁，保护字典操作的线程安全
        self._global_pool_creation_lock = threading.Lock()

        self.session_factories: Dict[str, Any] = {}
        
        # 清理线程
        self.cleanup_thread = None
        self.cleanup_interval = 60  # 清理间隔（秒）
        self.running = True
        
        self._start_cleanup_thread()
        
    
    def _start_cleanup_thread(self):
        """启动清理线程"""
        self.cleanup_thread = threading.Thread(target=self._cleanup_expired_sessions, daemon=True)
        self.cleanup_thread.start()
    
    def _cleanup_expired_sessions(self):
        """清理过期会话"""
        while self.running:
            try:
                current_time = time.time()

                # 在全局锁保护下获取模型列表和对应的锁
                with self._global_pool_creation_lock:
                    model_names_and_locks = [(name, self.locks.get(name)) for name in list(self.session_pools.keys())]

                # 对每个模型进行清理
                for model_name, model_lock in model_names_and_locks:
                    if model_lock is None:
                        continue  # 跳过没有锁的模型（理论上不应该发生）

                    with model_lock:
                        sessions = self.session_pools.get(model_name, [])
                        if not sessions:
                            continue

                        expired_sessions_indices = []   # 存储索引

                        # 获取过期会话
                        for i, session_info in enumerate(sessions):
                            if (not session_info.in_use and
                                current_time - session_info.last_used_time > self.session_timeout):
                                expired_sessions_indices.append(i)

                        # 根据配置决定是否保留最少会话数
                        if expired_sessions_indices:
                            # 计算可以清理的会话数量（保留最少会话数）
                            sessions_after_cleanup = len(sessions) - len(expired_sessions_indices)
                            if sessions_after_cleanup < self.min_sessions_to_keep:
                                # 只清理部分过期会话，保留最少数量
                                keep_count = self.min_sessions_to_keep - sessions_after_cleanup
                                expired_sessions_indices = expired_sessions_indices[:-keep_count] if keep_count > 0 else expired_sessions_indices

                            # 清理过期会话
                            for i in reversed(expired_sessions_indices):
                                expired_session = sessions.pop(i)
                                logger.debug(f"清理过期ONNX会话: {model_name} - {expired_session.uid}, 使用次数: {expired_session.use_count}")

                            # 如果某个模型的所有会话都被清理了，记录日志
                            if not sessions:
                                logger.info(f"模型 {model_name} 的所有会话已清理，下次使用时将重新加载")
                            elif len(expired_sessions_indices) > 0:
                                logger.debug(f"模型 {model_name} 清理了 {len(expired_sessions_indices)} 个过期会话，剩余 {len(sessions)} 个")

                time.sleep(self.cleanup_interval)
            except Exception as e:
                logger.error(f"清理过期会话时出错: {e}")
                time.sleep(self.cleanup_interval)
    
    def register_model(self, model_name: str, session_factory_func, *args, **kwargs):
        """
        注册模型到会话池
        Args:
            model_name: 模型名称
            session_factory_func: 创建会话的工厂函数
            *args, **kwargs: 传递给工厂函数的参数
        """

        with self._global_pool_creation_lock:
            if model_name not in self.session_pools:
                # 在全局锁保护下初始化所有相关字典
                self.session_pools[model_name] = []
                self.waiting_queues[model_name] = queue.Queue()
                self.locks[model_name] = threading.RLock()
                self.session_factories[model_name] = (session_factory_func, args, kwargs)

                # 预创建一个会话
                try:
                    session, input_names, metadata = session_factory_func(*args, **kwargs)
                    session_info = SessionInfo(
                        uid=str(uuid.uuid4())[:8],
                        session=session,
                        input_names=input_names,
                        metadata=metadata,
                        created_time=time.time(),
                        last_used_time=time.time()
                    )
                    self.session_pools[model_name].append(session_info)   # 添加一个session
                    logger.debug(f"模型 {model_name} 注册成功，预创建会话: {session_info.uid}")
                except Exception as e:
                    logger.error(f"为模型 {model_name} 预创建会话失败: {e}")
            else:
                logger.debug(f"模型 {model_name} 已经注册，跳过重复注册")
    
    def get_session(self, model_name: str, timeout: float = 5.0, client_id = None) -> Optional[SessionInfo]:
        """
        获取会话
        Args:
            model_name: 模型名称
            timeout: 超时时间（秒）
        Returns:
            SessionInfo或None
        """
        if model_name not in self.session_pools:
            logger.warning(f"模型 {model_name} 未注册到会话池")
            return None

        # 安全检查：确保对应的锁存在
        if model_name not in self.locks:
            logger.error(f"模型 {model_name} 的锁不存在，这是一个严重的内部错误")
            return None

        with self.locks[model_name]:
            sessions = self.session_pools[model_name]
            in_use_count = sum(1 for s in sessions if s.in_use)

            # 原始扩容逻辑：没有可用会话时才扩容

            # 1. 查找可用会话
            for session_info in sessions:
                if not session_info.in_use:
                    session_info.in_use = True
                    session_info.last_used_time = time.time()
                    session_info.use_count += 1
                    logger.debug(f"客户 {client_id} 获取现有会话: {model_name} - {session_info.uid}, 池大小: {len(self.session_pools[model_name])}")
                    return session_info

            # 2. 没有可用会话，立即创建新会话（如果未达到最大数量）
            if len(sessions) < self.max_sessions_per_model:
                try:
                    logger.debug(f"客户 {client_id} 模型 {model_name} 无可用会话，立即创建新会话")
                    factory_func, args, kwargs = self.session_factories[model_name]
                    session, input_names, metadata = factory_func(*args, **kwargs)
                    session_info = SessionInfo(
                        uid=str(uuid.uuid4())[:8],
                        session=session,
                        input_names=input_names,
                        metadata=metadata,
                        created_time=time.time(),
                        last_used_time=time.time()
                    )
                    self.session_pools[model_name].append(session_info)
                    session_info.in_use = True   # 新创建的会话直接设为使用中
                    session_info.use_count += 1
                    logger.debug(f"客户 {client_id} 为模型 {model_name} 立即创建新会话 {session_info.uid}, 池大小: {len(self.session_pools[model_name])}")
                    return session_info
                except Exception as e:
                    logger.error(f"客户 {client_id} 为模型 {model_name} 立即创建会话失败: {e}")
                    return None

            # 3. 池已满，记录当前状态
            logger.debug(f"客户 {client_id} 模型 {model_name} 会话池已满: 总数={len(sessions)}, 使用中={in_use_count}, 最大={self.max_sessions_per_model}")

        # 4. 如果无法立即获取会话，使用等待队列避免忙等
        try:
            logger.debug(f"客户 {client_id} 可用会话，等待其他线程释放 {model_name} ...")
            session_info = self.waiting_queues[model_name].get(timeout=timeout)
            with self.locks[model_name]:
                # 再次加锁确保线程安全
                session_info.in_use = True
                session_info.last_used_time = time.time()
                session_info.use_count += 1
                logger.debug(f"客户 {client_id} 从等待队列获取会话: {model_name} - {session_info.uid}")
                return session_info
        except queue.Empty:
            logger.warning(f"客户 {client_id} 等待模型 {model_name} 会话超时 ({timeout}s)")
            pass

        # 保底机制：如果超时仍未获取到会话，创建紧急会话
        logger.warning(f"客户 {client_id} 获取模型 {model_name} 的会话超时，创建紧急会话")
        return self._create_emergency_session(model_name)
    
    def release_session(self, model_name: str, session_info: SessionInfo):
        """
        释放会话
        Args:
            model_name: 模型名称
            session_info: 会话信息
        """
        # 检查是否是紧急会话（不在池中的会话）
        if model_name not in self.session_pools:
            logger.debug(f"释放紧急会话: {model_name} - {session_info.uid}")
            # 紧急会话直接丢弃，不加入池中
            return

        with self.locks[model_name]:
            # 检查会话是否在池中
            if session_info not in self.session_pools[model_name]:
                logger.debug(f"释放紧急会话（不在池中）: {model_name} - {session_info.uid}")
                # 紧急会话直接丢弃
                return

            # 标记会话为可用
            session_info.in_use = False
            session_info.last_used_time = time.time()

            # 获取当前池状态用于日志
            sessions = self.session_pools[model_name]
            available_count = sum(1 for s in sessions if not s.in_use)
            logger.debug(f"释放会话: {model_name} - {session_info.uid}, 当前可用: {available_count}/{len(sessions)}")

            # 通知等待的线程
            try:
                self.waiting_queues[model_name].put_nowait(session_info)
                # logger.debug(f"会话 {session_info.uid} 已加入等待队列")
            except queue.Full:
                logger.debug(f"等待队列已满，会话 {session_info.uid} 直接可用")
                pass  # 队列满了就忽略，会话仍然可用

    def _create_emergency_session(self, model_name: str) -> Optional[SessionInfo]:
        """
        创建紧急会话（保底机制）

        Args:
            model_name: 模型名称

        Returns:
            SessionInfo或None
        """
        try:
            if model_name not in self.session_factories:
                logger.error(f"模型 {model_name} 未注册，无法创建紧急会话")
                return None

            factory_func, args, kwargs = self.session_factories[model_name]
            session, input_names, metadata = factory_func(*args, **kwargs)

            session_info = SessionInfo(
                uid=str(uuid.uuid4())[:8],
                session=session,
                input_names=input_names,
                metadata=metadata,
                created_time=time.time(),
                last_used_time=time.time(),
                in_use=True,
                use_count=1
            )

            logger.warning(f"为模型 {model_name} 创建紧急会话")
            return session_info

        except Exception as e:
            logger.error(f"创建紧急会话失败: {e}")
            return None
    
    def get_pool_stats(self) -> Dict[str, Dict[str, Any]]:
        """获取会话池统计信息"""
        stats = {}
        current_time = time.time()

        # 在全局锁保护下获取模型列表
        with self._global_pool_creation_lock:
            model_names = list(self.session_pools.keys())

        for model_name in model_names:
            model_lock = self.locks.get(model_name)
            if model_lock is None:
                continue

            with model_lock:
                sessions = self.session_pools.get(model_name, [])
                if not sessions:
                    continue

                in_use_count = sum(1 for s in sessions if s.in_use)
                total_use_count = sum(s.use_count for s in sessions)
                avg_age = sum(current_time - s.created_time for s in sessions) / len(sessions)
                oldest_session_age = max(current_time - s.created_time for s in sessions) if sessions else 0
                newest_session_age = min(current_time - s.created_time for s in sessions) if sessions else 0

                stats[model_name] = {
                    'total_sessions': len(sessions),
                    'in_use_sessions': in_use_count,
                    'available_sessions': len(sessions) - in_use_count,
                    'total_use_count': total_use_count,
                    'waiting_queue_size': self.waiting_queues[model_name].qsize(),
                    'avg_session_age_seconds': round(avg_age, 2),
                    'oldest_session_age_seconds': round(oldest_session_age, 2),
                    'newest_session_age_seconds': round(newest_session_age, 2),
                    'max_sessions_allowed': self.max_sessions_per_model,
                    'utilization_rate': round(in_use_count / len(sessions) * 100, 1) if sessions else 0
                }

        return stats
    
    def shutdown(self):
        """关闭会话池"""
        self.running = False
        if self.cleanup_thread:
            self.cleanup_thread.join(timeout=5)

        # 在全局锁保护下清理所有会话
        with self._global_pool_creation_lock:
            # 获取所有模型名称和对应的锁
            model_names_and_locks = [(name, self.locks.get(name)) for name in list(self.session_pools.keys())]

            # 清理每个模型的会话
            for model_name, model_lock in model_names_and_locks:
                if model_lock is None:
                    continue

                with model_lock:
                    sessions = self.session_pools.get(model_name, [])
                    for session_info in sessions:
                        try:
                            # ONNX会话会自动清理, 这里只是记录
                            logger.debug(f"关闭模型 {model_name} 的会话 {session_info.uid}, 使用次数: {session_info.use_count}")
                        except Exception as e:
                            logger.error(f"关闭会话时出错: {e}")

            # 清理所有字典
            self.session_pools.clear()
            self.waiting_queues.clear()
            self.locks.clear()
            self.session_factories.clear()


# 全局会话池实例
_global_session_pool: Optional[ONNXSessionPool] = None


def get_global_session_pool() -> Optional[ONNXSessionPool]:
    """获取全局会话池实例"""
    return _global_session_pool


def initialize_global_session_pool(config: Dict[str, Any]):
    """初始化全局会话池"""
    global _global_session_pool
    if _global_session_pool is None:
        _global_session_pool = ONNXSessionPool(config)
    return _global_session_pool


def shutdown_global_session_pool():
    """关闭全局会话池"""
    global _global_session_pool
    if _global_session_pool:
        _global_session_pool.shutdown()
        _global_session_pool = None
