# ASR流式语音识别服务部署手册

## 系统要求

### 硬件要求
| 组件 | 最低配置 | 推荐配置 | 说明 |
|------|----------|----------|------|
| CPU | 4核 2.0GHz | 8核 3.0GHz | 支持AVX2指令集 |
| 内存 | 8GB | 16GB+ | 根据并发数调整 |
| 存储 | 50GB | 100GB+ | SSD推荐 |
| 网络 | 100Mbps | 1Gbps | 稳定网络连接 |

### 软件要求
| 软件 | 版本要求 | 说明 |
|------|----------|------|
| 操作系统 | Linux (Ubuntu 18.04+/CentOS 7+) | 64位系统 |
| Python | 3.8+ | 推荐3.9或3.10 |
| pip | 最新版本 | Python包管理器 |

### 可选组件
- **GPU支持**: NVIDIA GPU + CUDA 11.0+ (用于GPU推理)
- **Docker**: 20.10+ (容器化部署)
- **Nginx**: 1.18+ (反向代理)

## 快速部署

### 1. 环境准备

#### 创建虚拟环境
```bash
# 创建Python虚拟环境
python3 -m venv asr_env
source asr_env/bin/activate

# 升级pip
pip install --upgrade pip
```

#### 安装系统依赖
```bash
# Ubuntu/Debian
sudo apt-get update
sudo apt-get install -y build-essential python3-dev libsndfile1

# CentOS/RHEL
sudo yum groupinstall -y "Development Tools"
sudo yum install -y python3-devel libsndfile
```

### 2. 安装服务

#### 下载源码
```bash
# 解压服务包
tar -xzf asr-streaming-service.tar.gz
cd asr-streaming-service

# 或从Git仓库克隆
git clone <repository-url>
cd asr-streaming-service
```

#### 安装Python依赖
```bash
# 安装依赖包
pip install -r requirements.txt

# 验证安装
python -c "import torch, onnxruntime, fastapi; print('依赖安装成功')"
```

### 3. 模型部署

#### 模型文件结构
```
/ws/MODELS/
├── online_onnx_zh/          # 中文模型
│   ├── encoder.onnx
│   ├── ctc.onnx
│   ├── train.yaml
│   └── units.txt
├── online_onnx_en/          # 英文模型
├── online_onnx_ru/          # 俄语模型
├── online_onnx_kk/          # 哈萨克语模型
├── online_onnx_kkin/        # 柯尔克孜语模型
├── online_onnx_ug/          # 维吾尔语模型
└── lid_model/               # 语种识别模型
    ├── lid.onnx
    ├── config.yaml
    └── spk2id.json
```

#### 模型配置
```bash
# 创建模型目录
sudo mkdir -p /ws/MODELS

# 复制模型文件到指定位置
sudo cp -r models/* /ws/MODELS/

# 设置权限
sudo chown -R $USER:$USER /ws/MODELS
```

### 4. 配置文件

#### 全局配置 (conf/config.yaml)
```yaml
# 服务器配置
server:
  host: "0.0.0.0"           # 监听地址
  port: 10080               # 服务端口
  heartbeat_interval: 30    # 心跳间隔(秒)
  max_connections: 10       # 最大连接数

# 音频处理配置
audio:
  expected_sample_rate: 16000     # 期望采样率
  expected_sample_width: 2        # 采样位宽
  expected_sample_channels: 1     # 声道数
  max_packet_interval: 6000       # 包超时时间(毫秒)

# 监控配置
monitoring:
  enable_health_check: true       # 启用健康检查
  health_check_port: 8081        # 健康检查端口
  memory_warning_threshold: 80    # 内存警告阈值(%)

# 日志配置
logging:
  level: "INFO"                  # 日志级别
  rotation: "1 day"              # 日志轮转
  retention: "7 days"            # 日志保留期

# 支持的语种
supported_languages:
  - zh    # 中文
  - en    # 英文
  - ru    # 俄语
  - kk    # 哈萨克语
  - kkin  # 柯尔克孜语
  - ug    # 维吾尔语
```

#### 语种配置示例 (conf/zh.yaml)
```yaml
# 中文模型配置
onnx_dir: /ws/MODELS/online_onnx_zh    # 模型路径
fp16: false                            # 是否使用FP16
quant: false                           # 是否使用量化
device: cpu                            # 设备类型: cpu/gpu/npu
device_id: 0                           # 设备ID

# 热词配置
context_list_path: "/ws/MODELS/online_onnx_zh/hotwords.txt"
context_graph_score: 40

# 断句配置
separator_interval: 0.5                # 断句间隔(秒)
default_separator: "，"                # 默认分隔符

# 后处理
lower: true                            # 转小写
remove_spm: false                      # 移除SentencePiece标记
```

### 5. 启动服务

#### 直接启动
```bash
# 前台启动(调试模式)
python server.py --lang_code zh --debug

# 后台启动
nohup python server.py --lang_code zh > server.log 2>&1 &
```

#### 使用systemd管理
创建服务文件 `/etc/systemd/system/asr-service.service`:

```ini
[Unit]
Description=ASR Streaming Service
After=network.target

[Service]
Type=simple
User=asr
Group=asr
WorkingDirectory=/opt/asr-streaming-service
Environment=PATH=/opt/asr-streaming-service/asr_env/bin
ExecStart=/opt/asr-streaming-service/asr_env/bin/python server.py --lang_code multi
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

启动服务:
```bash
# 重载systemd配置
sudo systemctl daemon-reload

# 启动服务
sudo systemctl start asr-service

# 设置开机自启
sudo systemctl enable asr-service

# 查看状态
sudo systemctl status asr-service
```

## 配置参数详解

### 服务器配置 (server)
| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| host | string | "0.0.0.0" | 监听地址，0.0.0.0表示所有接口 |
| port | int | 10080 | WebSocket服务端口 |
| heartbeat_interval | int | 30 | 心跳检测间隔(秒) |
| max_connections | int | 10 | 最大并发连接数 |
| connection_timeout | int | 300 | 连接超时时间(秒) |

### 音频配置 (audio)
| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| valid_sample_rate_list | list | [44100,16000,8000] | 支持的采样率列表 |
| expected_sample_rate | int | 16000 | 期望采样率(Hz) |
| expected_sample_width | int | 2 | 采样位宽(字节) |
| expected_sample_channels | int | 1 | 声道数 |
| expected_data_size | int | 12800 | 音频包大小(字节) |
| max_packet_interval | int | 6000 | 包接收超时(毫秒) |
| max_feat_cache | int | 1000 | 最大特征缓存(帧) |
| cache_clean_packet_interval | int | 30 | 缓存清理间隔(包数) |

### LID配置 (lid)
| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| enabled | bool | true | 是否启用语种识别 |
| model_path | string | - | LID模型路径 |
| model_config | string | - | LID配置文件路径 |
| dict_path | string | - | 语种词典路径 |
| device | string | "cpu" | 推理设备 |
| max_attempts | int | 6 | 最大检测尝试次数 |
| confidence_threshold | float | 0.6 | 置信度阈值 |
| silence_threshold | float | 1.0 | 静音重置阈值(秒) |

### 会话池配置 (onnx_session_pool)
| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| max_sessions_per_model | int | 4 | 每个模型最大会话数 |
| session_timeout | int | 300 | 会话超时时间(秒) |

### 监控配置 (monitoring)
| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| enable_health_check | bool | true | 启用健康检查 |
| health_check_port | int | 8081 | 健康检查端口 |
| memory_check_interval | int | 60 | 内存检查间隔(秒) |
| memory_warning_threshold | int | 80 | 内存警告阈值(%) |
| memory_critical_threshold | int | 90 | 内存严重阈值(%) |

### 日志配置 (logging)
| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| level | string | "INFO" | 日志级别(DEBUG/INFO/WARNING/ERROR) |
| rotation | string | "1 day" | 日志轮转策略 |
| retention | string | "7 days" | 日志保留时间 |
| compression | string | "zip" | 日志压缩格式 |
| max_file_size | string | "100 MB" | 单个日志文件最大大小 |

## 端口配置

### 默认端口
| 服务 | 端口 | 协议 | 说明 |
|------|------|------|------|
| WebSocket服务 | 10080 | TCP | 主要服务端口 |
| 健康检查 | 8081 | HTTP | 监控端口 |

### 端口修改
修改 `conf/config.yaml` 中的端口配置:

```yaml
server:
  port: 8080              # 修改主服务端口

monitoring:
  health_check_port: 8082 # 修改健康检查端口
```

### 防火墙配置
```bash
# Ubuntu/Debian (ufw)
sudo ufw allow 10080/tcp
sudo ufw allow 8081/tcp

# CentOS/RHEL (firewalld)
sudo firewall-cmd --permanent --add-port=10080/tcp
sudo firewall-cmd --permanent --add-port=8081/tcp
sudo firewall-cmd --reload

## 健康检查

### 健康检查接口
服务提供多个健康检查接口用于监控服务状态：

#### 1. 基础健康检查
```bash
curl -X GET "http://localhost:8081/health"
```

**响应示例:**
```json
{
    "status": "healthy",
    "timestamp": "2024-01-01T12:00:00Z",
    "memory_usage": 45.2,
    "cpu_usage": 12.8,
    "disk_usage": 78.5,
    "active_connections": 3,
    "uptime_seconds": 86400,
    "errors": []
}
```

#### 2. 详细系统指标
```bash
curl -X GET "http://localhost:8081/metrics"
```

**响应示例:**
```json
{
    "system": {
        "cpu_percent": 12.8,
        "memory_percent": 45.2,
        "disk_usage_percent": 78.5,
        "load_average": [0.5, 0.8, 1.2]
    },
    "service": {
        "total_requests": 1250,
        "error_count": 5,
        "active_connections": 3,
        "uptime_seconds": 86400
    },
    "models": {
        "loaded_languages": ["zh", "en", "ru"],
        "session_pool_stats": {
            "zh_encoder": {"total": 2, "in_use": 1},
            "zh_ctc": {"total": 2, "in_use": 1}
        }
    }
}
```

#### 3. 传输规则查询
```bash
curl -X GET "http://localhost:10080/api/transmission-rules"
```

### 健康检查脚本
创建监控脚本 `health_check.sh`:

```bash
#!/bin/bash

# 健康检查脚本
HEALTH_URL="http://localhost:8081/health"
SERVICE_URL="ws://localhost:10080/ws/health_check"

# 检查HTTP健康接口
echo "检查健康状态..."
HEALTH_STATUS=$(curl -s -o /dev/null -w "%{http_code}" $HEALTH_URL)

if [ $HEALTH_STATUS -eq 200 ]; then
    echo "✅ 健康检查通过"
else
    echo "❌ 健康检查失败 (HTTP $HEALTH_STATUS)"
    exit 1
fi

# 检查内存使用率
MEMORY_USAGE=$(curl -s $HEALTH_URL | jq -r '.memory_usage')
if (( $(echo "$MEMORY_USAGE > 90" | bc -l) )); then
    echo "⚠️  内存使用率过高: ${MEMORY_USAGE}%"
fi

echo "服务运行正常"
```

## 性能调优

### 1. 系统级优化

#### 内核参数调优
编辑 `/etc/sysctl.conf`:

```bash
# 网络优化
net.core.rmem_max = 16777216
net.core.wmem_max = 16777216
net.ipv4.tcp_rmem = 4096 87380 16777216
net.ipv4.tcp_wmem = 4096 65536 16777216

# 文件描述符限制
fs.file-max = 65536

# 应用更改
sudo sysctl -p
```

#### 用户限制调优
编辑 `/etc/security/limits.conf`:

```bash
# 增加文件描述符限制
asr soft nofile 65536
asr hard nofile 65536

# 增加进程数限制
asr soft nproc 32768
asr hard nproc 32768
```

### 2. 应用级优化

#### 并发配置
根据硬件配置调整并发参数:

```yaml
# 高配置服务器
server:
  max_connections: 50

onnx_session_pool:
  max_sessions_per_model: 8

# 低配置服务器
server:
  max_connections: 5

onnx_session_pool:
  max_sessions_per_model: 2
```

#### 内存优化
```yaml
audio:
  max_feat_cache: 500              # 减少特征缓存
  cache_clean_packet_interval: 20  # 更频繁清理缓存

logging:
  level: "WARNING"                 # 减少日志输出
  max_file_size: "50 MB"          # 限制日志文件大小
```

### 3. GPU加速配置

#### 安装GPU版本依赖
```bash
# 卸载CPU版本
pip uninstall onnxruntime

# 安装GPU版本
pip install onnxruntime-gpu

# 验证GPU支持
python -c "import onnxruntime as ort; print(ort.get_available_providers())"
```

#### GPU配置
```yaml
# 语种配置文件中启用GPU
device: gpu
device_id: 0    # GPU设备ID
```

## 容器化部署

### Docker部署

#### 创建Dockerfile
```dockerfile
FROM python:3.9-slim

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    build-essential \
    libsndfile1 \
    && rm -rf /var/lib/apt/lists/*

# 设置工作目录
WORKDIR /app

# 复制依赖文件
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY . .

# 创建日志目录
RUN mkdir -p /app/logs

# 暴露端口
EXPOSE 10080 8081

# 启动命令
CMD ["python", "server.py", "--lang_code", "multi"]
```

#### 构建和运行
```bash
# 构建镜像
docker build -t asr-service:latest .

# 运行容器
docker run -d \
  --name asr-service \
  -p 10080:10080 \
  -p 8081:8081 \
  -v /ws/MODELS:/ws/MODELS:ro \
  -v ./logs:/app/logs \
  --restart unless-stopped \
  asr-service:latest
```

#### Docker Compose部署
创建 `docker-compose.yml`:

```yaml
version: '3.8'

services:
  asr-service:
    build: .
    ports:
      - "10080:10080"
      - "8081:8081"
    volumes:
      - /ws/MODELS:/ws/MODELS:ro
      - ./logs:/app/logs
      - ./conf:/app/conf
    environment:
      - PYTHONUNBUFFERED=1
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8081/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - asr-service
    restart: unless-stopped
```

启动服务:
```bash
docker-compose up -d
```

## 负载均衡和高可用

### Nginx反向代理配置

#### HTTP负载均衡
创建 `nginx.conf`:

```nginx
upstream asr_backend {
    server 127.0.0.1:10080;
    server 127.0.0.1:10081;
    server 127.0.0.1:10082;
}

server {
    listen 80;
    server_name asr.example.com;

    location /ws/ {
        proxy_pass http://asr_backend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # WebSocket超时设置
        proxy_read_timeout 3600s;
        proxy_send_timeout 3600s;
    }

    location /api/ {
        proxy_pass http://asr_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }
}
```

#### HTTPS配置
```nginx
server {
    listen 443 ssl http2;
    server_name asr.example.com;

    ssl_certificate /etc/nginx/ssl/cert.pem;
    ssl_certificate_key /etc/nginx/ssl/key.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384;

    location /ws/ {
        proxy_pass http://asr_backend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        # ... 其他配置
    }
}
```

### 多实例部署
启动多个服务实例:

```bash
# 实例1 - 端口10080
python server.py --lang_code multi --port 10080

# 实例2 - 端口10081
python server.py --lang_code multi --port 10081

# 实例3 - 端口10082
python server.py --lang_code multi --port 10082
```

## 监控和日志

### 日志管理

#### 日志文件位置
```
logs/
├── log_multi/                    # 多语种模式日志
│   ├── server-INFO.2024-01-01.log
│   └── server-DEBUG.2024-01-01.log
└── log_zh/                      # 单语种模式日志
    ├── server-INFO.2024-01-01.log
    └── server-DEBUG.2024-01-01.log
```

#### 日志轮转配置
使用logrotate管理日志:

创建 `/etc/logrotate.d/asr-service`:

```bash
/opt/asr-streaming-service/logs/*/*.log {
    daily
    rotate 30
    compress
    delaycompress
    missingok
    notifempty
    create 644 asr asr
    postrotate
        systemctl reload asr-service
    endscript
}
```

#### 日志分析
常用日志分析命令:

```bash
# 查看错误日志
grep "ERROR" logs/log_multi/server-INFO.*.log

# 统计连接数
grep "新客户连接" logs/log_multi/server-INFO.*.log | wc -l

# 查看性能指标
grep "处理时间" logs/log_multi/server-DEBUG.*.log

# 实时监控日志
tail -f logs/log_multi/server-INFO.*.log
```

### 监控集成

#### Prometheus监控
添加Prometheus指标导出:

```python
# 在server.py中添加
from prometheus_client import Counter, Histogram, Gauge, start_http_server

# 定义指标
REQUEST_COUNT = Counter('asr_requests_total', 'Total requests')
REQUEST_DURATION = Histogram('asr_request_duration_seconds', 'Request duration')
ACTIVE_CONNECTIONS = Gauge('asr_active_connections', 'Active connections')

# 启动指标服务器
start_http_server(9090)
```

#### Grafana仪表板
创建监控仪表板监控以下指标:
- 请求QPS
- 响应时间
- 错误率
- 内存使用率
- CPU使用率
- 活跃连接数

## 故障排除

### 常见问题

#### 1. 服务启动失败
**问题**: 服务无法启动
**排查步骤**:
```bash
# 检查端口占用
netstat -tlnp | grep 10080

# 检查配置文件
python -c "import yaml; yaml.safe_load(open('conf/config.yaml'))"

# 检查模型文件
ls -la /ws/MODELS/

# 查看详细错误
python server.py --lang_code zh --debug
```

#### 2. 连接被拒绝
**问题**: 客户端无法连接
**排查步骤**:
```bash
# 检查服务状态
systemctl status asr-service

# 检查防火墙
sudo ufw status
sudo firewall-cmd --list-ports

# 测试连接
telnet localhost 10080
```

#### 3. 识别结果异常
**问题**: 识别准确率低或无结果
**排查步骤**:
```bash
# 检查音频格式
file audio_file.wav

# 检查模型加载
grep "模型加载" logs/log_multi/server-INFO.*.log

# 检查LID日志
grep "语种识别" logs/log_multi/server-DEBUG.*.log
```

#### 4. 内存泄漏
**问题**: 内存使用持续增长
**排查步骤**:
```bash
# 监控内存使用
watch -n 5 'ps aux | grep python'

# 检查缓存清理
grep "缓存清理" logs/log_multi/server-DEBUG.*.log

# 调整缓存参数
# 在config.yaml中减少max_feat_cache值
```

### 性能问题诊断

#### 1. 响应时间过长
```bash
# 检查CPU使用率
top -p $(pgrep -f server.py)

# 检查会话池状态
curl -s http://localhost:8081/metrics | jq '.models.session_pool_stats'

# 启用详细日志
# 在config.yaml中设置 logging.level: "DEBUG"
```

#### 2. 并发性能差
```bash
# 检查连接数限制
ulimit -n

# 调整会话池大小
# 在config.yaml中增加max_sessions_per_model

# 检查系统负载
uptime
iostat 1 5
```

### 日志级别调试

#### 调试模式启动
```bash
# 启用详细日志
python server.py --lang_code multi --debug

# 或修改配置文件
# logging.level: "DEBUG"
```

#### 关键日志信息
- **连接建立**: "新客户连接"
- **音频处理**: "特征提取完成"
- **语种识别**: "LID检测结果"
- **模型推理**: "ONNX推理耗时"
- **结果返回**: "发送识别结果"

## 安全配置

### 1. 网络安全
```bash
# 限制访问IP
iptables -A INPUT -p tcp --dport 10080 -s ***********/24 -j ACCEPT
iptables -A INPUT -p tcp --dport 10080 -j DROP

# 使用SSL/TLS
# 配置Nginx SSL终止
```

### 2. 用户权限
```bash
# 创建专用用户
sudo useradd -r -s /bin/false asr
sudo chown -R asr:asr /opt/asr-streaming-service

# 限制文件权限
chmod 750 /opt/asr-streaming-service
chmod 640 /opt/asr-streaming-service/conf/*.yaml
```

### 3. 资源限制
在systemd服务文件中添加:

```ini
[Service]
# 内存限制
MemoryLimit=2G

# CPU限制
CPUQuota=200%

# 文件描述符限制
LimitNOFILE=65536
```

---

**版本**: v1.0
**更新时间**: 2024-01-01
**维护团队**: ASR开发团队
```
