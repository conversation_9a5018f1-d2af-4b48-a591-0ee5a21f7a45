热词接口协议（Json格式）
2023-12-18

[toc]

创建热词词库
基本信息
Path： /hotwords

Method： POST

接口描述： 传入一个热词列表，创建热词词库，总共可创建 200 个词库。手动提取热词时，每个热词不超过 10 字符，一个词库不超过 10000 个词条。自动提取热词时，文本内容不超过 100 万字符。

请求参数
Body（json）

名称	类型	是否必需	说明	默认值
name	string	否	热词词库的名称	空
lang_type	string	是	语种	必填
sample_rate	int	是	采样率（Hz）	必填
words	list<string>	是	热词列表	必填
auto_extraction	boolean	否	是否对热词列表中的内容自动提取关键词（需选配对应组件）	false
返回参数
名称	类型	说明
status	string	状态码
message	string	状态码描述
data	string	热词ID
请求示例

手动提取热词，列表中每个元素为一个热词词条

curl -X POST '127.0.0.1:7100/hotwords' \
-H 'Content-Type: application/json' \
-d '{
    "name":"人名",
    "lang_type":"zh-cmn-Hans-CN",
    "sample_rate":16000,
    "words":["张三","李四","王五"]
}'
自动提取热词，篇章内容可以为一个元素，也可以为多个元素，需要设置参数 auto_extraction 为 true

curl -X POST '127.0.0.1:7100/hotwords' \
-H 'Content-Type: application/json' \
-d '{
    "name":"术语",
    "lang_type":"zh-cmn-Hans-CN",
    "sample_rate":16000,
    "auto_extraction":true,
    "words":[
        "中国第40次南极考察内陆队北京时间16日在中山站举行出征仪式，29名队员将分别前往泰山站、昆仑站和格罗夫山地区，开展相关科学考察。中国第40次南极考察由自然资源部组织。这是中国首次派出3艘船执行南极考察任务，即雪龙号、雪龙二号和天惠轮。",
        "其中，天惠轮目前正在罗斯海新站卸运建站物资，雪龙二号正在前往新西兰利特尔顿港。本次考察的一个重要方面是开展国际南极科学前沿领域合作研究，并与多国开展后勤保障方面的国际合作。"
    ]
}'
返回示例

{
    "status": "00000",
    "message": "success",
    "data": "HW50D67839" // data为创建的热词ID
}
删除热词词库
基本信息
Path： /hotwords/{hotwords_id}

Method： DELETE

接口描述： 删除指定 ID 的热词词库。只能删除当前没有识别请求调用的词库。

请求示例

curl -X DELETE '127.0.0.1:7100/hotwords/HW50D67839'
返回示例

{
    "status": "00000",
    "message": "success",
    "data": null
}
返回参数
名称	类型	说明
status	string	状态码
message	string	状态码描述
data	object	无
获取热词词库信息列表
基本信息
Path： /hotwords

Method： GET

接口描述： 获取所有热词词库的信息

返回参数
名称	类型	说明
status	string	状态码
message	string	状态码描述
data	object[]	数据
├─ id	string	热词ID
├─ name	string	热词名称
├─ lang_type	string	语种
├─ status	int	状态，-1:训练失败，0:训练中，1:训练完成
├─ sample_rate	int	采样率
├─ update_time	string	最近修改时间
请求示例

curl -X GET '127.0.0.1:7100/hotwords'
返回示例

{
    "status": "00000",
    "message": "success",
    "data": [
        {
            "id": "HW50D67839",
            "name": "人名",
            "lang_type": "zh-cmn-Hans-CN",
            "status": 1,
            "sample_rate": 16000,
            "update_time": "2023-10-09 13:14:15"
        }
    ]
}
获取热词词库信息
基本信息
Path： /hotwords/{hotwords_id}

Method： GET

接口描述： 获取指定 ID 的热词词库的信息

返回参数
名称	类型	说明
status	string	状态码
message	string	状态码描述
data	object	数据
├─ id	string	热词ID
├─ name	string	热词名称
├─ lang_type	string	语种
├─ status	int	状态，-1:训练失败，0:训练中，1:训练完成
├─ sample_rate	int	采样率
├─ update_time	string	最近修改时间
├─ words	list<string>	热词列表
├─ file	string	Base64编码的热词文件（UTF-8 编码的文本格式）
注意：该字段已在V2.6.0版本中弃用，请改用words字段代替
├─ amount	int	包含的词数
请求示例

curl -X GET '127.0.0.1:7100/hotwords/HW50D67839'
返回示例

{
    "status": "00000",
    "message": "success",
    "data": {
            "id": "HW50D67839",
            "name": "人名",
            "lang_type": "zh-cmn-Hans-CN",
            "status": 1,
            "sample_rate": 16000,
            "update_time": "2021-11-29 13:14:15",
            "file": "<Base64编码>",
            "words": ["张三","李四","王五"],
            "amount": 12
        }
}
修改热词词库信息
基本信息
Path： /hotwords/{hotwords_id}

Method： PUT

接口描述： 修改已有的热词词库的名称或热词列表。注意：修改热词列表时，词库内所有热词将被新列表内容覆盖，如需追加热词请使用添加热词接口。

请求参数
Body（json）

名称	类型	是否必需	说明	默认值
name	string	否	热词词库的名称	无
words	list<string>	否	热词列表	无
auto_extraction	boolean	否	是否对热词列表中的内容自动提取关键词（需选配对应组件）	false
返回参数
名称	类型	说明
status	string	状态码
message	string	状态码描述
data	string	热词id
请求示例

curl -X PUT '127.0.0.1:7100/hotwords/HW50D67839' \
-d '{
    "name":"姓名",
    "words":["玄奘","悟空","悟能","悟净"]
}'
以上例子中，既修改了词库的名称，又更新了热词内容。如只需要修改词库名称，则无需传入 words 参数；反之亦然。

返回示例

{
    "status": "00000",
    "message": "success",
    "data": "HW50D67839"
}
添加热词词条
基本信息
Path： /hotwords/{hotwords_id}

Method： POST

接口描述： 向已有的热词词库中添加热词词条。每个词条不超过 10 字符，一个词库不超过 10000 个词条。

请求参数
Body（json）

名称	类型	是否必需	说明	默认值
words	list<string>	是	热词列表	必填
auto_extraction	boolean	否	是否对热词列表中的内容自动提取关键词（需选配对应组件）	false
返回参数
名称	类型	说明
status	string	状态码
message	string	状态码描述
data	int	当前词库内热词数量
请求示例

curl -X POST '127.0.0.1:7100/hotwords/HW50D67839' \
-H 'Content-Type: application/json' \
-d '{
    "words":["赵六","孙七"]
}'
返回示例

{
    "status": "00000",
    "message": "success",
    "data": 6
}
删除热词词条
基本信息
Path： /hotwords/{hotwords_id}

Method： DELETE

接口描述： 在指定ID的热词词库中，删除指定的热词词条。注意：当请求不含请求体（Body）时，将删除整个热词词库。

请求参数
Body（json）

名称	类型	是否必需	说明	默认值
words	list<string>	是	待删除的热词列表	必填
返回参数
名称	类型	说明
status	string	状态码
message	string	状态码描述
data	int	当前词库内热词数量
请求示例

curl -X DELETE '127.0.0.1:7100/hotwords/HW50D67839' \
-H 'Content-Type: application/json' \
-d '{
    "words":["悟空","悟能"]
}'
返回示例

{
    "status": "00000",
    "message": "success",
    "data": 4
}
更新时间：2024-11-20