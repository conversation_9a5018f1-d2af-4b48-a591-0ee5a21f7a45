#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
系统监控和健康检查模块
提供内存监控、健康检查接口等功能
"""

# 尝试导入psutil, 如果失败则使用系统调用替代
try:
    import psutil
    HAS_PSUTIL = True
except ImportError:
    HAS_PSUTIL = False

import asyncio
import os
import sys
import threading
import time
from dataclasses import asdict, dataclass
from typing import Any, Dict, List, Optional
import json
import uvicorn
from fastapi import FastAPI, HTTPException
from fastapi.responses import JSONResponse

from modules.error_codes import ErrorCode
from modules.logger import logger
# from modules.onnx_session_pool import get_global_session_pool

if not HAS_PSUTIL:
    logger.warning("psutil未安装, 将使用系统调用获取监控信息")


def get_memory_info_fallback():
    """
    在没有psutil的情况下获取内存信息（Linux系统）
    """
    try:
        with open('/proc/meminfo', 'r') as f:
            meminfo = f.read()

        lines = meminfo.split('\n')
        mem_total = 0
        mem_available = 0

        for line in lines:
            if line.startswith('MemTotal:'):
                mem_total = int(line.split()[1]) * 1024  # 转换为字节
            elif line.startswith('MemAvailable:'):
                mem_available = int(line.split()[1]) * 1024  # 转换为字节

        if mem_total > 0:
            mem_used = mem_total - mem_available
            mem_percent = (mem_used / mem_total) * 100
            return {
                'total': mem_total,
                'used': mem_used,
                'percent': mem_percent
            }
    except Exception as e:
        logger.warning(f"获取内存信息失败: {e}")

    return {'total': 0, 'used': 0, 'percent': 0}


def get_cpu_info_fallback():
    """
    在没有psutil的情况下获取CPU信息（Linux系统）
    """
    try:
        with open('/proc/loadavg', 'r') as f:
            load_avg = f.read().strip().split()

        # 使用1分钟平均负载作为CPU使用率的近似值
        # 这不是精确的CPU百分比, 但可以作为参考
        load_1min = float(load_avg[0])

        # 获取CPU核心数
        cpu_count = os.cpu_count() or 1

        # 将负载转换为百分比（简化计算）
        cpu_percent = min((load_1min / cpu_count) * 100, 100)

        return cpu_percent
    except Exception as e:
        logger.warning(f"获取CPU信息失败: {e}")
        return 0


def get_disk_info_fallback(path='/'):
    """
    在没有psutil的情况下获取磁盘信息（Linux系统）
    """
    try:
        statvfs = os.statvfs(path)
        total = statvfs.f_frsize * statvfs.f_blocks
        free = statvfs.f_frsize * statvfs.f_bavail
        used = total - free
        percent = (used / total) * 100 if total > 0 else 0

        return {
            'total': total,
            'used': used,
            'free': free,
            'percent': percent
        }
    except Exception as e:
        logger.warning(f"获取磁盘信息失败: {e}")
        return {'total': 0, 'used': 0, 'free': 0, 'percent': 0}


@dataclass
class SystemMetrics:
    """系统指标"""
    timestamp: float
    memory_percent: float
    memory_used_mb: float
    memory_total_mb: float
    cpu_percent: float
    disk_usage_percent: float
    active_connections: int
    total_requests: int
    error_count: int


@dataclass
class HealthStatus:
    """健康状态"""
    status: str  # "healthy", "warning", "critical"
    timestamp: float
    memory_usage: float
    cpu_usage: float
    disk_usage: float
    active_connections: int
    errors: List[str]
    uptime_seconds: float


class SystemMonitor:
    """系统监控器"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化系统监控器
        Args:
            config: 监控配置
        """
        self.enabled = config.get('enable_health_check', True)
        self.memory_check_interval = config.get('memory_check_interval', 60)
        self.memory_warning_threshold = config.get('memory_warning_threshold', 80)
        self.memory_critical_threshold = config.get('memory_critical_threshold', 90)
        self.health_check_port = config.get('health_check_port', 8081)
        
        self.lock = threading.Lock()
        # 监控数据
        self.metrics_history: List[SystemMetrics] = []
        self.max_history_size = 100
        self.start_time = time.time()
        
        # 统计数据
        self.total_requests = 0
        self.error_count = 0
        self.active_connections = 0
        
        # 监控线程
        self.monitor_thread = None
        self.running = True
        
        # 健康检查应用
        self.health_app = None
        self.health_server = None
        
        if self.enabled:
            self._start_monitoring()
            logger.info(f"系统监控已启用, 内存检查间隔: {self.memory_check_interval}秒")
        else:
            logger.info("系统监控已禁用")
    
    def _start_monitoring(self):
        """启动监控"""
        # 启动监控线程
        self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self.monitor_thread.start()
        
        # 启动健康检查服务器
        self._start_health_server()
    
    def _monitor_loop(self):
        """监控循环"""
        while self.running:
            try:
                # 收集系统指标
                metrics = self._collect_metrics()
                self.metrics_history.append(metrics)
                
                # 保持历史记录大小
                if len(self.metrics_history) > self.max_history_size:
                    self.metrics_history.pop(0)
                
                # 检查内存使用情况
                self._check_memory_usage(metrics)
                
                # # 报告会话池情况
                # session_pool = get_global_session_pool()
                # logger.debug("报告会话池情况")
                # logger.debug(json.dumps(session_pool.get_pool_stats(), indent=4))

                time.sleep(self.memory_check_interval)
                
            except Exception as e:
                logger.error(f"监控循环出错: {e}")
                time.sleep(self.memory_check_interval)
    
    def _collect_metrics(self) -> SystemMetrics:
        """收集系统指标"""
        if HAS_PSUTIL:
            # 使用psutil获取系统信息
            memory = psutil.virtual_memory()
            cpu_percent = psutil.cpu_percent(interval=1)
            disk = psutil.disk_usage('/')

            return SystemMetrics(
                timestamp=time.time(),
                memory_percent=memory.percent,
                memory_used_mb=memory.used / 1024 / 1024,
                memory_total_mb=memory.total / 1024 / 1024,
                cpu_percent=cpu_percent,
                disk_usage_percent=disk.percent,
                active_connections=self.active_connections,
                total_requests=self.total_requests,
                error_count=self.error_count
            )
        else:
            # 使用fallback方法获取系统信息
            memory_info = get_memory_info_fallback()
            cpu_percent = get_cpu_info_fallback()
            disk_info = get_disk_info_fallback()

            return SystemMetrics(
                timestamp=time.time(),
                memory_percent=memory_info['percent'],
                memory_used_mb=memory_info['used'] / 1024 / 1024,
                memory_total_mb=memory_info['total'] / 1024 / 1024,
                cpu_percent=cpu_percent,
                disk_usage_percent=disk_info['percent'],
                active_connections=self.active_connections,
                total_requests=self.total_requests,
                error_count=self.error_count
            )
    
    def _check_memory_usage(self, metrics: SystemMetrics):
        """检查内存使用情况"""
        if metrics.memory_percent >= self.memory_critical_threshold:
            logger.critical(f"内存使用率达到临界值: {metrics.memory_percent:.1f}%")
        elif metrics.memory_percent >= self.memory_warning_threshold:
            logger.warning(f"内存使用率较高: {metrics.memory_percent:.1f}%")
    
    def _start_health_server(self):
        """启动健康检查服务器"""
        self.health_app = FastAPI(title="ASR服务健康检查", version="1.0")
        
        @self.health_app.get("/health")
        async def health_check():
            """健康检查接口"""
            return JSONResponse(content=asdict(self.get_health_status()))
        
        @self.health_app.get("/metrics")
        async def get_metrics():
            """获取系统指标"""
            if not self.metrics_history:
                return JSONResponse(content={"error": "暂无监控数据"})
            
            latest_metrics = self.metrics_history[-1]
            return JSONResponse(content=asdict(latest_metrics))
        
        @self.health_app.get("/metrics/history")
        async def get_metrics_history():
            """获取历史指标"""
            return JSONResponse(content=[asdict(m) for m in self.metrics_history])
        
        @self.health_app.get("/stats")
        async def get_stats():
            """获取统计信息"""
            uptime = time.time() - self.start_time
            return JSONResponse(content={
                "uptime_seconds": uptime,
                "total_requests": self.total_requests,
                "error_count": self.error_count,
                "active_connections": self.active_connections,
                "error_rate": self.error_count / max(self.total_requests, 1) * 100
            })
        
        # 在单独线程中启动健康检查服务器
        def run_health_server():
            try:
                uvicorn.run(
                    self.health_app,
                    host="0.0.0.0",
                    port=self.health_check_port,
                    log_level="warning"
                )
            except Exception as e:
                logger.error(f"健康检查服务器启动失败: {e}")
        
        health_thread = threading.Thread(target=run_health_server, daemon=True)
        health_thread.start()
        logger.info(f"健康检查服务器已启动, 端口: {self.health_check_port}")
    
    def get_health_status(self) -> HealthStatus:
        """获取健康状态"""
        if not self.metrics_history:
            return HealthStatus(
                status="unknown",
                timestamp=time.time(),
                memory_usage=0,
                cpu_usage=0,
                disk_usage=0,
                active_connections=0,
                errors=["暂无监控数据"],
                uptime_seconds=time.time() - self.start_time
            )
        
        latest_metrics = self.metrics_history[-1]
        errors = []
        status = "healthy"
        
        # 检查内存使用率
        if latest_metrics.memory_percent >= self.memory_critical_threshold:
            status = "critical"
            errors.append(f"内存使用率过高: {latest_metrics.memory_percent:.1f}%")
        elif latest_metrics.memory_percent >= self.memory_warning_threshold:
            status = "warning"
            errors.append(f"内存使用率较高: {latest_metrics.memory_percent:.1f}%")
        
        # 检查磁盘使用率
        if latest_metrics.disk_usage_percent >= 90:
            status = "critical"
            errors.append(f"磁盘使用率过高: {latest_metrics.disk_usage_percent:.1f}%")
        elif latest_metrics.disk_usage_percent >= 80:
            if status != "critical":
                status = "warning"
            errors.append(f"磁盘使用率较高: {latest_metrics.disk_usage_percent:.1f}%")
        
        # 检查错误率
        error_rate = latest_metrics.error_count / max(latest_metrics.total_requests, 1) * 100
        if error_rate >= 10:
            status = "critical"
            errors.append(f"错误率过高: {error_rate:.1f}%")
        elif error_rate >= 5:
            if status != "critical":
                status = "warning"
            errors.append(f"错误率较高: {error_rate:.1f}%")
        
        return HealthStatus(
            status=status,
            timestamp=latest_metrics.timestamp,
            memory_usage=latest_metrics.memory_percent,
            cpu_usage=latest_metrics.cpu_percent,
            disk_usage=latest_metrics.disk_usage_percent,
            active_connections=latest_metrics.active_connections,
            errors=errors,
            uptime_seconds=time.time() - self.start_time
        )
    
    def increment_request_count(self):
        """增加请求计数"""
        with self.lock:
            self.total_requests += 1
    
    def increment_error_count(self):
        """增加错误计数"""
        with self.lock:
            self.error_count += 1
    
    def set_active_connections(self, count: int):
        """设置活跃连接数"""
        with self.lock:
            self.active_connections = count
    
    def shutdown(self):
        """关闭监控器"""
        self.running = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=5)


# 全局监控器实例
_global_monitor: Optional[SystemMonitor] = None


def get_global_monitor() -> Optional[SystemMonitor]:
    """获取全局监控器实例"""
    return _global_monitor


def initialize_global_monitor(config: Dict[str, Any]):
    """初始化全局监控器"""
    global _global_monitor
    if _global_monitor is None:
        _global_monitor = SystemMonitor(config)
    return _global_monitor


def shutdown_global_monitor():
    """关闭全局监控器"""
    global _global_monitor
    if _global_monitor:
        _global_monitor.shutdown()
        _global_monitor = None
