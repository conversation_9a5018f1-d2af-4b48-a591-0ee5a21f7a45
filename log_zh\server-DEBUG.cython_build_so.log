2025-07-18 08:39:10.738 | INFO  | contextlib:__aenter__  :171 - 日志系统初始化成功, 配置: {'level': 'DEBUG', 'rotation': '1 day', 'retention': '7 days', 'compression': 'zip', 'max_file_size': '100 MB'}
2025-07-18 08:39:10.738 | WARNING | importlib._bootstrap:_call_with_frames_removed:219 - psutil未安装, 将使用系统调用获取监控信息
2025-07-18 08:39:10.743 | INFO  | contextlib:__aenter__  :171 - 健康检查服务器已启动, 端口: 11082
2025-07-18 08:39:10.743 | INFO  | contextlib:__aenter__  :171 - 系统监控已启用, 内存检查间隔: 60秒
2025-07-18 08:39:10.743 | DEBUG | contextlib:__aenter__  :171 - 监控系统初始化成功
2025-07-18 08:39:10.744 | DEBUG | contextlib:__aenter__  :171 - 会话池初始化成功
2025-07-18 08:39:10.744 | DEBUG | contextlib:__aenter__  :171 - 所有模块初始化完成
2025-07-18 08:39:10.744 | INFO  | contextlib:__aenter__  :171 - 初始化统一ASR管理器, 支持语种: ['zh', 'en', 'ru', 'kk', 'kkin', 'ug']
2025-07-18 08:39:10.747 | INFO  | contextlib:__aenter__  :171 - 单语种模式：加载语种 zh
2025-07-18 08:39:10.748 | DEBUG | contextlib:__aenter__  :171 - 加载模型: /ws/MODELS/online_onnx_zh, fp16: False, quant: False, device: cpu
2025-07-18 08:39:10.748 | DEBUG | contextlib:__aenter__  :171 - ['CPUExecutionProvider']
2025-07-18 08:39:11.125 | DEBUG | contextlib:__aenter__  :171 - ONNX Session配置: inter_op_threads=0, intra_op_threads=0, execution_mode=PARALLEL
2025-07-18 08:39:11.977 | DEBUG | contextlib:__aenter__  :171 - 模型 zh_encoder 注册成功，预创建会话: 2a0c3e03
2025-07-18 08:39:11.977 | DEBUG | contextlib:__aenter__  :171 - 加载模型: /ws/MODELS/online_onnx_zh, fp16: False, quant: False, device: cpu
2025-07-18 08:39:11.977 | DEBUG | contextlib:__aenter__  :171 - ['CPUExecutionProvider']
2025-07-18 08:39:11.981 | DEBUG | contextlib:__aenter__  :171 - ONNX Session配置: inter_op_threads=0, intra_op_threads=0, execution_mode=PARALLEL
2025-07-18 08:39:11.991 | DEBUG | contextlib:__aenter__  :171 - 模型 zh_ctc 注册成功，预创建会话: bcffa621
2025-07-18 08:39:11.991 | DEBUG | contextlib:__aenter__  :171 - 语种 zh 模型已注册到会话池
2025-07-18 08:39:12.005 | DEBUG | contextlib:__aenter__  :171 - 构建的模型配置: {'feat_configs': {'feat_type': 'fbank', 'num_mel_bins': 80, 'frame_length': 25, 'frame_shift': 10, 'dither': 1.0, 'n_fft': 400, 'hop_length': 160}, 'batch': 1, 'chunk_size': 16, 'left_chunks': 16, 'reverse_weight': 0.3, 'decoding_window': 67, 'subsampling_rate': 4, 'right_context': 6, 'context_list_path': '/ws/MODELS/online_onnx_zh/hotwords.txt', 'context_graph_score': 40, 'output_size': 512, 'num_blocks': 12, 'cnn_module_kernel': 15, 'head': 8, 'feature_size': 80, 'vocab_size': 5538}
2025-07-18 08:39:12.006 | DEBUG | contextlib:__aenter__  :171 - 加载词表: /ws/MODELS/online_onnx_zh/units.txt
2025-07-18 08:39:12.010 | DEBUG | contextlib:__aenter__  :171 - 启用后处理: 转全小写字母
2025-07-18 08:39:12.011 | INFO  | contextlib:__aenter__  :171 - 模型加载完成：成功 1/1 个语种
2025-07-18 08:39:12.011 | INFO  | contextlib:__aenter__  :171 - 单语种模式 LID功能不可用
2025-07-18 08:39:12.011 | INFO  | contextlib:__aenter__  :171 - Server start, init manager, LID_MANAGER, ASR_MANAGER


2025-07-18 08:39:23.651 | DEBUG | fastapi.routing:app         :383 - client_id:000 - 开始初始化连接
2025-07-18 08:39:23.652 | INFO  | fastapi.routing:app         :383 - client_id:000 - >>> [请求] 新客户连接，当前活跃连接数: 1
2025-07-18 08:39:23.653 | DEBUG | fastapi.routing:app         :383 - client_id:111 - 开始初始化连接
2025-07-18 08:39:23.653 | INFO  | fastapi.routing:app         :383 - client_id:111 - >>> [请求] 新客户连接，当前活跃连接数: 2
2025-07-18 08:39:24.062 | INFO  | asyncio.events:_run        :81 - client_id:000 - 设置自定义分隔符: "，"
2025-07-18 08:39:24.062 | INFO  | asyncio.events:_run        :81 - client_id:000 - 初始化解码器, 使用默认语种: zh
2025-07-18 08:39:24.091 | DEBUG | asyncio.events:_run        :81 - 自定义分隔符: ，
2025-07-18 08:39:24.091 | INFO  | asyncio.events:_run        :81 - client_id:000 - 解码器初始化完成, 使用分隔符: "，"
2025-07-18 08:39:24.098 | DEBUG | asyncio.events:_run        :81 - client_id:000 - >>> [解析] 第0个数据包, 累计帧数: 38
2025-07-18 08:39:24.099 | DEBUG | asyncio.events:_run        :81 - client_id:000 - 所需帧数: 67, 目前帧数: 38, 继续等待数据包
2025-07-18 08:39:24.100 | INFO  | asyncio.events:_run        :81 - client_id:111 - 设置自定义分隔符: "，"
2025-07-18 08:39:24.100 | INFO  | asyncio.events:_run        :81 - client_id:111 - 初始化解码器, 使用默认语种: zh
2025-07-18 08:39:24.101 | DEBUG | asyncio.events:_run        :81 - 自定义分隔符: ，
2025-07-18 08:39:24.101 | INFO  | asyncio.events:_run        :81 - client_id:111 - 解码器初始化完成, 使用分隔符: "，"
2025-07-18 08:39:24.104 | DEBUG | asyncio.events:_run        :81 - client_id:111 - >>> [解析] 第0个数据包, 累计帧数: 38
2025-07-18 08:39:24.104 | DEBUG | asyncio.events:_run        :81 - client_id:111 - 所需帧数: 67, 目前帧数: 38, 继续等待数据包
2025-07-18 08:39:24.472 | DEBUG | asyncio.events:_run        :81 - client_id:000 - >>> [解析] 第1个数据包, 累计帧数: 76
2025-07-18 08:39:24.472 | DEBUG | asyncio.events:_run        :81 - client_id:000 - 所需帧数: 67, 目前帧数: 76, 开始解码
2025-07-18 08:39:24.473 | DEBUG | asyncio.events:_run        :81 - 客户 000 获取现有会话: zh_encoder - 2a0c3e03, 池大小: 1
2025-07-18 08:39:24.538 | DEBUG | asyncio.events:_run        :81 - 释放会话: zh_encoder - 2a0c3e03, 当前可用: 1/1
2025-07-18 08:39:24.538 | DEBUG | asyncio.events:_run        :81 - client_id:000 - 当前推理累计时点: 16
2025-07-18 08:39:24.539 | DEBUG | asyncio.events:_run        :81 - client_id:000 - ctc 搜索起始时点: 0 搜索片段长度: tensor([16])
2025-07-18 08:39:24.539 | DEBUG | asyncio.events:_run        :81 - 客户 000 获取现有会话: zh_ctc - bcffa621, 池大小: 1
2025-07-18 08:39:24.540 | DEBUG | asyncio.events:_run        :81 - 释放会话: zh_ctc - bcffa621, 当前可用: 1/1
