#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
恢复原始Python文件脚本
用于将备份的.py文件恢复，删除编译的.so文件
"""

import os
import shutil
import glob
from pathlib import Path

def restore_files():
    """恢复原始Python文件"""
    backup_dir = Path("backup/original_py_files")

    if not backup_dir.exists():
        print("错误: 未找到备份目录")
        return False

    print("开始恢复原始Python文件...")

    # 删除.so文件
    so_files = []
    so_files.extend(glob.glob("*.so"))
    so_files.extend(glob.glob("modules/*.so"))
    so_files.extend(glob.glob("utils/*.so"))

    for so_file in so_files:
        if os.path.exists(so_file):
            os.remove(so_file)
            print(f"已删除编译文件: {so_file}")

    # 恢复Python文件
    for backup_file in backup_dir.rglob("*.py"):
        rel_path = backup_file.relative_to(backup_dir)
        target_path = Path(rel_path)

        # 创建目标目录
        target_path.parent.mkdir(parents=True, exist_ok=True)

        # 复制文件
        shutil.copy2(backup_file, target_path)
        print(f"已恢复文件: {backup_file} -> {target_path}")

    print("恢复完成！")
    return True

if __name__ == "__main__":
    restore_files()
