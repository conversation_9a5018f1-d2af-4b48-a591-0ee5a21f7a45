2025-07-11 10:04:43.898 | INFO  | modules.config:load_global_config:260 - 成功加载全局配置: /ws/ministream_v2_refactor/modules/../conf/config_debug.yaml
2025-07-11 10:04:43.898 | INFO  | modules.config:load_language_config:405 - 加载语种配置: /ws/ministream_v2_refactor/modules/../conf/zh.yaml
2025-07-11 10:04:43.960 | INFO  | modules.config:load_global_config:260 - 成功加载全局配置: /ws/ministream_v2_refactor/modules/../conf/config_debug.yaml
2025-07-11 10:04:43.966 | INFO  | modules.config:init_logger :593 - 日志系统初始化成功, 配置: {'level': 'DEBUG', 'rotation': '1 day', 'retention': '7 days', 'compression': 'zip', 'max_file_size': '100 MB'}
2025-07-11 10:04:43.979 | INFO  | modules.monitoring:_start_health_server:308 - 健康检查服务器已启动, 端口: 8081
2025-07-11 10:04:43.981 | INFO  | modules.monitoring:__init__    :176 - 系统监控已启用, 内存检查间隔: 60秒
2025-07-11 10:04:43.982 | INFO  | modules.asr_manager:__init__    :39 - 初始化统一ASR管理器, 支持语种: ['zh', 'en', 'ru', 'kk', 'kkin', 'ug']
2025-07-11 10:04:43.984 | INFO  | modules.asr_manager:load_models :59 - 单语种模式：加载语种 zh
2025-07-11 10:04:45.528 | INFO  | modules.asr_manager:load_models :72 - 模型加载完成：成功 1/1 个语种
2025-07-11 10:04:45.528 | INFO  | server :lifespan    :109 - 单语种模式 LID功能不可用
2025-07-11 10:04:45.529 | INFO  | server :lifespan    :114 - Server start, init manager, LID_MANAGER, ASR_MANAGER


2025-07-11 10:04:50.562 | INFO  | server :websocket_endpoint:237 - client_id:000 - >>> [请求] 新客户连接，当前活跃连接数: 1
2025-07-11 10:04:50.564 | INFO  | server :websocket_endpoint:237 - client_id:111 - >>> [请求] 新客户连接，当前活跃连接数: 2
2025-07-11 10:04:50.974 | INFO  | modules.connect:on_check    :468 - client_id:000 - 设置自定义分隔符: "，"
2025-07-11 10:04:50.974 | INFO  | modules.connect:_init_decoder:141 - client_id:000 - 初始化解码器, 使用默认语种: zh
2025-07-11 10:04:51.016 | INFO  | modules.connect:_init_decoder:148 - client_id:000 - 解码器初始化完成, 使用分隔符: "，"
2025-07-11 10:04:51.023 | INFO  | modules.connect:on_check    :468 - client_id:111 - 设置自定义分隔符: "，"
2025-07-11 10:04:51.023 | INFO  | modules.connect:_init_decoder:141 - client_id:111 - 初始化解码器, 使用默认语种: zh
2025-07-11 10:04:51.024 | INFO  | modules.connect:_init_decoder:148 - client_id:111 - 解码器初始化完成, 使用分隔符: "，"
2025-07-11 10:04:58.839 | INFO  | modules.connect:on_decode   :980 - client_id:111 - *** 最后一个数据包完成解码 ***
2025-07-11 10:04:58.840 | INFO  | modules.connect:on_result   :430 - client_id:111 - <<< [响应] 最终识别结果: 我想在理性，哲学，以及心理学的层面上理解，美得体验究竟是什么
2025-07-11 10:04:58.843 | INFO  | server :receive     :329 - client_id: 111 - 关闭客户连接，当前活跃连接数: 1
2025-07-11 10:05:03.707 | INFO  | modules.connect:on_decode   :980 - client_id:000 - *** 最后一个数据包完成解码 ***
2025-07-11 10:05:03.707 | INFO  | modules.connect:on_result   :430 - client_id:000 - <<< [响应] 最终识别结果: 很高兴来到这里，与大家探讨我这为关心的一个主题，那就是美，我研究艺术与审美哲学，事实上，这是我的工作
2025-07-11 10:05:03.709 | INFO  | server :receive     :329 - client_id: 000 - 关闭客户连接，当前活跃连接数: 0
2025-07-11 10:09:01.197 | INFO  | server :lifespan    :120 - 正在关闭ASR服务...
2025-07-11 10:09:06.198 | INFO  | server :lifespan    :127 - 系统监控已关闭
2025-07-11 10:09:11.265 | INFO  | server :lifespan    :134 - ONNX会话池已关闭
2025-07-11 10:09:11.270 | INFO  | server :lifespan    :144 - 实时转写已关闭
2025-07-11 10:09:11.270 | INFO  | server :lifespan    :148 - Server shutdown, delete all global resources
