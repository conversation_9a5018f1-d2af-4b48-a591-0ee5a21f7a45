#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试setup.py编译脚本的功能
用于验证编译逻辑是否正确
"""

import os
import sys
import tempfile
import shutil
from pathlib import Path

def create_test_environment():
    """创建测试环境"""
    # 创建临时目录
    test_dir = tempfile.mkdtemp(prefix="asr_test_")
    print(f"创建测试目录: {test_dir}")
    
    # 创建测试文件结构
    test_files = {
        "test_server.py": '''
def start_server():
    print("Test server started")

if __name__ == "__main__":
    start_server()
''',
        "modules/test_config.py": '''
class Config:
    def __init__(self):
        self.value = "test"
    
    def get_value(self):
        return self.value
''',
        "utils/test_common.py": '''
def test_function():
    return "test result"

class TestClass:
    def method(self):
        return "test method"
'''
    }
    
    # 创建文件
    for file_path, content in test_files.items():
        full_path = Path(test_dir) / file_path
        full_path.parent.mkdir(parents=True, exist_ok=True)
        with open(full_path, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"创建测试文件: {full_path}")
    
    return test_dir, test_files

def create_test_setup_py(test_dir):
    """创建简化的测试setup.py"""
    setup_content = '''
import os
import sys
import glob
import shutil
from pathlib import Path
from distutils.core import setup
from distutils.extension import Extension

# 模拟Cython导入（如果没有安装Cython）
try:
    from Cython.Build import cythonize
    from Cython.Distutils import build_ext
    CYTHON_AVAILABLE = True
except ImportError:
    print("Cython未安装，使用模拟模式")
    CYTHON_AVAILABLE = False
    
    class MockBuildExt:
        def __init__(self):
            self.extensions = []
        
        def run(self):
            print("模拟编译过程...")
            # 模拟生成.so文件
            for ext in self.extensions:
                original_py_file = ext.sources[0]
                target_dir = os.path.dirname(original_py_file)
                original_name = os.path.splitext(os.path.basename(original_py_file))[0]
                
                if target_dir:
                    so_file = os.path.join(target_dir, original_name + ".so")
                else:
                    so_file = original_name + ".so"
                
                # 创建模拟的.so文件
                with open(so_file, 'w') as f:
                    f.write(f"# Mock .so file for {original_py_file}")
                print(f"模拟生成: {so_file}")
    
    build_ext = MockBuildExt

class CustomBuildExt(build_ext):
    """自定义构建扩展类，处理编译后的文件组织和替换"""
    
    def run(self):
        if CYTHON_AVAILABLE:
            # 执行标准构建
            build_ext.run(self)
        else:
            # 模拟构建
            super().run()
        
        # 记录需要删除的原始Python文件
        self.original_py_files = []
        
        # 处理编译后的文件
        for ext in self.extensions:
            original_py_file = ext.sources[0]
            self.original_py_files.append(original_py_file)
            
            # 计算目标.so文件路径
            target_dir = os.path.dirname(original_py_file)
            original_name = os.path.splitext(os.path.basename(original_py_file))[0]
            target_filename = original_name + ".so"

            if target_dir:
                target_path = os.path.join(target_dir, target_filename)
            else:
                target_path = target_filename
            
            print(f"目标文件: {target_path}")
            
            # 检查.so文件是否存在
            if os.path.exists(target_path):
                print(f"编译文件已存在: {target_path}")
            else:
                print(f"警告: 编译文件不存在: {target_path}")
        
        # 删除原始Python文件
        self.remove_original_files()
    
    def remove_original_files(self):
        """删除原始Python文件"""
        for py_file in self.original_py_files:
            if os.path.exists(py_file):
                os.remove(py_file)
                print(f"已删除原始文件: {py_file}")

def find_python_files():
    """查找需要编译的Python文件"""
    python_files = []
    
    # 查找所有.py文件
    for root, dirs, files in os.walk("."):
        for file in files:
            if file.endswith('.py') and file != 'test_setup.py':
                file_path = os.path.join(root, file)
                python_files.append(file_path)
    
    return python_files

def create_extensions():
    """创建扩展"""
    python_files = find_python_files()
    extensions = []
    
    for py_file in python_files:
        module_name = py_file.replace(os.sep, '.').replace('.py', '').lstrip('./')
        
        ext = Extension(
            name=module_name,
            sources=[py_file],
        )
        extensions.append(ext)
        print(f"添加编译目标: {py_file} -> {module_name}")
    
    return extensions

if __name__ == "__main__":
    print("测试编译脚本")
    
    extensions = create_extensions()
    
    if CYTHON_AVAILABLE:
        setup(
            ext_modules=cythonize(extensions),
            cmdclass={'build_ext': CustomBuildExt},
        )
    else:
        # 模拟模式
        custom_build = CustomBuildExt()
        custom_build.extensions = extensions
        custom_build.run()
'''
    
    setup_path = Path(test_dir) / "test_setup.py"
    with open(setup_path, 'w', encoding='utf-8') as f:
        f.write(setup_content)
    
    return setup_path

def run_test():
    """运行测试"""
    print("=" * 60)
    print("测试setup.py编译逻辑")
    print("=" * 60)
    
    # 创建测试环境
    test_dir, test_files = create_test_environment()
    
    try:
        # 切换到测试目录
        original_cwd = os.getcwd()
        os.chdir(test_dir)
        
        # 创建测试setup.py
        setup_path = create_test_setup_py(test_dir)
        
        # 运行测试
        print("\n运行编译测试...")
        os.system(f"{sys.executable} test_setup.py build_ext --inplace")
        
        # 检查结果
        print("\n检查编译结果...")
        for file_path in test_files.keys():
            py_file = Path(file_path)
            so_file = py_file.with_suffix('.so')
            
            if so_file.exists():
                print(f"✅ 生成了.so文件: {so_file}")
            else:
                print(f"❌ 未生成.so文件: {so_file}")
            
            if py_file.exists():
                print(f"❌ 原始.py文件仍存在: {py_file}")
            else:
                print(f"✅ 原始.py文件已删除: {py_file}")
        
    finally:
        # 恢复原始目录
        os.chdir(original_cwd)
        
        # 清理测试目录
        shutil.rmtree(test_dir)
        print(f"\n清理测试目录: {test_dir}")
    
    print("\n测试完成!")

if __name__ == "__main__":
    run_test()
