# ASR流式语音识别服务依赖包
# 核心依赖

# Web框架和服务器
fastapi>=0.104.0
uvicorn[standard]>=0.24.0
websockets>=12.0

# 机器学习和深度学习
torch>=2.0.0
torchaudio>=2.0.0
onnx>=1.15.0
onnxruntime>=1.16.0
numpy>=1.24.0

# 音频处理
soundfile>=0.12.0
pydub>=0.25.0
webrtcvad>=2.0.10

# 配置和数据处理
PyYAML>=6.0
dataclasses-json>=0.6.0

# 日志系统
loguru>=0.7.0

# 系统监控（可选）
psutil>=5.9.0

# 测试和评估工具（用于客户端测试）
jiwer>=3.0.0

# 工具库
fire>=0.5.0

# 开发和调试工具（可选）
# pytest>=7.0.0
# pytest-asyncio>=0.21.0

# 注意事项：
# 1. torch和torchaudio版本需要根据实际CUDA版本调整
# 2. onnxruntime可以选择CPU版本(onnxruntime)或GPU版本(onnxruntime-gpu)
# 3. psutil在某些环境下可能安装失败，代码中已做兼容处理
# 4. 如需GPU加速，请安装对应的CUDA版本的torch和onnxruntime-gpu

# 生产环境建议固定版本号，开发环境可使用>=符号
