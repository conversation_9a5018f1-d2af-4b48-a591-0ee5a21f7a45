#! /user/bin/env python
#! -*- coding: utf-8 -*-
# File    :  connect.py
# Time    :  2025/04/17 14:20:49
# Author  :  lh
# Version :  1.0
# Description:


import asyncio
import base64
import time
import uuid
from datetime import datetime
from typing import Any, Dict, List, Optional, Tuple

import numpy as np
import torch
from fastapi import WebSocket

from modules.decoder import ASRDecoder
from modules.error_codes import ErrorCode, ErrorResponse
from modules.feature import FeaturePipeline
from modules.lid_manager import LIDManager
from modules.logger import logger


def format_time(timestamp):
    return datetime.fromtimestamp(timestamp).strftime('%H:%M:%S.%f')[:-3]

class ConnectionManager:
    """
    连接管理类, 用于处理客户端连接、音频数据解析和管理解码。

    统一架构：单语种看作特殊的多语种（只有一个语种）, 所有语种相关资源由ASR管理器统一管理

    Attributes:
        active_connection (Dict[str, Any]): 存储活动连接的字典
        client_states (Dict[str, Dict[str, Any]]): 客户端状态信息
        valid_sample_rate_list (List[int]): 支持的采样率列表
        expected_data_size (int): 预期音频数据大小
        packet_interval (float): 数据包间隔时间(秒)
        required_decoding_window (int): 解码所需的最小帧数
    """

    def __init__(self, args, asr_manager, lid_manager=None):
        """
        统一的连接管理器初始化, 单语种和多语种使用相同的架构

        Args:
            args: 服务器配置参数
            asr_manager: ASR管理器, 统一管理所有语种的模型和资源
            lid_manager: 语种识别管理器（可选, 多语种模式时使用）
        """
        self.args = args

        # 错误处理配置
        self.enable_detailed_errors = getattr(args, 'error_handling', {}).get('enable_detailed_errors', True)

        # 客户连接、客户端状态
        self.active_connection: Dict[str: WebSocket] = {}
        self.client_states: Dict[str: dict] = {}

        # audio 相关的参数
        self.valid_sample_rate_list = args.audio.valid_sample_rate_list
        self.expected_sample_rate = args.audio.expected_sample_rate
        self.expected_sample_width = args.audio.expected_sample_width
        self.expected_sample_channels = args.audio.expected_sample_channels
        self.expected_data_size = args.audio.expected_data_size
        self.packet_interval = args.audio.max_packet_interval
        # 音频缓存清理配置
        self.max_feat_cache = min(args.audio.max_feat_cache, 120)  # 最多120个数据包（60秒）
        self.cache_cleanup_interval = min(args.audio.cache_clean_packet_interval, 20)  # 最多20个数据包检查一次
        # 帧时长默认25ms
        self.frame_samples = 25.0 / 1000

        # 统一的LID和ASR管理器
        self.lid_manager = lid_manager
        self.asr_manager = asr_manager

        # !!!(TODO) 实验特性: 需要考虑每个onnx会话的并发数与内存使用压力
        # 是否根据客户请求数量动态扩容(每多一个活跃连接数请求相同的模型会话, 就新扩容一个, 直到达到会话池上限.
        # 注意扩容有开销, 用户感知的第一个返回消息会延后, 所以请谨慎选择,
        # 实测4个短语音请求(10s内), 不值得动态扩容, 等待同步调用解码反而RTF更小, 因为每个onnx会话已经开启最大并发性能, 能充分利用cpu核心并发和线程并发
        self.async_decode = args.onnx_session_pool.enabled
        if self.async_decode:
            logger.info(f"启用会话池动态扩容, 最大会话数: {args.onnx_session_pool.max_sessions_per_model}")

    def _get_feat_pipe(self, client_id: str):
        """
        统一获取特征管道, 直接从ASR管理器获取

        Args:
            client_id: 客户端ID

        Returns:
            FeaturePipeline: 特征管道实例
        """
        # 统一架构：所有特征管道都通过ASR管理器获取
        current_language = self.client_states[client_id].get("current_language")

        # 优先使用当前语种的特征管道
        if current_language and self.asr_manager.is_language_loaded(current_language):
            feat_pipe = self.asr_manager.get_feat_pipe(current_language)
            if feat_pipe:
                return feat_pipe

        # 如果没有当前语种, 使用默认语种的特征管道
        default_lang = self.args.default_language
        if self.asr_manager.is_language_loaded(default_lang):
            feat_pipe = self.asr_manager.get_feat_pipe(default_lang)
            if feat_pipe:
                return feat_pipe

        # 如果都失败了, 抛出异常
        raise RuntimeError(f"无法获取客户端 {client_id} 的特征管道")

    def _init_decoder(self, client_id: str, custom_separator: str = None) -> None:
        """
        统一的解码器初始化方法, 消除重复代码

        Args:
            client_id: 客户端ID
            custom_separator: 自定义分隔符
        """
        # 统一的解码器配置获取逻辑
        current_language = self.client_states[client_id].get("current_language")

        if current_language and self.asr_manager.is_language_loaded(current_language):
            # 使用当前语种的配置
            decoder_configs = self.asr_manager.get_config(current_language)
            decoder_symbol_table = self.asr_manager.get_symbol_table(current_language)
            lang_configs = self.args.model[current_language]
            logger.info(f"client_id:{client_id} - 初始化解码器, 使用语种: {current_language}")
        else:
            # 使用默认语种的配置
            current_language = default_lang = self.args.default_language
            decoder_configs = self.asr_manager.get_config(default_lang)
            decoder_symbol_table = self.asr_manager.get_symbol_table(default_lang)
            lang_configs = self.args.model[default_lang]
            logger.info(f"client_id:{client_id} - 初始化解码器, 使用默认语种: {default_lang}")

        # 创建解码器（传递语种代码以支持会话池）
        self.client_states[client_id]["decoder"] = ASRDecoder(
            lang_configs, decoder_configs, decoder_symbol_table, custom_separator, current_language    # TODO报错 local variable 'default_lang' referenced before assignment
        )
        self.required_decoding_window = self.client_states[client_id]["decoder"].decoding_window
        logger.info(f"client_id:{client_id} - 解码器初始化完成, 使用分隔符: \"{self.client_states[client_id]['decoder'].separator}\"")


    def _handle_language_options(self, client_id: str, json_data: dict):
        """
        统一处理客户端请求中的语种相关选项

        Args:
            client_id: 客户端ID
            json_data: 客户端请求数据
        """
        # 统一处理语种选项, 单语种模式下某些选项会被忽略但不报错

        # 处理客户端指定的语种
        if "language" in json_data:
            specified_language = json_data["language"]
            if self.asr_manager and self.asr_manager.is_language_loaded(specified_language):
                # 客户端指定了支持的语种, 直接使用该语种, 跳过LID
                logger.info(f"client_id:{client_id} - 客户端指定语种: {specified_language}, 跳过LID")
                self.client_states[client_id]["lid_enabled"] = False
                self.client_states[client_id]["current_language"] = specified_language
                self.client_states[client_id]["detected_language"] = specified_language
                self.client_states[client_id]["language_confidence"] = 1.0
                self.client_states[client_id]["lid_confirmed"] = True

                # 立即切换到指定语种
                asyncio.create_task(self._switch_asr_model(client_id, specified_language))
            else:
                logger.warning(f"client_id:{client_id} - 客户端指定的语种 {specified_language} 不支持, 将使用LID")

        # 处理LID启用选项（单语种模式下会被忽略）
        if "enable_lid" in json_data:
            # 只有在多语种模式且有LID管理器时才生效
            if len(self.asr_manager.supported_languages) > 1 and self.lid_manager:
                # 只有在没有指定语种的情况下才允许禁用LID
                if "language" not in json_data or not self.asr_manager.is_language_loaded(json_data.get("language")):
                    self.client_states[client_id]["lid_enabled"] = bool(json_data["enable_lid"])
                    logger.info(f"client_id:{client_id} - LID 功能: {'启用' if self.client_states[client_id]['lid_enabled'] else '禁用'}")
            else:
                logger.debug(f"client_id:{client_id} - 单语种模式下忽略 LID 启用选项")

    async def connect(self, websocket: WebSocket, client_id: str):
        await websocket.accept()

        # 存储ws连接
        self.active_connection[client_id] = websocket
        # 初始化客户状态
        client_state = {
            "feat_buffer": [],   # 缓存的特征
            "frame_nums": 0,     # 累计的帧数量
            "packet_index": -1,   # 到达数据包的索引
            "sample_rate": None,
            "last_packet_time": None,     # 上一个数据包到达的时间 (用于检查实时率)
            "is_first": True,   # 是否为第一个数据包  (接受到第一个数据包时置为False, 用于后续验证数据包大小、采样率)
            "is_final": False,  # 是否为最后一个数据包
            "is_final_result": False,  # 是否为最后的识别结果
            "custom_separator": None,  # 用户自定义分隔符, 将在第一个数据包中设置
            "decoder": None,    # 解码器将在收到第一个数据包时初始化
            "decoder_result" : "",
            "decoder_result_history" : "",   # 如果后续切换语种, 会重置解码器, 此前的解码结果存在这里, 发送返回消息时拼接
            "message_id_base": f"client_id:{client_id}_{uuid.uuid4()}",
            "message_counter": 0,
            # 缓存管理
            "packet_count": 0,  # 已处理的数据包计数
            "feat_buffer_timestamps": [],  # 特征缓存的时间戳
            "audio_buffer_timestamps": [],  # 音频缓存的时间戳
        }

        # 统一的语种相关状态初始化
        # 判断是否为多语种模式（支持的语种数量大于1）
        is_multi_lang = len(self.asr_manager.supported_languages) > 1

        # 设置初始语种
        if is_multi_lang:
            # 多语种模式：初始状态为未确定语种
            initial_language = None
            lid_enabled = True
            lid_confirmed = False
            language_confidence = 0.0
        else:
            # 单语种模式：直接使用唯一支持的语种
            initial_language = self.asr_manager.supported_languages[0]
            lid_enabled = False
            lid_confirmed = True
            language_confidence = 1.0

        client_state.update({
            # LID相关状态
            "lid_enabled": lid_enabled,  # 是否启用语种识别
            "detected_language": initial_language,  # 检测到的语种
            "language_confidence": language_confidence,  # 语种识别置信度
            "audio_buffer": [],  # 用于LID的原始音频缓存
            "audio_duration": 0.0,  # 连续有效话音时长（秒）
            "lid_attempts": 0,  # LID尝试次数
            "lid_confirmed": lid_confirmed,  # LID结果是否已确认
            "vad_speech_detected": False,  # 是否检测到有效语音
            # 多语种对话支持
            "silence_time_sec": 0,  # 连续静音时长
            # 当前语种状态（统一管理）
            "current_language": initial_language,  # 当前使用的语种
            "current_symbol_table": None,  # 当前使用的词表（动态获取）
            "current_config": None,  # 当前使用的配置（动态获取）
            "current_lang_config": None,  # 当前使用的语种配置（动态获取）
        })

        self.client_states[client_id] = client_state

        # 更新活跃连接数
        from modules.monitoring import get_global_monitor
        monitor = get_global_monitor()
        if monitor:
            monitor.set_active_connections(len(self.client_states))
    
    def _cleanup_old_cache(self, client_id: str):
        """
        清理旧的音频和特征缓存, 防止内存无限增长
        Args:
            client_id: 客户端ID
        """
        state = self.client_states[client_id]
        current_time = time.time()

        # 清理特征缓存（保留最近的max_feat_cache个）
        if len(state["feat_buffer"]) > self.max_feat_cache:
            remove_count = len(state["feat_buffer"]) - self.max_feat_cache
            state["feat_buffer"] = state["feat_buffer"][remove_count:]
            state["feat_buffer_timestamps"] = state["feat_buffer_timestamps"][remove_count:]
            logger.debug(f"client_id:{client_id} - 清理了{remove_count}个旧的特征缓存")

        # 清理音频缓存（保留最近60秒的数据）
        if state["audio_buffer"] and state["audio_buffer_timestamps"]:
            cutoff_time = current_time - 60.0  # 保留60秒
            keep_indices = [i for i, ts in enumerate(state["audio_buffer_timestamps"]) if ts > cutoff_time]

            if len(keep_indices) < len(state["audio_buffer"]):
                remove_count = len(state["audio_buffer"]) - len(keep_indices)
                state["audio_buffer"] = [state["audio_buffer"][i] for i in keep_indices]
                state["audio_buffer_timestamps"] = [state["audio_buffer_timestamps"][i] for i in keep_indices]
                logger.debug(f"client_id:{client_id} - 清理了{remove_count}个旧的音频缓存")

    # @profile
    async def disconnect(self, client_id: str):
        logger.debug(f"client_id:{client_id} - 开始断开连接，当前活跃连接数: {len(self.client_states)}")

        try:
            if client_id in self.active_connection:
                await self.active_connection[client_id].close()
            else:
                logger.warning(f"client_id:{client_id} - WebSocket连接不在active_connection中")
        except Exception as e:
            logger.debug(f"client_id:{client_id} - WebSocket连接已经关闭或关闭时出错: {e}")
        # 移除ws连接
        if client_id in self.active_connection:
            del self.active_connection[client_id]
        else:
            logger.warning(f"client_id:{client_id} - 不在active_connection中")

        # 移除客户状态, 并清空相关资源
        if client_id in self.client_states:
            logger.debug(f"client_id:{client_id} - 开始清理客户端状态和缓存")
            # 清空缓存
            self.client_states[client_id]["feat_buffer"].clear()
            self.client_states[client_id]["feat_buffer_timestamps"].clear()
            # 清空LID音频缓存
            if "audio_buffer" in self.client_states[client_id]:
                self.client_states[client_id]["audio_buffer"].clear()
                self.client_states[client_id]["audio_buffer_timestamps"].clear()
            # 清除ASRDecoder实例
            if "decoder" in self.client_states[client_id]:
                del self.client_states[client_id]["decoder"]
            # 移除客户状态
            del self.client_states[client_id]

            # 更新活跃连接数
            from modules.monitoring import get_global_monitor
            monitor = get_global_monitor()
            if monitor:
                monitor.set_active_connections(len(self.client_states))
                logger.debug(f"client_id:{client_id} - 已经断开连接，活跃连接数已更新: {len(self.client_states)}")
        else:
            logger.warning(f"client_id:{client_id} - 不在client_states中，可能已经清理过")

    def generate_message_id(self, client_id: str) -> str:
        self.client_states[client_id]["message_counter"] += 1
        base = self.client_states[client_id]["message_id_base"]
        index = self.client_states[client_id]["message_counter"]
        return f"{base}_{index}"

    async def on_error(self, error_code: ErrorCode, client_id: str, **kwargs):
        """
        处理错误并发送错误信息给客户端
        Args:
            error_code (ErrorCode): 错误码枚举
            client_id (str): 客户端唯一标识
            **kwargs: 错误消息格式化参数
        Returns:
            None
        """
        # 更新错误统计
        from modules.monitoring import get_global_monitor
        monitor = get_global_monitor()
        if monitor:
            monitor.increment_error_count()

        if client_id in self.active_connection and self.active_connection[client_id]:
            index = self.client_states[client_id]["message_counter"]
            message_id = self.generate_message_id(client_id)

            # 根据配置决定是否包含详细错误信息
            if self.enable_detailed_errors:
                error_response = ErrorResponse.create_error_response(
                    error_code=error_code,
                    client_id=client_id,
                    index=index,
                    message_id=message_id,
                    **kwargs
                )
            else:
                # 简化错误信息，不包含详细信息
                simplified_kwargs = {k: v for k, v in kwargs.items() if k not in ['details']}
                error_response = ErrorResponse.create_error_response(
                    error_code=error_code,
                    client_id=client_id,
                    index=index,
                    message_id=message_id,
                    **simplified_kwargs
                )

            logger.warning(f"client_id:{client_id} - <<< [发送] 错误信息: {error_response}")
            await self.active_connection[client_id].send_json(error_response)
            logger.info(f"client_id: {client_id} - 关闭连接, 清理资源")
            await self.disconnect(client_id)



    async def on_result(self, result: str, client_id: str):
        """
        发送识别结果给客户端。
        Args:
            client_id (str): 客户端唯一标识
            result (str): 当前识别结果
        Returns:
            None
        """
        if self.active_connection[client_id]:
            index = self.client_states[client_id]["message_counter"]
            final = self.client_states[client_id]['is_final_result']
            # 需要拼接此前解码的结果(非当前语种)
            decoder_result_history = self.client_states[client_id]['decoder_result_history']
            if decoder_result_history: 
                result = decoder_result_history + ' ' + result
            response = {
                    "code": 200,
                    "state": "success",
                    "index": index,
                    "result": result,
                    "voice_id": client_id,
                    "message_id": self.generate_message_id(client_id),
                    "final": 1 if final else 0
            }

            # 添加语种识别结果（如果可用且已确认）
            if (self.client_states[client_id].get("lid_confirmed", False) and
                self.client_states[client_id].get("detected_language")):
                response["language_detected"] = self.client_states[client_id]["detected_language"]
                response["language_confidence"] = self.client_states[client_id].get("language_confidence", 0.0)

                # response 示例:
                # {
                #   'code': 200, 
                #   'state': 'success', 
                #   'index': 0, 
                #   'result': 'تۆۋەندىكىسىتۇرمۇشتابىرقىسىمكۆپئۇچرايدىغانجەمئىيەتتىكىيوشۇرۇن', 
                #   'voice_id': '333', 
                #   'message_id': 
                #   'client_id:333_21f0d0ff-2b35-442a-be42-5722348afa64_1', 
                #   'final': 0, 
                #   'language_detected': 'ug',    # 自动检测的语种, 如果客户请求消息中未设置有效的语种, 将启用自动语种检测
                #   'language_confidence': 0.65   # 自动检测的语种对应的置信度分数,分数0~1, 分数越高, 表示模型越确信当前语种正确性
                # }
            logger.debug(f"client_id:{client_id} - <<< [发送] 第{index}个数据包, 更新识别结果: \"{result}\"")
            if final:
                logger.info(f"client_id:{client_id} - <<< [响应] 最终识别结果: {result}")

            await self.active_connection[client_id].send_json(response)

    async def on_check(self, client_id: str, json_data: Dict[str, Any]) -> bool:
        """
        检查数据包合法性, 解析音频数据并获取音频特征。
        Args:
            client_id (str): 客户端唯一标识
            json_data (Dict[str, Any]): 接收到的 JSON 数据
        Returns:
            bool: 检查结果(True 表示合法, False 表示非法)
        注意： 
        不像wav等有文件头可以确认音频元信息, pcm数据包只能靠传输协议来确定sample_rate、width、channels这些元信息, 所以需要与客户端协商一致
        """
        if client_id not in self.active_connection:
            logger.error(f"客户已断开连接 \"{client_id}\"")
            raise KeyError(f"client disconnect: {client_id}")

        # 1. 检查必要字段
        # "index", 发送数据包的序号, 从0开始
        # "audio_data", Base64编码的音频数据, 数据长度需要满足固定要求
        # "sample_rate", 推荐16000
        # "is_final", True 代表最后一个数据包
        # "custom_separator", 可选的自定义分隔符参数
        # "language", 可选的自定义语种
        # "enable_lid", 可选的是否启用LID
        required_keys = ["index", "audio_data", "sample_rate", "is_final"]
        for key in required_keys:
            if key not in json_data:
                await self.on_error(ErrorCode.MISSING_REQUIRED_FIELD, client_id, field=key)
                return False

        # 处理可选参数（仅在第一个数据包中处理）
        if json_data["index"] == 0:
            # 处理自定义分隔符参数
            if "custom_separator" in json_data:
                self.client_states[client_id]["custom_separator"] = json_data["custom_separator"]
                logger.info(f"client_id:{client_id} - 设置自定义分隔符: \"{json_data['custom_separator']}\"")
            # 处理语种相关参数
            self._handle_language_options(client_id, json_data)

        # 是否为最后一个数据包
        is_final = json_data['is_final']
        if is_final:
            self.client_states[client_id]['is_final'] = True

        # 延迟初始化解码器, 等待LID结果确定语种后再初始化
        # 如果LID功能未启用或不可用, 则立即初始化解码器
        should_init_decoder = (
            self.client_states[client_id]["decoder"] is None and
            (not self.client_states[client_id]["lid_enabled"] or   # 如果未启用LID
             not self.lid_manager or
             not self.lid_manager.is_available() or
             self.client_states[client_id]["lid_confirmed"]    # 如果确认了语种
             )
        )

        if should_init_decoder:
            custom_separator = self.client_states[client_id]["custom_separator"]

            # 使用统一的解码器初始化方法
            self._init_decoder(client_id, custom_separator)


        # 2. 检查数据包索引, index 从0开始, 保证顺序到达
        index = json_data["index"]
        expected_index = self.client_states[client_id]['packet_index'] + 1
        if index != expected_index:
            await self.on_error(
                ErrorCode.INVALID_PACKET_INDEX,
                client_id,
                expected=expected_index,
                actual=index
            )
            return False
        # 更新索引状态
        self.client_states[client_id]['packet_index'] = index
        self.client_states[client_id]["packet_count"] += 1

        # 定期清理旧缓存
        if self.client_states[client_id]["packet_count"] % self.cache_cleanup_interval == 0:
            self._cleanup_old_cache(client_id)

        # 4. 检查数据包发送的实时率
        current_time = time.time()
        last_packet_time = self.client_states[client_id].get('last_packet_time', None)
        if last_packet_time is None:   # 第一个数据包
            self.client_states[client_id]['is_first'] = True
        else:
            self.client_states[client_id]['is_first'] = False
            if current_time - last_packet_time > self.packet_interval:
                current_time_fmt = format_time(current_time)
                last_time_fmt = format_time(last_packet_time)
                await self.on_error(
                    ErrorCode.PACKET_TIMEOUT,
                    client_id,
                    timeout=self.packet_interval,
                    last_time=last_time_fmt,
                    current_time=current_time_fmt
                )
                return False
        # 更新数据包到达时间
        self.client_states[client_id]['last_packet_time'] = current_time  

        # 5. 检查采样率一致性
        sample_rate = json_data['sample_rate']
        last_sample_rate = self.client_states[client_id].get('sample_rate', None)
        
        if last_sample_rate is not None and sample_rate != last_sample_rate:
            await self.on_error(
                ErrorCode.INVALID_SAMPLE_RATE,
                client_id,
                last_rate=last_sample_rate,
                current_rate=sample_rate
            )
            return False

        if sample_rate not in self.valid_sample_rate_list:
            await self.on_error(
                ErrorCode.INVALID_SAMPLE_RATE,
                client_id,
                supported_rates=self.valid_sample_rate_list
            )
            return False
    
        # 6.1 解码Base64编码的音频数据
        try:
            audio_data_base64 = json_data["audio_data"]
            pcm_bytes = base64.b64decode(audio_data_base64)
        except Exception as e:
            await self.on_error(
                ErrorCode.INVALID_AUDIO_FORMAT,
                client_id,
                details=f"Base64解码失败: {str(e)}"
            )
            return False
        # 6.2 检查pcm音频数据流大小
        if is_final:
            if len(pcm_bytes) > self.expected_data_size:
                await self.on_error(
                    ErrorCode.INVALID_DATA_SIZE,
                    client_id,
                    expected=self.expected_data_size,
                    actual=len(pcm_bytes)
                )
                return False
        else:
            if len(pcm_bytes) != self.expected_data_size:
                await self.on_error(
                    ErrorCode.INVALID_DATA_SIZE,
                    client_id,
                    expected=self.expected_data_size,
                    actual=len(pcm_bytes)
                )
                return False

        # 6.3 提取特征并处理LID逻辑
        try:
            waveform = self.to_waveform(pcm_bytes)  # torch.Tensor
            if is_final and waveform.size(0) < self.frame_samples * sample_rate:
                # 最后一个数据包达不到最小数据要求（小于一个帧长）则跳过
                return True

            # 6.3.1 静音检测和LID处理逻辑（LID未确认时会缓存waveform到self.client_states[client_id]["audio_buffer"]
            await self._process_silence_and_lid(client_id, waveform, sample_rate, is_final)

            # 6.3.2 只有在LID确认后\或LID未启用\或最后一个数据包 时才提取ASR特征
            if (not self.client_states[client_id]["lid_enabled"] or    # 如果未启用LID
                not self.lid_manager or
                not self.lid_manager.is_available() or
                self.client_states[client_id]["lid_confirmed"] or      # 确认了语种
                is_final):                                             # 最后一个数据包

                # 确保解码器已初始化
                if self.client_states[client_id]["decoder"] is None:
                    custom_separator = self.client_states[client_id]["custom_separator"]
                    self._init_decoder(client_id, custom_separator)
                    logger.debug(f"client_id:{client_id} - 延迟初始化解码器完成")

                # 获取相应的feat_pipe
                feat_pipe = self._get_feat_pipe(client_id)
                
                # 合并此前未确认LID的audio_buffer, 计算ASR特征
                self.client_states[client_id]["audio_buffer"].append(waveform)
                combined_waveform = torch.cat(self.client_states[client_id]["audio_buffer"], dim=0)
                feat = feat_pipe.feat_func(combined_waveform, sample_rate)   # torch.Tensor

                self.client_states[client_id]['feat_buffer'].append(feat)
                self.client_states[client_id]['feat_buffer_timestamps'].append(time.time())
                # 清空audio_buffer (仅缓存feat)
                self.client_states[client_id]["audio_buffer"].clear()  

                # 6.4 更新帧数
                cur_frames = feat.shape[0]
                self.client_states[client_id]['frame_nums'] += cur_frames
                logger.debug(f"client_id:{client_id} - >>> [解析] 第{index}个数据包, 累计帧数: {self.client_states[client_id]['frame_nums']}")
            else:
                logger.debug(f"client_id:{client_id} - >>> [等待LID] 第{index}个数据包, 等待语种识别确认")

            return True
        except Exception as e:
            # 使用统一的错误处理
            await self.on_error(ErrorCode.FEATURE_EXTRACTION_ERROR, client_id, details=f"Feature extraction error: {str(e)}")
            logger.error(e)
            return False

    def to_waveform(self, pcm_data: bytes) -> torch.Tensor:
        """
        将pcm数据转换为归一化的波形张量。
        Args:
            pcm_data (bytes): PCM格式的音频数据,字长为16位整数。
        Returns:
            torch.Tensor: 归一化后的波形张量,形状为(sample,)。
        Raises:
            ValueError: 如果PCM数据格式无效。
        """
        try:
            # 1. pcm字节数据转float32 numpy 数组
            audio_signal = np.frombuffer(pcm_data, np.int16).astype(np.float32)
        except Exception as e:
            raise ValueError("无效的PCM数据格式")

        # 2. 实际音频信号的峰值绝对振幅
        actual_peak = np.max(np.abs(audio_signal))
        # 3. 对音频进行峰值归一化
        epsilon = 1e-8
        if actual_peak > epsilon:
            # 放大音量,使其峰值达到1.0
            audio_signal_normalized = audio_signal / actual_peak
        else:
            # 静音的信号,保持为0
            audio_signal_normalized = audio_signal

        # 原来的归一化方法(未考虑到小音量):
        # max_val = np.iinfo(np.int16).max
        # audio_signal_normalized = (audio_signal.astype(np.float32) / max_val) # 归一化到 [-1, 1]

        waveform = torch.from_numpy(audio_signal_normalized).squeeze(0)  # (channel=1, sample) -> (sample,)
        return waveform

    async def _process_silence_and_lid(self, client_id: str, waveform: torch.Tensor, sample_rate: int, is_final: bool):
        """
        处理静音检测和语种识别逻辑, 支持多语种对话场景
        Args:
            client_id: 客户端ID
            waveform: 音频波形
            sample_rate: 采样率
            is_final: 是否为最后一个数据包
        """
        if not self.lid_manager or not self.lid_manager.is_available():
            return

        if not self.client_states[client_id]["lid_enabled"]:
            return

        # 检测当前帧是否包含语音
        current_has_speech = self.lid_manager.detect_speech(waveform, sample_rate)
        audio_duration = waveform.shape[0] / sample_rate

        if current_has_speech:
            # 检测到语音片段
            # 如果此前确认过语种, 且累积静音时长超过了静音阈值, 接收到了静音阈值后的第一个语音帧, 重新进行语种检测, 重置LID状态
            if (self.client_states[client_id]["lid_confirmed"] and
                self.client_states[client_id]["silence_time_sec"] >= self.lid_manager.silence_threshold):
                logger.info(f"client_id:{client_id} - 检测到静音后的新语音片段（静音时长：{self.client_states[client_id]['silence_time_sec']:.2f} s）, 重置LID状态")
                await self._reset_lid_state(client_id)
            else:
                # 之前未确认语种, 则累积有效话音时长, 等待LID
                # 或者, 未超过静音阈值就有新的语音片段 表明不需要切换语种
                self.client_states[client_id]["silence_time_sec"] = 0
                self.client_states[client_id]["audio_duration"] += audio_duration   # 连续有效话音时长
        else:
            # 检测到静音片段
            # 如果确认了语种, 表明可能要切换语种了, 累积静音时长, 连续有效话音时长(后续LID需要连续有效话音)
            if self.client_states[client_id]["lid_confirmed"]:
                self.client_states[client_id]["silence_time_sec"] += audio_duration
                self.client_states[client_id]["audio_duration"] = 0
                logger.debug(f"检测到静音: {self.client_states[client_id]['silence_time_sec']:.2f} s 静音阈值: {self.lid_manager.silence_threshold}")
            else:
                # 如果还未确认语种, 继续
                pass
            
        # 如果LID已确认, 不需要进行LID处理
        if self.client_states[client_id]["lid_confirmed"]:
            return

        # 缓存音频数据（只在LID未确认时缓存）
        if not self.client_states[client_id]["lid_confirmed"]:
            current_time = time.time()
            self.client_states[client_id]["audio_buffer"].append(waveform)
            self.client_states[client_id]["audio_buffer_timestamps"].append(current_time)

        # 检查是否需要进行LID
        should_perform_lid = False

        # 第一次检测：0.8秒后检查是否有有效语音
        if (self.client_states[client_id]["lid_attempts"] == 0 and
            self.client_states[client_id]["audio_duration"] >= self.lid_manager.detection_start):

            # 拼接所有音频
            combined_audio = torch.cat(self.client_states[client_id]["audio_buffer"], dim=0)

            # VAD检测
            has_speech = self.lid_manager.detect_speech(combined_audio, sample_rate)
            self.client_states[client_id]["vad_speech_detected"] = has_speech

            if has_speech:
                should_perform_lid = True
                logger.info(f"client_id:{client_id} - 第{self.client_states[client_id]['packet_index']}个数据包 检测到有效语音, 开始 LID")
            else:
                logger.debug(f"client_id:{client_id} - 未检测到有效语音, 继续等待")

        # 后续检测：每0.8秒进行一次, 最多6次（共4.4秒）
        elif (self.client_states[client_id]["vad_speech_detected"] and
              self.client_states[client_id]["lid_attempts"] < self.lid_manager.max_attempts and
              (self.client_states[client_id]["audio_duration"] >=
               self.lid_manager.detection_start + self.client_states[client_id]["lid_attempts"] * self.lid_manager.detection_interval)):
            should_perform_lid = True

        # 最后一个数据包时强制进行LID（如果还未确认）
        elif is_final and self.client_states[client_id]["vad_speech_detected"]:
            should_perform_lid = True

        if should_perform_lid:

            await self._perform_lid(client_id, sample_rate)

    async def _reset_lid_state(self, client_id: str):
        """
        重置LID状态, 用于新的语音片段
        Args:
            client_id: 客户端ID
        """
        logger.info(f"client_id:{client_id} - 重置 LID 状态, 开始新的语音片段识别")

        # 重置LID相关状态
        self.client_states[client_id]["detected_language"] = None
        self.client_states[client_id]["language_confidence"] = 0.0
        self.client_states[client_id]["audio_buffer"] = []
        self.client_states[client_id]["audio_buffer_timestamps"] = []
        self.client_states[client_id]["audio_duration"] = 0.0
        self.client_states[client_id]["lid_attempts"] = 0
        self.client_states[client_id]["lid_confirmed"] = False
        self.client_states[client_id]["vad_speech_detected"] = False

        # 重置解码器, 因为可能需要切换到新的语种
        if self.client_states[client_id]["decoder"] is not None:
            logger.info(f"client_id:{client_id} - 重置解码器以准备语种切换")
            self.client_states[client_id]["decoder"] = None

        # 特征缓存重置
        self.client_states[client_id]['feat_buffer'] = []
        self.client_states[client_id]["feat_buffer_timestamps"] = []
        self.client_states[client_id]["decoder_result_history"] += self.client_states[client_id]["decoder_result"]


    async def _perform_lid(self, client_id: str, sample_rate: int):
        """
        执行语种识别
        Args:
            client_id: 客户端ID
            sample_rate: 采样率
        """
        try:
            start_time = time.time()
            # 拼接所有音频
            if len(self.client_states[client_id]["audio_buffer"]) == 0:
                raise ValueError(F"audio_buffer EMPTY!")

            combined_audio = torch.cat(self.client_states[client_id]["audio_buffer"], dim=0)

            # 执行LID
            lid_result = self.lid_manager.predict_language(combined_audio, sample_rate)

            self.client_states[client_id]["lid_attempts"] += 1
            current_language = lid_result.get("predict", "unknown")
            current_confidence = lid_result.get("confidence", 0.0)

            logger.info(f"client_id:{client_id} - LID尝试 {self.client_states[client_id]['lid_attempts']}: "
                       f"语种={current_language}, {lid_result}")

            # 更新检测结果
            self.client_states[client_id]["detected_language"] = current_language
            self.client_states[client_id]["language_confidence"] = current_confidence

            should_confirm = False
            # 至少识别2次,且高于置信度阈值(0.7)
            if self.client_states[client_id]["lid_attempts"] >= 2 and current_confidence >= self.lid_manager.confidence_threshold:
                should_confirm = True
            # 至少识别4次, 分数高于0.5即可确认
            elif self.client_states[client_id]["lid_attempts"] >= 4 and current_confidence >= 0.5:
                should_confirm = True
            # 最后一个数据包, 无论分数如何都应该确认
            elif self.client_states[client_id]["is_final"]:
                should_confirm = True
            # 超过最大尝试次数, 无论分数如何都应该确认
            elif self.client_states[client_id]["lid_attempts"] >= self.lid_manager.max_attempts:
                should_confirm = True
            # 超过最大LID识别长度, 无论分数如何都应该确认
            elif self.client_states[client_id]["audio_duration"] >= self.lid_manager.detection_end:
                should_confirm = True

            if should_confirm:
                self.client_states[client_id]["lid_confirmed"] = True
                self.client_states[client_id]["lid_attempts"] = self.lid_manager.max_attempts  # 避免确认之后再次尝试
                logger.info(f"client_id:{client_id} - LID 结果确认: 语种={current_language}, 置信度={current_confidence}")

                # 根据识别的语种切换ASR模型（多语种模式）
                if self.asr_manager and current_language != "unknown":
                    success = await self._switch_asr_model(client_id, current_language)
                    if not success:
                        logger.warning(f"client_id:{client_id} - 切换到语种 {current_language} 失败, 使用默认模型")
                        # 使用默认语种
                        default_lang = self.args.default_language
                        await self._switch_asr_model(client_id, default_lang)

                # 确认LID之后, 取出最后一个音频缓存, 因为后面紧接着计算特征, 会统一缓存一次
                self.client_states[client_id]["audio_buffer"].pop()
            
            logger.debug(f"LID 耗时: {time.time()-start_time:.4f} s")

        except Exception as e:
            logger.error(f"client_id:{client_id} - LID执行失败: {e}")

            # 更新错误统计
            from modules.monitoring import get_global_monitor
            monitor = get_global_monitor()
            if monitor:
                monitor.increment_error_count()

            # LID失败时, 确认为未知语种, 继续使用默认ASR
            self.client_states[client_id]["detected_language"] = "unknown"
            self.client_states[client_id]["lid_confirmed"] = True

    async def _switch_asr_model(self, client_id: str, target_language: str) -> bool:
        """
        切换到指定语种的ASR模型

        Args:
            client_id: 客户端ID
            target_language: 目标语种代码

        Returns:
            bool: 是否切换成功
        """
        try:
            if not self.asr_manager:
                logger.warning(f"client_id:{client_id} - ASR管理器未初始化, 无法切换模型")
                return False

            # 切换全局ONNX模型会话
            if not self.asr_manager.switch_to_language(target_language):
                logger.error(f"client_id:{client_id} - 切换到语种 {target_language} 失败")
                return False

            # 获取目标语种的配置和词表
            target_lang_config = self.asr_manager.get_lang_config(target_language)
            target_symbol_table = self.asr_manager.get_symbol_table(target_language)
            target_config = self.asr_manager.get_config(target_language)

            if not target_lang_config or not target_symbol_table or not target_config:
                logger.error(f"client_id:{client_id} - 获取语种 {target_language} 的配置失败")
                return False

            # 更新客户端状态中的语种信息
            self.client_states[client_id]["current_language"] = target_language
            self.client_states[client_id]["current_symbol_table"] = target_symbol_table
            self.client_states[client_id]["current_config"] = target_config
            self.client_states[client_id]["current_lang_config"] = target_lang_config

            # 重新创建解码器以使用新的语种配置
            if self.client_states[client_id]["decoder"] is not None:
                if self.client_states[client_id]["decoder"].lang_code == target_language:
                    logger.debug(f"client_id:{client_id} - 已有当前语种的解码器, 跳过切换")
                else:
                    logger.debug(f"client_id:{client_id} - 重新创建解码器以使用新语种配置")
                    del self.client_states[client_id]["decoder"]

                    # 重新创建解码器
                    self.client_states[client_id]["decoder"] = ASRDecoder(
                        target_lang_config, target_config, target_symbol_table,
                        None, target_language  # 传递新的语种代码
                    )

                    logger.info(f"client_id:{client_id} - 切换到语种: {target_language}")
            return True

        except Exception as e:
            logger.error(f"client_id:{client_id} - 切换ASR模型到 {target_language} 时发生异常: {e}")

            # 更新错误统计
            from modules.monitoring import get_global_monitor
            monitor = get_global_monitor()
            if monitor:
                monitor.increment_error_count()

            return False

    async def on_decode(self, client_id: str) -> Tuple[bool, Optional[str]]:
        """
        当帧数满足解码所需的最小帧数时, 开始逐 chunk 解码。
        Args:
            client_id (str): 客户端唯一标识
        Returns:
            Tuple[bool, Optional[str]]: 是否成功解码(True/False), 解码结果或空字符串
        """
        try:
            # 检查是否需要等待LID确认
            if (self.client_states[client_id]["lid_enabled"] and
                self.lid_manager and
                self.lid_manager.is_available() and
                not self.client_states[client_id]["lid_confirmed"]):

                # 如果是最后一个数据包但LID还未确认, 强制确认
                if self.client_states[client_id]['is_final']:
                    self.client_states[client_id]["lid_confirmed"] = True
                    if not self.client_states[client_id]["detected_language"]:
                        self.client_states[client_id]["detected_language"] = "unknown"
                    logger.info(f"client_id:{client_id} - 最后数据包, 强制确认LID结果")
                else:
                    # 等待LID确认
                    await asyncio.sleep(0.04)
                    return (True, "")

            while True:
                frame_nums = self.client_states[client_id]['frame_nums']

                if frame_nums >= self.required_decoding_window:
                    logger.debug(f"client_id:{client_id} - 所需帧数: {self.required_decoding_window}, 目前帧数: {frame_nums}, 开始解码")

                    # 拼接特征并添加 batch 维度
                    accum_feats = torch.cat(self.client_states[client_id]['feat_buffer'], dim=0).unsqueeze(0)

                    is_final = self.client_states[client_id]['is_final']
                    asr_model = self.client_states[client_id]['decoder']
                    
                    # 当前语种的累积的解码结果存储在 asr_model.result 中
                    if self.async_decode:
                        # 异步调用解码, 多个同语种请求等待解码时, 根据请求数量来动态扩容会话池, 扩容会耗时, 适合多路并发持续连接场景
                        loop = asyncio.get_event_loop()
                        await loop.run_in_executor(None, asr_model.decode, accum_feats, client_id, is_final)
                    else: 
                        # 同步调用解码, 多个同语种请求线性排队等待解码, 会话开启并发但会话池不扩容, 适合短语音场景, 无扩容开销但会增加排队时间
                        asr_model.decode(accum_feats, client_id, is_final)
                    
                    # 决定是否发送解码结果
                    last_result = self.client_states[client_id]['decoder_result']
                    cur_result = asr_model.result
                    if is_final:
                        logger.info(f"client_id:{client_id} - *** 最后一个数据包完成解码 ***")
                        self.client_states[client_id]['is_final_result'] = True
                        return (True, cur_result)    # 最后一个数据包, 无论识别结果变不变, 都要发送响应数据包

                    if cur_result != last_result:
                        self.client_states[client_id]['decoder_result'] = cur_result
                        return (True, cur_result)
                    else:
                        logger.debug(f"client_id:{client_id} - chunk 完成解码, 识别结果不变")
                        return (True, "")

                else:
                    logger.debug(f"client_id:{client_id} - 所需帧数: {self.required_decoding_window}, 目前帧数: {frame_nums}, 继续等待数据包")
                    await asyncio.sleep(0.01)   # 低频轮询避免忙等待 (10ms 间隔)
                    return (True, "")

        except Exception as e:
            # 使用统一的错误处理
            await self.on_error(ErrorCode.DECODE_ERROR, client_id, details=f"Decode error: {str(e)}")
            logger.error(e)
            return (False, None)
